<?php
namespace Api\Controllers;

use App\Core\Controller;
use Api\Core\ApiHandler;
use Api\Services\FirebaseService;
use App\Services\UserService;
use App\Services\EmailService;

class GameUserController extends Controller {
    use ApiHandler;

    private $firebaseService;
    private $request;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        // Lazy loading - chỉ khởi tạo khi cần
    }

    protected function getUserService(): UserService
    {
        return UserService::getInstance($this->db);
    }

    /**
     * Get FirebaseService instance (lazy loading)
     */
    private function getFirebaseService(): FirebaseService
    {
        if ($this->firebaseService === null) {
            $this->firebaseService = new FirebaseService();
        }
        return $this->firebaseService;
    }

    /**
     * Format thông tin user cho response
     */
    private function formatUserResponse($user) {
        return [
            'user_id' => $user['user_id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'is_email_verified' => (bool)$user['is_email_verified'],
            'phone' => $user['phone'],
            'is_phone_verified' => (bool)$user['is_phone_verified'],
            'user_type' => $user['user_type'],
            'balance' => (float)($user['balance'] ?? 0)
        ];
    }

    public function register() {
        return $this->apiEndpoint('register', function($validator) {

            // Đã được validate tự động từ config, chỉ cần xử lý logic business
            $registerType = $validator->input('register_type');
            $source = $validator->input('source');
            $userService = $this->getUserService();
            $date = date('Y-m-d H:i:s');

            // Prepare register_info
            $registerInfo = [
                'ip' => $validator->getIp(),
                'device_id' => $validator->input('device_id'),
                'device_info' => $validator->input('device_info')
            ];

            switch ($registerType) {
                case 'account':
                    // Validate thêm cho account registration (ngoài config)
                    $additionalData = $this->validateRequestSafe([
                        'username' => 'required|username|min:6|max:20',
                        'password' => 'required|password|min:6|max:30',
                        'email' => 'email',
                        'phone' => 'phone'
                    ]);

                    // Merge với validated data
                    $accountData = array_merge($validator->all(), $additionalData);

                    // Kiểm tra username đã tồn tại chưa
                    $existingUser = $userService->getByUsername($accountData['username']);
                    if ($existingUser) {
                        throw new \Exception('Tên tài khoản đã tồn tại');
                    }

                    // Kiểm tra email đã tồn tại chưa
                    if (!empty($accountData['email'])) {
                        $existingEmail = $userService->getByEmail($accountData['email']);
                        if ($existingEmail) {
                            throw new \Exception('Email đã tồn tại');
                        }
                    }

                    // Kiểm tra phone đã tồn tại chưa
                    if (!empty($accountData['phone'])) {
                        $existingPhone = $userService->getByPhone($accountData['phone']);
                        if ($existingPhone) {
                            throw new \Exception('Số điện thoại đã tồn tại');
                        }
                    }

                    $userData = [
                        'user_id' => 'account_' . uniqid(),
                        'username' => $accountData['username'],
                        'password' => password_hash($accountData['password'], PASSWORD_DEFAULT),
                        'email' => $accountData['email'] ?? null,
                        'phone' => $accountData['phone'] ?? null,
                        'user_type' => 'account',
                        'register_source' => $source,
                        'register_info' => json_encode($registerInfo)
                    ];

                    // Tạo user mới và refresh cache
                    $id = $userService->create($userData);
                    $user = $userService->getById($id);
                    break;

                case 'guest':
                    // Validate thêm cho guest registration
                    $guestData = $this->validateRequestSafe([
                        'device_id' => 'required'
                    ]);

                    // Biến để track xem là đăng ký mới hay login
                    $isNewRegistration = false;

                    // Kiểm tra device_id đã tồn tại chưa
                    $existingGuestUser = $userService->getByDeviceId($guestData['device_id']);

                    // Nếu tìm thấy user, cập nhật last_login
                    if ($existingGuestUser) {
                        $user = $existingGuestUser;
                        // Cập nhật last_login và refresh cache
                        $userService->update($existingGuestUser['id'], [
                            'last_login' => $date
                        ]);
                        $user['last_login'] = $date;
                    } else {
                        $isNewRegistration = true;
                        // Kiểm tra số lượng tài khoản guest từ IP hiện tại
                        $userIp = $validator->getIp();
                        $guestCount = $userService->countGuestsByIp($userIp);

                        if ($guestCount >= 5) {
                            throw new \Exception('IP này đã tạo quá nhiều tài khoản guest. Giới hạn 5 tài khoản/IP');
                        }

                        $guestId = 'guest_' . uniqid();
                        $userData = [
                            'user_id' => $guestId,
                            'username' => 'Guest_' . substr($guestId, -6),
                            'user_type' => 'guest',
                            'register_source' => $source,
                            'register_info' => json_encode([
                                'ip' => $validator->getIp(),
                                'device_id' => $guestData['device_id'],
                                'device_info' => $validator->input('device_info')
                            ])
                        ];

                        // Tạo user mới và refresh cache
                        $id = $userService->create($userData);
                        $user = $userService->getById($id);
                    }
                    break;

                default:
                    throw new \Exception('Loại đăng ký không hợp lệ');
            }

            // Lấy cấu hình token từ config
            $tokenSettings = $this->config['api']['authentication']['token_settings'] ?? [];

            // Nếu là guest user thì token có hạn theo guest_expire, còn lại theo default_expire
            $expiresIn = ($registerType === 'guest')
                ? ($tokenSettings['guest_expire'] ?? '+1 month')
                : ($tokenSettings['default_expire'] ?? '+7 days');

            $tokenData = $this->generateAccessToken($user['user_id'], [
                'ip' => $validator->getIp(),
                'user_agent' => $validator->getUserAgent()
            ], $expiresIn);

            $this->successResponse([
                'user' => $this->formatUserResponse($user),
                'token' => $tokenData
            ], ($registerType === 'guest' && !$isNewRegistration) ? 'Login successful' : 'Registration successful');
        });
    }

    public function login() {
        return $this->apiEndpoint('login', function($validator) {
            $user = null;
            $userService = $this->getUserService();

            switch ($validator->input('login_type')) {
                case 'google':
                case 'facebook':
                case 'apple':
                    $oauthData = $this->validateRequestSafe([
                        'id_token' => 'required'
                    ]);

                    // Xác thực token với Firebase
                    $verifiedToken = $this->getFirebaseService()->verifyIdToken($oauthData['id_token']);

                    // Tạo user_id theo format: login_type_uid
                    $userId = $validator->input('login_type') . '_' . $verifiedToken['uid'];

                    // Kiểm tra user đã tồn tại chưa
                    $user = $userService->getByUserId($userId);

                    if (!$user) {
                        // Tạo user mới nếu chưa tồn tại
                        $userData = [
                            'user_id' => $userId,
                            'username' => $validator->input('login_type') . '_' . explode('@', $verifiedToken['email'])[0],
                            'email' => $verifiedToken['email'],
                            'is_email_verified' => 1,
                            'user_type' => $validator->input('login_type'),
                            'oauth_id' => $verifiedToken['uid'],
                            'register_source' => $validator->input('source', 'web'),
                            'register_info' => json_encode([
                                'ip' => $validator->getIp(),
                                'device_info' => $validator->input('device_info')
                            ]),
                            'last_login' => date('Y-m-d H:i:s')
                        ];

                        $id = $userService->create($userData);
                        $user = $this->getUserService()->getById($id);// tạo lại cache
                    }
                    break;

                case 'account':
                    $accountData = $this->validateRequestSafe([
                        'username' => 'required',
                        'password' => 'required'
                    ]);

                    $user = $userService->getByUsername($accountData['username']);

                    if (!$user || $user['user_type'] !== 'account' || !password_verify($accountData['password'], $user['password'])) {
                        throw new \Exception('Tên tài khoản hoặc mật khẩu không hợp lệ');
                    }
                    break;

                case 'guest':
                    $guestData = $this->validateRequestSafe([
                        'user_id' => 'required'
                    ]);

                    $user = $userService->getByUserId($guestData['user_id']);

                    if (!$user || $user['user_type'] !== 'guest') {
                        throw new \Exception('Tài khoản guest không tồn tại');
                    }
                    break;

                default:
                    throw new \Exception('Loại đăng nhập không hợp lệ');
            }

            if (!$user) {
                throw new \Exception('Tài khoản không tồn tại');
            }

            $this->checkAccountStatus($user);

            // Kiểm tra số lượng token đã tạo trong ngày
            $tokenCount = $this->db->fetch(
                "SELECT COUNT(*) as count FROM access_tokens
                 WHERE user_id = ?
                 AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)",
                [$user['user_id']]
            );

            if ($tokenCount['count'] >= 120) {
                throw new \Exception('Bạn đã đăng nhập quá nhiều lần. Vui lòng thử lại sau 24 giờ');
            }

            // Update last_login cho tất cả các loại login
            $userService->update($user['id'], [
                'last_login' => date('Y-m-d H:i:s')
            ]);
            $user['last_login'] = date('Y-m-d H:i:s');

            // Lấy cấu hình token từ config
            $tokenSettings = $this->config['api']['authentication']['token_settings'] ?? [];

            // Guest user sử dụng guest_expire, các loại khác sử dụng default_expire
            $expiresIn = $tokenSettings['default_expire'] ?? '+7 days';

            // Generate new access token
            $tokenData = $this->generateAccessToken($user['user_id'], [
                'ip' => $validator->getIp(),
                'user_agent' => $validator->getUserAgent()
            ], $expiresIn);

            $this->successResponse([
                'user' => $this->formatUserResponse($user),
                'token' => $tokenData
            ], 'Login successful');
        });
    }

    public function convertGuestAccount() {
        return $this->apiEndpoint('convert_guest_account', function($validator) {

            $user = $this->getAuthenticatedUser();

            // Kiểm tra xem token có thuộc về guest user không
            if ($user['user_id'] !== $validator->input('guest_user_id')) {
                throw new \Exception('Token không hợp lệ cho tài khoản guest này');
            }

            // Kiểm tra tài khoản guest có hợp lệ không
            if ($user['user_type'] !== 'guest') {
                throw new \Exception('Chỉ tài khoản guest mới được phép chuyển đổi');
            }

            // Kiểm tra username đã tồn tại chưa
            $existingUsername = $this->getUserService()->getByUsername($validator->input('username'));
            if ($existingUsername && $existingUsername['user_type'] === 'account') {
                throw new \Exception('Tên tài khoản đã tồn tại');
            }

            // Kiểm tra email đã tồn tại chưa
            if (!empty($validator->input('email'))) {
                $existingEmail = $this->getUserService()->getByEmail($validator->input('email'));
                if ($existingEmail) {
                    throw new \Exception('Email đã tồn tại');
                }
            }

            // Kiểm tra phone đã tồn tại chưa
            if (!empty($validator->input('phone'))) {
                $existingPhone = $this->getUserService()->getByPhone($validator->input('phone'));
                if ($existingPhone) {
                    throw new \Exception('Số điện thoại đã tồn tại');
                }
            }

            // Cập nhật thông tin tài khoản
            $this->getUserService()->update($user['id'], [
                'user_type' => 'account',
                'username' => $validator->input('username'),
                'password' => password_hash($validator->input('password'), PASSWORD_DEFAULT),
                'email' => $validator->input('email') ?? null,
                'phone' => $validator->input('phone') ?? null
            ]);

            // Generate new access token
            $tokenData = $this->generateAccessToken($user['user_id'], [
                'ip' => $validator->getIp(),
                'user_agent' => $validator->getUserAgent()
            ]);

            // Lấy thông tin user đã cập nhật
            $updatedUser = $this->getUserService()->getById($user['id']);

            $this->successResponse([
                'user' => $this->formatUserResponse($updatedUser),
                'token' => $tokenData
            ], 'Account converted successfully');
        });
    }

    public function verifyToken() {
        return $this->apiEndpoint('verify_token', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();
            $this->checkAccountStatus($user);

            $tokenData = $this->getAuthTokenData();

            $this->successResponse([
                'user' => $this->formatUserResponse($user),
                'token' => [
                    'expires_at' => $tokenData['expires_at']
                ]
            ], 'Token hợp lệ');
        });
    }

    public function forgotPassword() {
        return $this->apiEndpoint('forgot_password', function($validator) {
            // Kiểm tra email có tồn tại không
            $user = $this->getUserService()->getByEmail($validator->input('email'));

            if (!$user || $user['user_type'] !== 'account' || empty($user['email'])) {
                throw new \Exception('Email không tồn tại trong hệ thống');
            }
            if ((int)$user['is_email_verified'] !== 1) {
                throw new \Exception('Email chưa được kích hoạt. Vui lòng kích hoạt email trước khi yêu cầu khôi phục mật khẩu.');
            }
            $this->checkAccountStatus($user);

            // Generate reset token
            $resetToken = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));

            // Save reset token
            $this->db->insert('password_resets', [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'token' => $resetToken,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // Send password reset email
            try {
                $emailService = EmailService::getInstance();
                $emailSent = $emailService->sendPasswordReset(
                    $user['email'],
                    $resetToken,
                    $user['username'] ?? ''
                );

                if (!$emailSent) {
                    throw new \Exception('Không thể gửi email khôi phục mật khẩu. Vui lòng thử lại sau.');
                }
            } catch (\Exception $e) {
                // Log error but don't fail the request
                error_log("Password reset email sending failed: " . $e->getMessage());
                throw new \Exception('Không thể gửi email khôi phục mật khẩu. Vui lòng kiểm tra địa chỉ email và thử lại.');
            }

            $this->successResponse([
                'expires_at' => $expiresAt
            ], 'Email khôi phục mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.');
        });
    }

    /**
     * Xác thực token và đặt lại mật khẩu
     */
    public function resetPassword() {
        return $this->apiEndpoint('reset_password', function($validator) {
            // Get reset token record
            $resetRecord = $this->db->fetch(
                "SELECT pr.*, gu.id as user_id, gu.username, gu.email
                FROM password_resets pr
                JOIN game_users gu ON pr.user_id = gu.id
                WHERE pr.token = ? AND pr.expires_at > NOW() AND pr.used = 0
                ORDER BY pr.created_at DESC LIMIT 1",
                [$validator->input('token')]
            );

            if (!$resetRecord) {
                throw new \Exception('Token khôi phục mật khẩu không hợp lệ hoặc đã hết hạn');
            }

            // Validate confirm password
            if ($validator->input('new_password') !== $validator->input('confirm_password')) {
                throw new \Exception('Mật khẩu xác nhận không khớp');
            }

            // Update user password
            $this->getUserService()->update($resetRecord['user_id'], [
                'password' => password_hash($validator->input('new_password'), PASSWORD_DEFAULT)
            ]);

            // Mark reset token as used
            $this->db->update(
                'password_resets',
                ['used' => 1, 'used_at' => date('Y-m-d H:i:s')],
                'id = ?',
                [$resetRecord['id']]
            );

            // Invalidate all tokens for this user (force re-login)
            $this->getUserService()->invalidateUserTokens($resetRecord['user_id']);

            $this->successResponse(null, 'Mật khẩu đã được đặt lại thành công. Vui lòng đăng nhập lại với mật khẩu mới.');
        });
    }

    public function sendPhoneVerification() {
        return $this->apiEndpoint('send_phone_verification', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();

            // Kiểm tra tài khoản đã xác thực số điện thoại chưa
            if ($user['is_phone_verified'] == 1) {
                throw new \Exception('Tài khoản đã được xác thực số điện thoại');
            }

            // Check if phone already exists and verified by other account
            $existingPhone = $this->db->fetch(
                "SELECT * FROM game_users WHERE phone = ? AND is_phone_verified = 1 AND id != ?",
                [$validator->input('phone'), $user['id']]
            );
            if ($existingPhone) {
                throw new \Exception('Số điện thoại đã được sử dụng');
            }

            // Generate OTP (in real case, this should be random)
            $otp = '11111';
            $expiresAt = date('Y-m-d H:i:s', strtotime('+5 minutes'));

            // Save OTP to database
            $this->db->insert('phone_verifications', [
                'user_id' => $user['id'],
                'phone' => $validator->input('phone'),
                'otp' => $otp,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // Integrate with real SMS service here
            // For now, just return success

            $this->successResponse([
                'expires_at' => $expiresAt,
                'wait_time' => 60 // Thời gian chờ giữa 2 lần gửi OTP (giây)
            ], 'Mã xác thực đã được gửi');
        });
    }

    public function verifyPhoneOTP() {
        return $this->apiEndpoint('verify_phone_otp', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();

            // Get verification record
            $verification = $this->db->fetch(
                "SELECT * FROM phone_verifications
                WHERE user_id = ? AND phone = ? AND otp = ? AND expires_at > NOW()
                ORDER BY created_at DESC LIMIT 1",
                [$user['id'], $validator->input('phone'), $validator->input('otp')]
            );

            if (!$verification) {
                throw new \Exception('Mã OTP không hợp lệ hoặc đã hết hạn');
            }

            // Update user's phone and verification status
            $this->getUserService()->update($user['id'], [
                'phone' => $validator->input('phone'),
                'is_phone_verified' => 1
            ]);

            // Delete used verification code
            $this->db->delete(
                'phone_verifications',
                'id = ?',
                [$verification['id']]
            );

            // Get updated user info
            $updatedUser = $this->getUserService()->getById($user['id']);

            $this->successResponse([
                'user' => $this->formatUserResponse($updatedUser),
                'user_id' => $user['user_id'],
                'phone' => $validator->input('phone'),
                'is_phone_verified' => true
            ], 'Xác thực số điện thoại thành công');
        });
    }

    /**
     * Gửi mã xác thực email
     */
    public function sendEmailVerification() {
        return $this->apiEndpoint('send_email_verification', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();

            // Kiểm tra tài khoản đã xác thực email chưa
            if ($user['is_email_verified'] == 1) {
                throw new \Exception('Email đã được xác thực');
            }

            // Check if email already exists and verified by other account
            $existingEmail = $this->db->fetch(
                "SELECT * FROM game_users WHERE email = ? AND is_email_verified = ? AND id != ?",
                [$validator->input('email'), 1, $user['id']]
            );
            if ($existingEmail) {
                throw new \Exception('Email đã được sử dụng bởi tài khoản khác');
            }

            // Generate verification code
            $verificationCode = sprintf("%06d", mt_rand(0, 999999));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+5 minutes'));

            // Save verification code
            $this->db->insert('email_verifications', [
                'user_id' => $user['id'],
                'email' => $validator->input('email'),
                'verification_code' => $verificationCode,
                'expires_at' => $expiresAt,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // Send email with verification code
            try {
                $emailService = EmailService::getInstance();
                $emailSent = $emailService->sendEmailVerification(
                    $validator->input('email'),
                    $verificationCode,
                    $user['username'] ?? ''
                );

                if (!$emailSent) {
                    throw new \Exception('Không thể gửi email xác thực. Vui lòng thử lại sau.');
                }
            } catch (\Exception $e) {
                // Log error but don't fail the request
                error_log("Email sending failed: " . $e->getMessage());
                throw new \Exception('Không thể gửi email xác thực. Vui lòng kiểm tra địa chỉ email và thử lại.');
            }

            $this->successResponse([
                'expires_at' => $expiresAt,
                'wait_time' => 60 // Thời gian chờ giữa 2 lần gửi (giây)
            ], 'Mã xác thực đã được gửi đến email của bạn');
        });
    }

    /**
     * Xác thực email với mã xác thực
     */
    public function verifyEmail() {
        return $this->apiEndpoint('verify_email', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();

            // Get verification record
            $verification = $this->db->fetch(
                "SELECT * FROM email_verifications
                WHERE user_id = ? AND email = ? AND verification_code = ? AND expires_at > NOW()
                ORDER BY created_at DESC LIMIT 1",
                [$user['id'], $validator->input('email'), $validator->input('verification_code')]
            );

            if (!$verification) {
                throw new \Exception('Mã xác thực không hợp lệ hoặc đã hết hạn');
            }

            // Update user's email and verification status
            $this->getUserService()->update($user['id'], [
                'email' => $validator->input('email'),
                'is_email_verified' => 1
            ]);

            // Delete used verification code
            $this->db->delete(
                'email_verifications',
                'id = ?',
                [$verification['id']]
            );

            // Get updated user info
            $updatedUser = $this->getUserService()->getById($user['id']);

            $this->successResponse([
                'user' => $this->formatUserResponse($updatedUser)
            ], 'Xác thực email thành công');
        });
    }

    /**
     * Đổi mật khẩu
     */
    public function changePassword() {
        return $this->apiEndpoint('change_password', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();

            // Validate confirm password
            if ($validator->input('new_password') !== $validator->input('confirm_password')) {
                throw new \Exception('Mật khẩu xác nhận không khớp');
            }

            // Kiểm tra user type
            if ($user['user_type'] !== 'account') {
                throw new \Exception('Chỉ tài khoản thường mới có thể đổi mật khẩu');
            }

            // Verify current password
            if (!password_verify($validator->input('current_password'), $user['password'])) {
                throw new \Exception('Mật khẩu hiện tại không đúng');
            }

            // Check if new password is same as current
            if ($validator->input('current_password') === $validator->input('new_password')) {
                throw new \Exception('Mật khẩu mới không được trùng với mật khẩu hiện tại');
            }

            // Update password
            $this->getUserService()->update($user['id'], [
                'password' => password_hash($validator->input('new_password'), PASSWORD_DEFAULT),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // Invalidate all tokens for this user (force re-login)
            $this->getUserService()->invalidateUserTokens($user['user_id']);

            // Get updated user info
            $updatedUser = $this->getUserService()->getById($user['id']);


            $this->successResponse([
                'user' => $this->formatUserResponse($updatedUser)
            ], 'Đổi mật khẩu thành công. Vui lòng đăng nhập lại.');
        });
    }

    /**
     * Xóa tài khoản
     */
    public function deleteAccount() {
        return $this->apiEndpoint('delete_account', function($validator) {
            // Authentication đã được xử lý tự động
            $user = $this->getAuthenticatedUser();

            // Cập nhật trạng thái tài khoản thành đã xóa
            $this->getUserService()->update($user['id'], [
                'status' => 2 // Trạng thái đã xóa
            ]);

            // Vô hiệu hóa tất cả token của user
            $this->getUserService()->invalidateUserTokens($user['user_id']);

            $this->successResponse(null, 'Tài khoản đã được xóa thành công');
        });
    }

    // Cập nhật kiểm tra status trong các phương thức khác
    private function checkAccountStatus(array $user): void {
        if ($user['status'] == 0) {
            throw new \Exception('Tài khoản đã bị khóa');
        }
        if ($user['status'] == 2) {
            throw new \Exception('Tài khoản đã bị xóa');
        }
    }
}