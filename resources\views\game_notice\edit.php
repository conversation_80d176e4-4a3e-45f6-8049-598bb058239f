<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-2xl font-bold text-gray-900">
                        Chỉnh sửa thông báo
                    </h2>
                    <a href="?route=gamenotice" class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200">
                        <i class="fas fa-arrow-left mr-2"></i>Quay lại
                    </a>
                </div>
            </div>

            <!-- Form -->
            <form id="noticeForm" class="p-6">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="id" value="<?php echo $notice['idpublicnotice']; ?>">

                <!-- Server Selection -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Chọn máy chủ <span class="text-red-600">*</span>
                    </label>
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                        <?php foreach ($servers as $server): ?>
                            <?php if (!$server['merged_into']): ?>
                                <label class="flex items-center p-3 border rounded-lg hover:bg-gray-50">
                                    <input type="checkbox" name="server_ids[]" value="<?php echo $server['id']; ?>" 
                                           class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                           <?php echo $server['id'] == $_GET['server_id'] ? 'checked' : ''; ?>>
                                    <span class="ml-3 text-gray-900"><?php echo htmlspecialchars($server['name']); ?></span>
                                </label>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                </div>

                <!-- Notice Type -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Loại thông báo <span class="text-red-600">*</span>
                    </label>
                    <select name="notice_type" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                        <option value="1" <?php echo $notice['type'] == 1 ? 'selected' : ''; ?>>Thông thường</option>
                        <option value="2" <?php echo $notice['type'] == 2 ? 'selected' : ''; ?>>Có link</option>
                    </select>
                </div>

                <!-- Content -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Nội dung <span class="text-red-600">*</span>
                    </label>
                    <textarea name="content" rows="4" 
                              class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                              placeholder="Nhập nội dung thông báo"><?php echo htmlspecialchars($notice['content']); ?></textarea>
                </div>

                <!-- Link (for type 2) -->
                <div id="linkField" class="mb-6 <?php echo $notice['type'] != 2 ? 'hidden' : ''; ?>">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Link
                    </label>
                    <input type="text" name="link" 
                           class="shadow-sm focus:ring-blue-500 focus:border-blue-500 mt-1 block w-full sm:text-sm border-gray-300 rounded-md"
                           placeholder="https://..."
                           value="<?php echo htmlspecialchars($notice['link'] ?? ''); ?>">
                </div>

                <!-- Color -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Màu chữ
                    </label>
                    <div class="flex items-center space-x-4">
                        <select name="color" id="colorSelect" class="mt-1 block w-48 pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <?php foreach ($colorOptions as $key => $opt): ?>
                                <option value="<?php echo $key; ?>" data-color="<?php echo $opt['value']; ?>" <?php echo ($notice['color'] == $key) ? 'selected' : ''; ?>>
                                    <?php echo $opt['label']; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <span id="colorPreview" class="inline-block w-10 h-10 rounded border border-gray-300" style="background: <?php echo $colorOptions[$notice['color']]['value'] ?? $colorOptions[0]['value']; ?>;"></span>
                    </div>
                </div>

                <!-- Time Settings -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Thời gian bắt đầu <span class="text-red-600">*</span>
                        </label>
                        <input type="datetime-local" name="begin_send_time" 
                               value="<?php echo date('Y-m-d\TH:i', strtotime($notice['begin_send_time'])); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Thời gian kết thúc <span class="text-red-600">*</span>
                        </label>
                        <input type="datetime-local" name="end_send_time" 
                               value="<?php echo date('Y-m-d\TH:i', strtotime($notice['end_send_time'])); ?>"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </div>

                <!-- Interval -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        Khoảng thời gian gửi (giây) <span class="text-red-600">*</span>
                    </label>
                    <input type="number" name="send_interval" min="1" 
                           value="<?php echo $notice['send_interval']; ?>"
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-save mr-2"></i>Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('noticeForm');
    const noticeType = form.querySelector('[name="notice_type"]');
    const linkField = document.getElementById('linkField');

    // Hiển thị/ẩn trường link theo loại thông báo
    noticeType.addEventListener('change', function() {
        linkField.classList.toggle('hidden', this.value !== '2');
    });

    // Xử lý submit form
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        // Validate
        const serverIds = Array.from(form.querySelectorAll('[name="server_ids[]"]:checked')).map(cb => cb.value);
        if (serverIds.length === 0) {
            alert('Vui lòng chọn ít nhất một máy chủ');
            return;
        }

        const content = form.querySelector('[name="content"]').value.trim();
        if (!content) {
            alert('Vui lòng nhập nội dung thông báo');
            return;
        }

        const beginTime = form.querySelector('[name="begin_send_time"]').value;
        const endTime = form.querySelector('[name="end_send_time"]').value;
        if (!beginTime || !endTime) {
            alert('Vui lòng chọn thời gian bắt đầu và kết thúc');
            return;
        }

        if (new Date(beginTime) >= new Date(endTime)) {
            alert('Thời gian kết thúc phải sau thời gian bắt đầu');
            return;
        }

        const interval = parseInt(form.querySelector('[name="send_interval"]').value);
        if (isNaN(interval) || interval < 1) {
            alert('Khoảng thời gian gửi không hợp lệ');
            return;
        }

        // Submit form
        const formData = new FormData(form);
        fetch('?route=gamenotice&action=update', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                alert('Cập nhật thông báo thành công');
                window.location.href = '?route=gamenotice';
            } else {
                alert(data.message || 'Có lỗi xảy ra');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra');
        });
    });

    // Preview màu khi chọn
    const colorSelect = document.getElementById('colorSelect');
    const colorPreview = document.getElementById('colorPreview');
    const colorMap = <?php echo json_encode(array_column($colorOptions, 'value')); ?>;
    colorSelect.addEventListener('change', function() {
        colorPreview.style.background = colorMap[this.value] || '#000000';
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 