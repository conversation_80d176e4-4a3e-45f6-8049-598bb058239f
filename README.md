# GMTool - <PERSON><PERSON> Thống Quản Lý Game

## Yêu <PERSON>hống
- PHP 7.x
- MySQL 5.7+
- Apache/Nginx
- Composer (để quản lý dependencies)

## Cài Đặt
1. Clone repository
2. Copy `.env.example` thành `.env` và cấu hình
3. Tạo database và import file `database/gmtool.sql`
4. Ch<PERSON>y `composer install`
5. Cấu hình Apache/Nginx trỏ đến thư mục `public`

## Cấu Trúc Thư <PERSON>
```
gmtool/
├── app/                    # Logic ứng dụng
│   ├── Controllers/        # Các controller
│   ├── Models/            # Các model
│   ├── Middleware/        # Middleware (phân quyền, bảo mật)
│   └── Helpers/           # Helper functions
├── config/                # File cấu hình
│   ├── database.php
│   ├── permissions.php
│   └── api.php
├── public/                # Thư mục public
│   ├── index.php         # Entry point
│   ├── assets/           # CSS, JS, images
│   └── .htaccess
├── resources/            # View templates
│   └── views/
├── routes/               # Đ<PERSON>nh nghĩa routes
├── storage/             # Logs, cache
├── database/            # Migration & seeds
└── vendor/              # Dependencies
```

## Tính Năng
- Đăng nhập & phân quyền
- Quản lý người dùng
- Quản lý nạp tiền
- Thống kê & báo cáo
- API cho bên thứ 3
- Nhật ký hoạt động
- Bảo mật cao

## Bảo Mật
- Xác thực nhiều bước
- CSRF Protection
- Rate Limiting
- IP Whitelist
- Logging đầy đủ

## License
Copyright © 2024