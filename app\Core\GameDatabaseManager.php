<?php
namespace App\Core;

class GameDatabaseManager {
    private $mainDb;
    private $connections = [];

    public function __construct($mainDb) {
        $this->mainDb = $mainDb;
    }

    // Kết nối tới nhiều server theo mảng serverId
    public function connectServers(array $serverIds) {
        $results = [];
        foreach ($serverIds as $serverId) {
            try {
                $this->connections[$serverId] = new GameDatabaseConnection($serverId, $this->mainDb);
                $results[$serverId] = ['success' => true];
            } catch (\Exception $e) {
                $results[$serverId] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        return $results;
    }

    // Thực thi lệnh trên tất cả server đã kết nối
    public function executeOnAll($sql, $params = []) {
        $results = [];
        foreach ($this->connections as $serverId => $conn) {
            try {
                $data = $conn->fetchAll($sql, $params);
                $results[$serverId] = [
                    'success' => true,
                    'data' => $data
                ];
            } catch (\Exception $e) {
                $results[$serverId] = [
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        return $results;
    }

    public function executeOnServer($serverId, $sql, $params = []) {
        if (!isset($this->connections[$serverId])) {
            return [
                'success' => false,
                'error' => "Server $serverId chưa được kết nối"
            ];
        }
        try {
            $data = $this->connections[$serverId]->fetchAll($sql, $params);
            return [
                'success' => true,
                'data' => $data
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    // Đóng tất cả kết nối
    public function closeAll() {
        foreach ($this->connections as $conn) {
            $conn->close();
        }
        $this->connections = [];
    }
}
