<?php
namespace Api\Core;

use App\Services\RedisService;
use App\Services\UserService;

trait Auth
{
    protected $authenticatedUser = null;
    protected $authTokenData = null;
    protected $userService = null;

    /**
     * Get UserService instance
     */
    protected function getUserService(): UserService
    {
        if ($this->userService === null) {
            $this->userService = UserService::getInstance($this->db);
        }
        return $this->userService;
    }

    /**
     * Kiểm tra endpoint có cần authentication không
     */
    protected function requiresAuth(string $endpointName): bool
    {
        // Lấy cấu hình endpoint trực tiếp
        $endpointConfig = $this->config['api']['endpoints'][$endpointName] ?? [];
        
        // Kiểm tra trường require_auth, mặc định là true nếu không được định nghĩa
        return $endpointConfig['require_auth'] ?? true;
    }

    /**
     * Tự động xác minh token nếu endpoint yêu cầu
     */
    protected function autoVerifyToken(string $endpointName): void
    {
        if (!$this->requiresAuth($endpointName)) {
            return; // Endpoint công khai, không cần auth
        }

        // Lấy token từ header
        $token = $_SERVER['HTTP_AUTHORIZATION'] ?? null;
        if (!$token) {
            throw new \Exception('Authorization header is required');
        }

        $token = str_replace('Bearer ', '', $token);
        
        // Xác minh token
        $this->authTokenData = $this->verifyAccessToken($token);
        
        // Lấy thông tin user từ cache thông qua UserService
        $this->authenticatedUser = $this->getUserService()->getByUserId($this->authTokenData['user_id']);

        if (!$this->authenticatedUser) {
            throw new \Exception('User không tồn tại');
        }

        if ($this->authenticatedUser['status'] != 1) {
            throw new \Exception('Tài khoản đã bị khóa');
        }

        // Kiểm tra rate limiting
        $this->checkRateLimit($endpointName);
    }

    /**
     * Kiểm tra rate limiting
     */
    protected function checkRateLimit(string $endpointName): void
    {
        $authConfig = $this->config['api']['authentication'] ?? [];
        $rateLimits = $authConfig['rate_limits'] ?? [];
        
        $limit = $rateLimits[$endpointName] ?? $rateLimits['default'] ?? 60;
        $userId = $this->authenticatedUser['user_id'] ?? 'anonymous';
        
        $validator = Validator::getInstance();

        // Key để track rate limit cho Redis
        $redisKey = "rate_limit:{$endpointName}:{$userId}";
        
        // Thử sử dụng Redis trước
        try {
            $redisService = RedisService::getInstance();
            if ($redisService->isAvailable()) {
                $result = $redisService->checkRateLimit($redisKey, $limit, 60);
                if (!$result['allowed']) {
                    $response = [
                        'status' => false,
                        'message' => "Bạn đã vượt quá giới hạn sử dụng. Vui lòng thử lại sau " . date('Y-m-d H:i:s', $result['reset_time'])
                    ];
                    $this->jsonResponse($response, 429); // 429 Too Many Requests
                    exit;
                }
                return;
            }
        } catch (\Exception $e) {
            error_log("Redis rate limit failed, fallback to database: " . $e->getMessage());
        }
    }

    /**
     * Lấy thông tin user đã xác thực
     */
    protected function getAuthenticatedUser(): ?array
    {
        return $this->authenticatedUser;
    }

    /**
     * Lấy token data đã xác thực
     */
    protected function getAuthTokenData(): ?array
    {
        return $this->authTokenData;
    }

    /**
     * Wrapper cho endpoint với automatic auth
     */
    protected function withAuth(string $endpointName, callable $callback, array $options = [])
    {
        // Tự động verify token nếu cần
        $this->autoVerifyToken($endpointName);
        
        // Execute callback với context đã authenticated
        return $callback();
    }

    /**
     * Shorthand để yêu cầu auth (throw exception nếu chưa auth)
     */
    protected function requireAuth(): void
    {
        if (!$this->authenticatedUser) {
            throw new \Exception('Authentication required');
        }
    }
} 