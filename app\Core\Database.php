<?php
namespace App\Core;
use Exception;
use PDO;
class Database {
    private static $instance = null;
    private $connection;
    private $config;

    private function __construct($config) {
        $this->config = $config;
        $this->connect();
    }

    public static function getInstance($config = null) {
        if (self::$instance === null) {
            if ($config === null) {
                throw new Exception("Database configuration is required for first initialization");
            }
            self::$instance = new self($config);
        }
        return self::$instance;
    }

    private function connect() {
        try {
            // echo "Kết nối đến cơ sở dữ liệu...\n";
            // echo "Host: " . $this->config['host'] . "\n";
            // echo "Port: " . $this->config['port'] . "\n";
            // echo "Database: " . $this->config['database'] . "\n";
            // echo "Charset: " . $this->config['charset'] . "\n";
            // echo "Username: " . $this->config['username'] . "\n";
            // echo "Password: " . $this->config['password'] . "\n";

            $this->connection = new PDO(
                "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}",
                $this->config['username'],
                $this->config['password']
            );

            $this->connection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->connection->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            die("Connection failed: " . $e->getMessage());
        }
    }

    public function getConnection() {
        return $this->connection;
    }

    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }

    public function fetch($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }


    public function fetchValue($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        $row = $stmt->fetch(PDO::FETCH_NUM);
        return $row ? $row[0] : null;
    }

    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }

    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');

        $sql = "INSERT INTO $table (" . implode(', ', $fields) . ")
                VALUES (" . implode(', ', $placeholders) . ")";

        $this->query($sql, array_values($data));
        return $this->connection->lastInsertId();
    }

    public function batchInsert($table, $data) {
        if (empty($data)) {
            return;
        }

        $fields = array_keys($data[0]);
        $placeholders = array_fill(0, count($fields), '?');
        $values = [];
        $params = [];

        foreach ($data as $row) {
            $values[] = '(' . implode(', ', $placeholders) . ')';
            $params = array_merge($params, array_values($row));
        }

        $sql = "INSERT INTO $table (" . implode(', ', $fields) . ")
                VALUES " . implode(', ', $values);

        $this->query($sql, $params);
    }

    public function update($table, $data, $where, $whereParams = []) {
        $fields = array_map(function($field) {
            return "$field = ?";
        }, array_keys($data));

        $sql = "UPDATE $table SET " . implode(', ', $fields) . " WHERE $where";

        $params = array_merge(array_values($data), $whereParams);
        $this->query($sql, $params);
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM $table WHERE $where";
        $this->query($sql, $params);
    }

    public function beginTransaction() {
        return $this->connection->beginTransaction();
    }

    public function commit() {
        return $this->connection->commit();
    }

    public function rollBack() {
        return $this->connection->rollBack();
    }

    public function log($action, $status, $data = []) {
        try {
            $userId = $_SESSION['user_id'] ?? null;
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;

            $this->insert('gm_logs', [
                'user_id' => $userId,
                'action' => $action,
                'status' => $status,
                'ip_address' => $ipAddress,
                'data' => json_encode($data)
            ]);
        } catch (Exception $e) {
            // Không throw exception để tránh lỗi vòng lặp
            error_log("Failed to log: " . $e->getMessage());
        }
    }

    public function getServers($includeMerged = false, $returnIdsOnly = false) {
        $sql = "SELECT s1.*, s2.name as merged_into_name
                FROM game_servers s1
                LEFT JOIN game_servers s2 ON s1.merged_into = s2.id";

        if (!$includeMerged) {
            $sql .= " WHERE s1.merged_into IS NULL";
        }

        $sql .= " ORDER BY s1.created_at ASC";

        $result = $this->fetchAll($sql);

        if ($returnIdsOnly) {
            return array_column($result, 'id');
        }

        return $result;
    }

    public function getServerById($id) {
        return $this->fetch(
            "SELECT s1.*, s2.name as merged_into_name
             FROM game_servers s1
             LEFT JOIN game_servers s2 ON s1.merged_into = s2.id
             WHERE s1.id = ?",
            [$id]
        );
    }

    // Lấy thông tin server, trả về cho api
    public function getServerInfo($isGm = false) {
        $sql = "SELECT
                    s1.id,
                    s1.name as server_name,
                    CASE
                        WHEN s1.merged_into IS NOT NULL THEN s2.ip
                        ELSE s1.ip
                    END as ip,
                    CASE
                        WHEN s1.merged_into IS NOT NULL THEN s2.port
                        ELSE s1.port
                    END as port,
                    CASE
                        WHEN s1.merged_into IS NOT NULL THEN s2.status
                        ELSE s1.status
                    END as status,
                    CASE
                        WHEN s1.merged_into IS NOT NULL THEN s2.opentime
                        ELSE s1.opentime
                    END as opentime
                FROM game_servers s1
                LEFT JOIN game_servers s2 ON s1.merged_into = s2.id";

        if (!$isGm) {
            $sql .= " WHERE CASE
                        WHEN s1.merged_into IS NOT NULL THEN s2.opentime
                        ELSE s1.opentime
                    END <= NOW()";
        }

        $sql .= " ORDER BY s1.id ASC";

        $servers = $this->fetchAll($sql);

        // Kiểm tra trạng thái bảo trì toàn cục
        $serverStatusConfig = $this->getConfigByKey('server_status');
        $isGlobalMaintenance = ($serverStatusConfig && $serverStatusConfig['type'] === 'boolean' && $serverStatusConfig['config_value'] == '1');

        if ($isGlobalMaintenance && !$isGm) {
            foreach ($servers as &$server) {
                $server['status'] = 2; // 2 là mã bảo trì
            }
        } elseif ($isGm) {
            foreach ($servers as &$server) {
                $server['status'] = 1; // 1 là hoạt động
            }
        }
        return $servers;
    }

    public function mergeServers($sourceId, $targetId) {
        try {
            // Cập nhật merged_into cho server nguồn
            $this->update('game_servers',
                ['merged_into' => $targetId],
                'id = ?',
                [$sourceId]
            );

            // Cập nhật tất cả các server đã gộp vào server nguồn
            $this->update('game_servers',
                ['merged_into' => $targetId],
                'merged_into = ?',
                [$sourceId]
            );
            return true;
        } catch (Exception $e) {
            throw $e;
        }
    }

    // Lấy config theo key
    public function getConfigByKey($key) {
        return $this->fetch("SELECT * FROM config WHERE config_key = ? LIMIT 1", [$key]);
    }

    // Lấy tất cả config
    public function getAllConfigs() {
        return $this->fetchAll("SELECT * FROM config");
    }

    public function logApiRequest($endpoint, $method, $requestData, $responseData, $statusCode) {
        try {
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';

            $this->insert('api_logs', [
                'endpoint' => $endpoint,
                'method' => $method,
                'request_data' => json_encode($requestData),
                'response_data' => json_encode($responseData),
                'ip_address' => $ipAddress,
                'status_code' => $statusCode
            ]);
        } catch (\Exception $e) {
            // Không throw exception để tránh ảnh hưởng đến flow chính
            error_log("Failed to log API request: " . $e->getMessage());
        }
    }


}