<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kh<PERSON><PERSON> phục mật khẩu - <?= htmlspecialchars($app_name) ?></title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            padding: 20px;
        }
        .email-wrapper {
            max-width: 600px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            text-align: center;
            padding: 40px 30px;
        }
        .header h1 {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 8px;
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .greeting {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .message {
            font-size: 16px;
            color: #5a6c7d;
            margin-bottom: 30px;
            line-height: 1.7;
        }
        .action-container {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 30px;
            text-align: center;
            margin: 30px 0;
        }
        .reset-button {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 16px 32px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            margin: 15px 0;
            transition: transform 0.2s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }
        .reset-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }
        .token-section {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }
        .token-label {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 10px;
        }
        .reset-token {
            background: white;
            border: 2px solid #ff6b6b;
            border-radius: 8px;
            padding: 12px 16px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #ff6b6b;
            word-break: break-all;
            font-weight: 600;
        }
        .warning-box {
            background: #fef3c7;
            border-left: 4px solid #f59e0b;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .warning-box .title {
            font-weight: 600;
            color: #92400e;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .warning-box ul {
            color: #92400e;
            margin-left: 20px;
        }
        .warning-box li {
            margin-bottom: 5px;
        }
        .security-box {
            background: #dbeafe;
            border-left: 4px solid #3b82f6;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        .security-box .title {
            font-weight: 600;
            color: #1e40af;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .security-box p, .security-box ul {
            color: #1e40af;
        }
        .security-box ul {
            margin-left: 20px;
        }
        .security-box li {
            margin-bottom: 5px;
        }
        .footer {
            background: #f8fafc;
            padding: 25px 30px;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        .footer p {
            font-size: 14px;
            color: #64748b;
            margin-bottom: 5px;
        }
        .footer a {
            color: #ff6b6b;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="email-wrapper">
        <div class="header">
            <h1><?= htmlspecialchars($app_name) ?></h1>
            <p>Khôi phục mật khẩu</p>
        </div>

        <div class="content">
            <div class="greeting">Xin chào <?= htmlspecialchars($username) ?>! 🔐</div>

            <div class="message">
                Chúng tôi nhận được yêu cầu khôi phục mật khẩu cho tài khoản <strong><?= htmlspecialchars($app_name) ?></strong> của bạn. Nhấn nút bên dưới để đặt lại mật khẩu:
            </div>

            <div class="action-container">
                <a href="<?= htmlspecialchars($reset_url) ?>" class="reset-button">
                    🔐 Đặt lại mật khẩu ngay
                </a>

                <div class="token-section">
                    <div class="token-label">Hoặc sử dụng mã khôi phục:</div>
                    <div class="reset-token"><?= htmlspecialchars($reset_token) ?></div>
                </div>
            </div>

            <div class="warning-box">
                <div class="title">
                    <span>⚠️</span> Lưu ý quan trọng
                </div>
                <ul>
                    <li>Link có hiệu lực trong <strong>1 giờ</strong></li>
                    <li>Chỉ sử dụng được <strong>1 lần duy nhất</strong></li>
                    <li>Không chia sẻ với bất kỳ ai</li>
                </ul>
            </div>

            <div class="security-box">
                <div class="title">
                    <span>🛡️</span> Bảo mật tài khoản
                </div>
                <p>Nếu bạn <strong>KHÔNG</strong> yêu cầu khôi phục mật khẩu:</p>
                <ul>
                    <li>Hãy bỏ qua email này</li>
                    <li>Kiểm tra bảo mật tài khoản</li>
                    <li>Liên hệ hỗ trợ nếu nghi ngờ bị xâm nhập</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p><strong><?= htmlspecialchars($app_name) ?></strong> - Hệ thống gửi tự động</p>
            <p>Vui lòng không trả lời email này</p>
        </div>
    </div>
</body>
</html>
