<?php
namespace App\Core;

use App\Core\GameSqlBuilder;
use App\Services\UserService;
class Controller {
    protected $db;
    protected $config;

    protected $gameSqlBuilder;

    protected $gameDatabaseManager;
    
    protected $gameLogDb;

    protected $THIS_DATETIME;

    protected $allItems = [];

    protected $categories = [];

    protected $viewConfig = [
        'container' => 'max-w-7xl',
        'padding' => 'py-6 sm:px-6 lg:px-8'
    ];

    public function __construct($db, $config) {
        $this->db = $db;
        $this->config = $config;
        $this->gameSqlBuilder = new GameSqlBuilder();
        $this->gameDatabaseManager = new GameDatabaseManager($this->db);
        $this->THIS_DATETIME = time();
    }
    
    protected function initItems() {
        $jsonPath = __DIR__ . '/../../data/merged_items.json';
        if (file_exists($jsonPath)) {
            $itemsData = json_decode(file_get_contents($jsonPath), true);
            if ($itemsData) {
                foreach ($itemsData as $categoryKey => $categoryData) {
                    $categoryName = $categoryData['name'] ?? $categoryKey;
                    $categoryRes = $categoryData['res'] ?? '';
                    $this->categories[$categoryKey] = $categoryName;
                    if (isset($categoryData['data'])) {
                        foreach ($categoryData['data'] as $itemId => $item) {
                            $item['category_name'] = $categoryName;
                            $item['category_key'] = $categoryKey;
                            $item['res'] = $categoryRes;
                            $this->allItems[$itemId] = $item;
                        }
                    }
                }
            }
        }
    }
    protected function getUserService(): UserService {
        return UserService::getInstance($this->db);
    }   
    // Kiểm tra access token của user
    protected function verifyAccessToken($token) {
        if (empty($token)) {
            throw new \Exception('Access token is required');
        }

        // Sử dụng UserService với cache để verify token
        $userService = $this->getUserService();
        $tokenData = $userService->getAccessToken($token);

        if (!$tokenData) {
            throw new \Exception('Token không hợp lệ hoặc đã hết hạn');
        }

        return $tokenData;
    }

    protected function generateAccessToken($userId, $deviceInfo = null, $expiresIn = null) {
        // Lấy cấu hình token từ config
        $tokenSettings = $this->config['api']['authentication']['token_settings'] ?? [];
        
        // Nếu không có expiresIn, lấy từ config
        if ($expiresIn === null) {
            $expiresIn = $tokenSettings['default_expire'] ?? '+7 days';
        }
        
        // Xóa các token hết hạn nếu được cấu hình
        if ($tokenSettings['cleanup_expired'] ?? false) {
            $this->db->query("DELETE FROM access_tokens WHERE expires_at <= NOW()");
        }
        
        // Kiểm tra số lượng token trong 24h
        $maxTokens = $tokenSettings['max_tokens_per_user'] ?? 120;
        $tokenCount = $this->db->fetch(
            "SELECT COUNT(*) as count FROM access_tokens 
             WHERE user_id = ? 
             AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)",
            [$userId]
        );

        if ($tokenCount['count'] >= $maxTokens) {
            throw new \Exception("Bạn đã tạo quá nhiều token (giới hạn {$maxTokens} token/24h). Vui lòng thử lại sau.");
        }
        
        $token = bin2hex(random_bytes(32));
        $refreshToken = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime($expiresIn));
        
        $tokenId = $this->db->insert('access_tokens', [
            'user_id' => $userId,
            'token' => $token,
            'refresh_token' => $refreshToken,
            'expires_at' => $expiresAt,
            'device_info' => $deviceInfo ? json_encode($deviceInfo) : null
        ]);
        
        // Cache token thông qua UserService
        $userService = $this->getUserService();
        $userService->cacheAccessToken([
            'id' => $tokenId,
            'user_id' => $userId,
            'token' => $token,
            'refresh_token' => $refreshToken,
            'expires_at' => $expiresAt,
            'device_info' => $deviceInfo ? json_encode($deviceInfo) : null
        ]);
        
        return [
            'access_token' => $token,
            'refresh_token' => $refreshToken,
            'expires_at' => $expiresAt
        ];
    }

    protected function generateSign(...$params) {
        $stringToHash = implode('', $params) . $this->config['api']['settings']['token'];
        return md5($stringToHash);
    }

    protected function checkToken($expectedSign, $data) {
        if (!isset($data['sign']) || $data['sign'] !== $expectedSign) {
            return false;
        }
        return true;
    }

    protected function jsonResponse($data, $statusCode = 200) {
        // Clear output buffer
        if (ob_get_level()) {
            ob_clean();
        }
        
        // Check if headers sent
        if (!headers_sent()) {
            http_response_code($statusCode);
            header('Content-Type: application/json; charset=utf-8');
        }
        
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }

    protected function hasPermission($permission) {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        $userRole = $_SESSION['role'] ?? '';
        $roles = $this->config['permissions']['roles'];

        if (!isset($roles[$userRole])) {
            return false;
        }

        $userPermissions = $roles[$userRole]['permissions'];
        
        return $userPermissions === '*' || in_array($permission, $userPermissions);
    }

    protected function redirectToFirstAccessiblePage() {
        $userRole = $_SESSION['role'] ?? '';
        $roles = $this->config['permissions']['roles'];
        $modules = $this->config['permissions']['modules'];

        if (!isset($roles[$userRole])) {
            $_SESSION['error'] = 'Role không hợp lệ.';
            header('Location: /public/?route=auth&action=login');
            exit;
        }

        $userPermissions = $roles[$userRole]['permissions'];
        
        // Nếu có quyền tất cả
        if ($userPermissions === '*') {
            header('Location: /public/?route=dashboard');
            exit;
        }

        // Kiểm tra từng module trong permissions.php
        foreach ($modules as $module => $moduleData) {
            $permission = $module . '.view';
            if (in_array($permission, $userPermissions)) {
                header("Location: /public/?route=$module");
                exit;
            }
        }

        // Nếu không có quyền truy cập bất kỳ trang nào
        $_SESSION['error'] = 'Bạn không có quyền truy cập vào bất kỳ trang nào.';
        header('Location: /public/?route=auth&action=login');
        exit;
    }

    protected function requireLogin() {
        if (!isset($_SESSION['user_id'])) {
            header('Location: /public/?route=auth&action=login');
            exit;
        }
    }

    // Kiểm tra kết nối tới nhiều server game, trả về mảng lỗi nếu có
    protected function checkGameDbConnections($connectResults, $error = true): bool {
        if (!$error && empty($connectResults)) {
            return false;
        }
        foreach ($connectResults as $serverId => $result) {
            if (!$result['success']) {
                // Xử lý lỗi kết nối từng server
                if ($error) {
                    $this->jsonResponse(['status' => false, 'message' => "Server $serverId lỗi: " . $result['error']], 200);
                }
                return false;
            }
        }
        return true;
    }

    protected function isValidItemList($items): bool {
        if (empty($items)) {
            return false;
        }

        if (empty($this->allItems)) {
            $this->initItems();
        }

        $itemArr = explode(',', $items);
        foreach ($itemArr as $item) {
            $item = trim($item);
            if (empty($item)) {
                return false;
            }

            $parts = explode(':', $item);
            if (count($parts) != 2) {
                return false;
            }

            $id = trim($parts[0]);
            $qty = trim($parts[1]);

            if (!is_numeric($id) || !is_numeric($qty) || $qty <= 0) {
                return false;
            }

            // Kiểm tra id item có tồn tại trong danh sách items không
            if (!isset($this->allItems[$id])) {
                return false;
            }
        }

        return true;
    }

    protected function setViewConfig($config = []) {
        if (isset($config['container'])) {
            $this->viewConfig['container'] = $config['container'];
        }
        if (isset($config['padding'])) {
            $this->viewConfig['padding'] = $config['padding'];
        }
    }

    protected function getViewConfig() {
        return $this->viewConfig;
    }

    protected function initGameLogDb() {
        try {
            $host = getenv('GAME_LOG_DB_HOST');
            $dbname = getenv('GAME_LOG_DB_NAME');
            $user = getenv('GAME_LOG_DB_USER');
            $pass = getenv('GAME_LOG_DB_PASS');

            if (!$host || !$dbname || !$user || !$pass) {
                return false;
            }

            if (!$this->gameLogDb) {
                $this->gameLogDb = new GameLogDatabase($host, $dbname, $user, $pass);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
} 