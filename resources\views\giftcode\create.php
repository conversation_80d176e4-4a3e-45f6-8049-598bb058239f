<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg max-w-2xl mx-auto mt-8">
    <div class="px-4 py-5 sm:px-6 border-b">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            <i class="fas fa-gift mr-2"></i>Tạo nhóm Gift Code mới
        </h3>
    </div>
    <div class="p-6">
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-4 p-4 rounded-md bg-green-50">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            <?= htmlspecialchars($_SESSION['success']) ?>
                        </p>
                    </div>
                </div>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-4 p-4 rounded-md bg-red-50">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">
                            <?= htmlspecialchars($_SESSION['error']) ?>
                        </p>
                    </div>
                </div>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>

        <form method="POST" action="?route=giftcode&action=create" class="p-6 space-y-4">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            <div>
                <label class="block text-sm font-medium text-gray-700">Tên nhóm code</label>
                <input type="text" name="group_name" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2" required>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Loại code</label>
                <select name="type" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                    <option value="single">Code riêng (nhiều code random)</option>
                    <option value="common">Code chung (1 code, nhiều người dùng)</option>
                </select>
            </div>
            <div id="single-fields">
                <label class="block text-sm font-medium text-gray-700">Số lượng code random</label>
                <input type="number" name="amount" min="1" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                <label class="block text-sm font-medium text-gray-700 mt-2">Tiền tố code (prefix)</label>
                <input type="text" name="code_prefix" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            <div id="common-fields" style="display:none;">
                <label class="block text-sm font-medium text-gray-700">Code chung</label>
                <input type="text" name="code_value" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                <label class="block text-sm font-medium text-gray-700 mt-2">Số lần sử dụng tối đa</label>
                <input type="number" name="max_use" min="1" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Danh sách item (id:soluong, cách nhau bởi dấu phẩy)</label>
                <div class="relative">
                    <input type="text" name="items" id="items-input" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 pr-10" placeholder="vd: 10001:1,10002:5">
                    <button type="button" id="open-item-modal" class="absolute right-2 top-1/2 -translate-y-1/2 text-indigo-600 hover:text-indigo-800">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Thời hạn sử dụng</label>
                <div class="mt-1 flex items-center space-x-4">
                    <label class="inline-flex items-center">
                        <input type="radio" name="expired_type" value="unlimited" class="form-radio text-indigo-600" checked>
                        <span class="ml-2">Không giới hạn</span>
                    </label>
                    <label class="inline-flex items-center">
                        <input type="radio" name="expired_type" value="limited" class="form-radio text-indigo-600">
                        <span class="ml-2">Có thời hạn</span>
                    </label>
                </div>
                <div id="expired-date-field" class="mt-2 hidden">
                    <input type="datetime-local" name="expired_at" class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2">
                </div>
            </div>
            <div class="flex justify-end gap-2">
                <a href="?route=giftcode" class="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300 text-gray-700">Quay lại</a>
                <button type="submit" id="submit-btn" class="px-4 py-2 rounded bg-indigo-600 hover:bg-indigo-700 text-white">Lưu nhóm code</button>
            </div>
        </form>
    </div>
</div>
<script>
// Ẩn/hiện field theo loại code
const typeSelect = document.querySelector('select[name="type"]');
const singleFields = document.getElementById('single-fields');
const commonFields = document.getElementById('common-fields');
const expiredTypeRadios = document.querySelectorAll('input[name="expired_type"]');
const expiredDateField = document.getElementById('expired-date-field');

typeSelect.addEventListener('change', function() {
    if (this.value === 'single') {
        singleFields.style.display = '';
        commonFields.style.display = 'none';
    } else {
        singleFields.style.display = 'none';
        commonFields.style.display = '';
    }
});

expiredTypeRadios.forEach(radio => {
    radio.addEventListener('change', function() {
        if (this.value === 'limited') {
            expiredDateField.classList.remove('hidden');
        } else {
            expiredDateField.classList.add('hidden');
        }
    });
});

document.getElementById('open-item-modal').onclick = function() {
    let value = this.previousElementSibling.value;
    requestAndShowItemInfo(value);
};

// Block button submit khi form được submit
document.querySelector('form').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submit-btn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang xử lý...';
});
</script>
<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 