<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        <!-- Flash Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-3 sm:mb-4 p-3 sm:p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                <?php 
                echo $_SESSION['success'];
                unset($_SESSION['success']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-3 sm:mb-4 p-3 sm:p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                <?php 
                echo $_SESSION['error'];
                unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>

        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900">
                        Gửi thông báo trực tiếp
                    </h2>
                </div>
            </div>

            <!-- Main Content -->
            <div class="p-3 sm:p-6">
                <form id="directNoticeForm" class="space-y-4 sm:space-y-6">
                    <!-- Server Selection -->
                    <div class="mb-4 sm:mb-6 bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300">
                        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2">
                            <label class="block text-base sm:text-lg font-medium text-gray-700">Máy chủ <span class="text-red-600">*</span></label>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="select-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-blue-600 hover:text-blue-800 bg-blue-50 rounded-md border border-blue-200">Chọn tất cả</button>
                                <button type="button" id="deselect-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-red-600 hover:text-red-800 bg-red-50 rounded-md border border-red-200">Bỏ chọn tất cả</button>
                            </div>
                        </div>
                        <div class="relative mb-3 sm:mb-4">
                            <input type="text" id="server-search" placeholder="Tìm kiếm máy chủ..." 
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border-2 border-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute right-3 top-2.5">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="mt-3 sm:mt-4 max-h-96 overflow-y-auto border-2 border-gray-300 rounded-md">
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 p-2 sm:p-3">
                                <?php 
                                $dbServers = [];
                                foreach ($servers as $server) {
                                    if (!empty($server['mysql_db'])) {
                                        $dbServers[$server['mysql_db']][] = $server;
                                    }
                                }
                                foreach ($servers as $server): ?>
                                    <?php if (!$server['merged_into']): ?>
                                        <div class="server-item flex flex-col p-3 hover:bg-gray-100 rounded-md border border-gray-200">
                                            <div class="flex items-center">
                                                <input type="checkbox" name="server_ids[]" value="<?php echo $server['id']; ?>" 
                                                       class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400 rounded">
                                                <div class="ml-3">
                                                    <label class="block text-base font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($server['name']); ?>
                                                    </label>
                                                    <span class="text-sm text-gray-500">ID: <?php echo $server['id']; ?></span>
                                                </div>
                                            </div>
                                            <?php if (!empty($server['mysql_db']) && count($dbServers[$server['mysql_db']]) > 1): ?>
                                                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                                                    <div class="flex items-start">
                                                        <svg class="h-5 w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                        </svg>
                                                        <div class="text-sm text-yellow-700">
                                                            <p class="font-medium">Cảnh báo: Database trùng lặp</p>
                                                            <p class="mt-1">Database này cũng được sử dụng bởi:</p>
                                                            <ul class="list-disc list-inside mt-1">
                                                                <?php foreach ($dbServers[$server['mysql_db']] as $otherServer): ?>
                                                                    <?php if ($otherServer['id'] != $server['id']): ?>
                                                                        <li>Server <?php echo $otherServer['id']; ?>: <?php echo htmlspecialchars($otherServer['name']); ?></li>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700 mb-2">Nội dung thông báo (không bắt buộc)</label>
                        <textarea id="content" name="content" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>

                    <div class="flex items-center space-x-3 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                        <input type="checkbox" id="disconnect" name="disconnect" value="true" class="h-5 w-5 text-red-600 focus:ring-red-500 border-gray-300 rounded">
                        <div>
                            <label for="disconnect" class="block text-sm font-medium text-red-700">
                                Ngắt kết nối người chơi
                            </label>
                            <p class="text-sm text-red-600 mt-1">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Lưu ý: Tùy chọn này sẽ kick toàn bộ người chơi đang online khỏi server
                            </p>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base">
                            Gửi thông báo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('directNoticeForm');
    const selectAllBtn = document.getElementById('select-all');
    const deselectAllBtn = document.getElementById('deselect-all');
    const serverSearch = document.getElementById('server-search');
    const serverItems = document.querySelectorAll('.server-item');

    // Chọn tất cả máy chủ
    selectAllBtn.addEventListener('click', function() {
        serverItems.forEach(item => {
            if (item.style.display !== 'none') {
                item.querySelector('input[type="checkbox"]').checked = true;
            }
        });
    });

    // Bỏ chọn tất cả máy chủ
    deselectAllBtn.addEventListener('click', function() {
        serverItems.forEach(item => {
            if (item.style.display !== 'none') {
                item.querySelector('input[type="checkbox"]').checked = false;
            }
        });
    });

    // Tìm kiếm server
    serverSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        serverItems.forEach(item => {
            const label = item.querySelector('label').textContent.toLowerCase();
            const id = item.querySelector('span').textContent.toLowerCase();
            if (label.includes(searchTerm) || id.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Xử lý submit form
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');

        // Show loading
        Swal.fire({
            title: 'Đang gửi thông báo...',
            allowOutsideClick: false,
            width: '90vw',
            maxWidth: '400px',
            didOpen: () => {
                Swal.showLoading();
            }
        });

        fetch('?route=gamenotice&action=sendDirectNotice', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                let html = '<div class="text-left">';
                html += `<p class="mb-3 sm:mb-4">${data.message}</p>`;
                html += '<div class="space-y-2">';
                
                data.results.forEach(result => {
                    const serverItem = document.querySelector(`.server-item input[value="${result.server_id}"]`).closest('.server-item');
                    const serverName = serverItem.querySelector('label').textContent;
                    
                    if (result.status) {
                        html += `<div class="flex items-center text-green-600">
                            <i class="fas fa-check-circle mr-2"></i>
                            <span>Server ${result.server_id} (${serverName}): ${result.message}</span>
                        </div>`;
                    } else {
                        html += `<div class="flex items-center text-red-600">
                            <i class="fas fa-times-circle mr-2"></i>
                            <span>Server ${result.server_id} (${serverName}): ${result.message}</span>
                        </div>`;
                        if (result.error) {
                            html += `<div class="ml-4 sm:ml-6 text-sm text-red-500">${result.error}</div>`;
                        }
                    }
                });
                
                html += '</div></div>';

                Swal.fire({
                    icon: 'success',
                    title: 'Kết quả gửi thông báo',
                    html: html,
                    width: '90vw',
                    maxWidth: '600px'
                }).then(() => {
                    form.reset();
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: data.message,
                    width: '90vw',
                    maxWidth: '400px'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Có lỗi xảy ra khi gửi thông báo',
                width: '90vw',
                maxWidth: '400px'
            });
        });
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 