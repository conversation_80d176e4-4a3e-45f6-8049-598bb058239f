<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- <PERSON><PERSON><PERSON><PERSON> thư viện Chart.js -->
<script src="/public/assets/js/chart.min.js"></script>
<script src="/public/assets/js/chartjs-adapter-date-fns.min.js"></script>

<!-- Thêm style cho dashboard -->
<style>
    .stat-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    }
    .stat-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 10px -3px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    }
    .stat-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.75rem;
    }
    .stat-value {
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1.2;
        margin-top: 0.25rem;
    }
    .stat-label {
        font-size: 0.8rem;
        color: #6b7280;
        margin-bottom: 0.15rem;
    }
    .chart-container {
        position: relative;
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }
    .chart-container:hover {
        box-shadow: 0 6px 10px -3px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05);
    }
    .dashboard-section {
        border-radius: 0.75rem;
        background: white;
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
        margin-bottom: 1rem;
    }
    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1f2937;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #e5e7eb;
        margin-bottom: 1rem;
    }
    /* Compact layout */
    .grid {
        gap: 0.75rem !important;
    }
    .p-5 {
        padding: 0.75rem !important;
    }
    .p-6 {
        padding: 1rem !important;
    }
    .mb-6 {
        margin-bottom: 0.75rem !important;
    }
    .mt-6 {
        margin-top: 0.75rem !important;
    }
    .mt-8 {
        margin-top: 1rem !important;
    }
    
    /* Modal styles */
    .modal {
        display: none;
        position: fixed;
        inset: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 50;
    }
    
    .modal-content {
        background-color: white;
        border-radius: 0.5rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        max-width: 32rem;
        width: 100%;
        max-height: 80vh;
        overflow: hidden;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .modal-body {
        padding: 1rem;
        max-height: 50vh;
        overflow-y: auto;
    }
    
    .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>

<!-- Thống kê tổng quan -->
<div class="dashboard-section p-6 mb-6">
    <h2 class="section-title flex items-center">
        <i class="fas fa-chart-pie mr-2 text-indigo-600"></i>
        Số liệu tổng quan
    </h2>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Tổng người dùng -->
        <div class="stat-card bg-gradient-to-br from-blue-50 to-blue-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                    <i class="fas fa-users text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                    <i class="fas fa-users mr-1"></i> Tổng
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">Tổng người dùng</div>
                <div class="stat-value text-blue-700"><?= number_format($totalUsers) ?></div>
            </div>
        </div>
        
        <!-- Người dùng mới hôm nay -->
        <div class="stat-card bg-gradient-to-br from-green-50 to-green-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-green-500 to-green-600 text-white">
                    <i class="fas fa-user-plus text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-green-100 text-green-800">
                    <i class="fas fa-calendar-day mr-1"></i> Hôm nay
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">Người dùng mới hôm nay</div>
                <div class="stat-value text-green-700"><?= number_format($todayUsers) ?></div>
            </div>
        </div>
        
        <!-- Tổng doanh thu -->
        <div class="stat-card bg-gradient-to-br from-yellow-50 to-yellow-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                    <i class="fas fa-coins text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">
                    <i class="fas fa-money-bill-wave mr-1"></i> Tổng
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">Tổng doanh thu</div>
                <div class="stat-value text-yellow-700"><?= number_format($totalAllRevenue) ?> <span class="text-sm font-medium">VNĐ</span></div>
            </div>
        </div>
        
        <!-- ARPU tổng -->
        <div class="stat-card bg-gradient-to-br from-purple-50 to-purple-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                    <i class="fas fa-chart-line text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                    <i class="fas fa-calculator mr-1"></i> Trung bình
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">ARPU tổng</div>
                <div class="stat-value text-purple-700"><?= number_format($totalARPU) ?> <span class="text-sm font-medium">VNĐ</span></div>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
        <!-- Doanh thu hôm nay -->
        <div class="stat-card bg-gradient-to-br from-red-50 to-red-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-red-500 to-red-600 text-white">
                    <i class="fas fa-calendar-day text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-red-100 text-red-800">
                    <i class="fas fa-clock mr-1"></i> Hôm nay
            </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">Doanh thu hôm nay</div>
                <div class="stat-value text-red-700"><?= number_format($todayRevenue) ?> <span class="text-sm font-medium">VNĐ</span></div>
            </div>

            <?php if (!empty($stats['todayRevenueByServer'])): ?>
            <div class="mt-3 pt-3 border-t border-red-100">
                <button onclick="openRevenueServerModal()" class="w-full py-2 px-3 bg-red-50 hover:bg-red-100 text-red-700 rounded-lg text-sm font-medium transition-colors duration-200">
                    <i class="fas fa-server mr-1"></i> Xem doanh thu theo server
                </button>
            </div>

            <!-- Modal Doanh thu theo server -->
            <div id="revenueServerModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i class="fas fa-server mr-2 text-red-600"></i>
                            Doanh thu theo server hôm nay
                        </h3>
                        <button onclick="closeRevenueServerModal()" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="modal-body">
                        <div class="mb-4">
                            <div class="relative">
                                <input type="text" id="revenueServerSearchInput" placeholder="Tìm server..." class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-red-500 focus:border-red-500">
                                <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>

                        <div class="space-y-2 revenue-server-container">
                            <?php
                            foreach ($stats['todayRevenueByServer'] as $serverData):
                                $percent = $todayRevenue > 0 ? ($serverData['total_amount'] / $todayRevenue) * 100 : 0;
                            ?>
                                <div class="revenue-server-item" data-server-id="<?= $serverData['server_id'] ?>">
                                    <div class="flex justify-between items-center text-sm">
                                        <span class="text-gray-600">Server <?= $serverData['server_id'] ?></span>
                                        <span class="text-gray-700 font-medium"><?= number_format($serverData['total_amount']) ?> (<?= number_format($percent, 1) ?>%)</span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                        <div class="h-1.5 rounded-full bg-red-500" style="width: <?= $percent ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

        </div>
        
        <!-- Doanh thu tháng này -->
        <div class="stat-card bg-gradient-to-br from-pink-50 to-pink-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-pink-500 to-pink-600 text-white">
                    <i class="fas fa-calendar-alt text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-pink-100 text-pink-800">
                    <i class="fas fa-calendar-alt mr-1"></i> Tháng này
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">Doanh thu tháng này</div>
                <div class="stat-value text-pink-700"><?= number_format($thisMonthRevenue) ?> <span class="text-sm font-medium">VNĐ</span></div>
            </div>
        </div>
        
        <!-- Số role đang online -->
        <div class="stat-card bg-gradient-to-br from-indigo-50 to-indigo-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
                    <i class="fas fa-users text-xl"></i>
            </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-indigo-100 text-indigo-800">
                    <i class="fas fa-signal mr-1"></i> Trực tuyến
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">Số role đang online</div>
                <div class="stat-value text-indigo-700"><?= number_format($stats['roleOnlineCount']) ?></div>
            </div>
        </div>

        <!-- ARPPU tổng -->
        <div class="stat-card bg-gradient-to-br from-cyan-50 to-cyan-100 p-5">
            <div class="flex items-center justify-between">
                <div class="stat-icon bg-gradient-to-r from-cyan-500 to-cyan-600 text-white">
                    <i class="fas fa-coins text-xl"></i>
                </div>
                <div class="text-xs font-medium px-2 py-1 rounded-full bg-cyan-100 text-cyan-800">
                    <i class="fas fa-user-check mr-1"></i> Người trả phí
                </div>
            </div>
            <div class="mt-4">
                <div class="stat-label">ARPPU tổng</div>
                <div class="stat-value text-cyan-700"><?= number_format($stats['arppu']) ?> <span class="text-sm font-medium">VNĐ</span></div>
            </div>
        </div>
    </div>

    <!-- Thống kê người dùng hoạt động -->
    <div class="dashboard-section p-6 mt-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-user-clock mr-2 text-indigo-600"></i>
            Chỉ số tương tác người dùng
        </h2>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- DAU - Daily Active Users -->
            <div class="stat-card bg-gradient-to-br from-emerald-50 to-emerald-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-emerald-500 to-emerald-600 text-white">
                        <i class="fas fa-user-check text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-emerald-100 text-emerald-800">
                        <i class="fas fa-calendar-day mr-1"></i> Hôm nay
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Người dùng hoạt động hàng ngày (DAU)</div>
                    <div class="stat-value text-emerald-700"><?= number_format($stats['dau']) ?></div>
                </div>
            </div>

            <!-- MAU - Monthly Active Users -->
            <div class="stat-card bg-gradient-to-br from-teal-50 to-teal-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-teal-500 to-teal-600 text-white">
                        <i class="fas fa-users-cog text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-teal-100 text-teal-800">
                        <i class="fas fa-calendar-alt mr-1"></i> 30 ngày
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Người dùng hoạt động hàng tháng (MAU)</div>
                    <div class="stat-value text-teal-700"><?= number_format($stats['mau']) ?></div>
                </div>
            </div>

            <!-- DAU/MAU Ratio (Stickiness) -->
            <div class="stat-card bg-gradient-to-br from-sky-50 to-sky-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-sky-500 to-sky-600 text-white">
                        <i class="fas fa-percentage text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-sky-100 text-sky-800">
                        <i class="fas fa-chart-line mr-1"></i> Tỉ lệ
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Tỉ lệ DAU/MAU (Stickiness)</div>
                    <div class="stat-value text-sky-700"><?= number_format($stats['dauMauRatio'], 2) ?>%</div>
            </div>
        </div>
        
        <!-- Rate Pay tổng -->
            <div class="stat-card bg-gradient-to-br from-cyan-50 to-cyan-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-cyan-500 to-cyan-600 text-white">
                        <i class="fas fa-percentage text-xl"></i>
            </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-cyan-100 text-cyan-800">
                        <i class="fas fa-chart-pie mr-1"></i> Tỉ lệ
            </div>
        </div>
                <div class="mt-4">
                    <div class="stat-label">Rate Pay tổng</div>
                    <div class="stat-value text-cyan-700"><?= number_format($totalPayingRate, 2) ?>%</div>
                </div>
    </div>
</div>

        <!-- Retention Rate Section -->
        <div class="mt-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-user-clock mr-2 text-indigo-600"></i>
                Tỉ lệ giữ chân người dùng (Retention Rate)
            </h3>
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
                <!-- D1 Retention -->
                <div class="stat-card bg-gradient-to-br from-blue-50 to-blue-100 p-5">
                    <div class="flex items-center justify-between">
                        <div class="stat-icon bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                            <i class="fas fa-calendar-day text-xl"></i>
                        </div>
                        <div class="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                            <i class="fas fa-clock mr-1"></i> 1 ngày
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="stat-label">D1 Retention</div>
                        <div class="stat-value text-blue-700"><?= number_format($stats['d1Retention'], 2) ?>%</div>
                        <div class="text-xs text-gray-500 mt-1">Người dùng quay lại sau 1 ngày</div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-3">
                        <div class="h-2.5 rounded-full bg-blue-600" style="width: <?= min(100, $stats['d1Retention']) ?>%"></div>
                    </div>
                </div>

                <!-- D7 Retention -->
                <div class="stat-card bg-gradient-to-br from-indigo-50 to-indigo-100 p-5">
                    <div class="flex items-center justify-between">
                        <div class="stat-icon bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
                            <i class="fas fa-calendar-week text-xl"></i>
                        </div>
                        <div class="text-xs font-medium px-2 py-1 rounded-full bg-indigo-100 text-indigo-800">
                            <i class="fas fa-clock mr-1"></i> 7 ngày
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="stat-label">D7 Retention</div>
                        <div class="stat-value text-indigo-700"><?= number_format($stats['d7Retention'], 2) ?>%</div>
                        <div class="text-xs text-gray-500 mt-1">Người dùng quay lại sau 7 ngày</div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-3">
                        <div class="h-2.5 rounded-full bg-indigo-600" style="width: <?= min(100, $stats['d7Retention']) ?>%"></div>
                    </div>
                </div>

                <!-- D30 Retention -->
                <div class="stat-card bg-gradient-to-br from-purple-50 to-purple-100 p-5">
                    <div class="flex items-center justify-between">
                        <div class="stat-icon bg-gradient-to-r from-purple-500 to-purple-600 text-white">
                            <i class="fas fa-calendar-alt text-xl"></i>
                        </div>
                        <div class="text-xs font-medium px-2 py-1 rounded-full bg-purple-100 text-purple-800">
                            <i class="fas fa-clock mr-1"></i> 30 ngày
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="stat-label">D30 Retention</div>
                        <div class="stat-value text-purple-700"><?= number_format($stats['d30Retention'], 2) ?>%</div>
                        <div class="text-xs text-gray-500 mt-1">Người dùng quay lại sau 30 ngày</div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mt-3">
                        <div class="h-2.5 rounded-full bg-purple-600" style="width: <?= min(100, $stats['d30Retention']) ?>%"></div>
                    </div>
                </div>
            </div>

            <!-- Comprehensive Retention Rate Table -->
            <div class="mt-8 bg-white p-5 rounded-xl shadow-md">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                    <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-table mr-2 text-indigo-600"></i>
                        Bảng Retention Rate chi tiết
                    </h4>
                </div>

                <!-- Table View -->
                <div id="retentionTableView" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 text-sm">
                        <thead>
                            <tr>
                                <th class="px-3 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 z-10 bg-gray-50">Tháng</th>
                                <?php for ($day = 1; $day <= 31; $day++): ?>
                                    <th class="px-2 py-2 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">D<?= $day ?></th>
                                <?php endfor; ?>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($stats['retentionRateTable'] as $monthNumber => $monthData): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-3 py-2 whitespace-nowrap font-medium text-gray-900 sticky left-0 z-10 bg-white">
                                        <?= $monthData['month_name'] ?>
                                        <?php if (isset($monthData['registered_users'])): ?>
                                            <span class="text-xs text-gray-500 block"><?= number_format($monthData['registered_users']) ?> người dùng</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php for ($day = 1; $day <= 31; $day++): ?>
                                        <?php
                                            $retentionRate = $monthData['retention_rates'][$day] ?? null;
                                            $cellClass = '';
                                            $textClass = '';

                                            if ($retentionRate !== null) {
                                                if ($retentionRate >= 50) {
                                                    $cellClass = 'bg-green-100';
                                                    $textClass = 'text-green-800';
                                                } elseif ($retentionRate >= 30) {
                                                    $cellClass = 'bg-blue-100';
                                                    $textClass = 'text-blue-800';
                                                } elseif ($retentionRate >= 15) {
                                                    $cellClass = 'bg-yellow-100';
                                                    $textClass = 'text-yellow-800';
                                                } elseif ($retentionRate > 0) {
                                                    $cellClass = 'bg-red-50';
                                                    $textClass = 'text-red-800';
                                                }
                                            }
                                        ?>
                                        <td class="px-2 py-2 text-center whitespace-nowrap text-sm <?= $cellClass ?>">
                                            <?php if ($retentionRate !== null): ?>
                                                <span class="font-medium <?= $textClass ?>"><?= number_format($retentionRate, 1) ?>%</span>
                                            <?php else: ?>
                                                <span class="text-gray-300">-</span>
                                            <?php endif; ?>
                                        </td>
                                    <?php endfor; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Comprehensive Monthly Revenue Table -->
            <div class="mt-8 bg-white p-5 rounded-xl shadow-md">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                    <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                        <i class="fas fa-table mr-2 text-green-600"></i>
                        Bảng doanh thu chi tiết
                    </h4>
                </div>

                <!-- Table View -->
                <div id="revenueTableView" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 text-sm">
                        <thead>
                            <tr>
                                <th class="px-3 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 z-10 bg-gray-50">Tháng</th>
                                <?php for ($day = 1; $day <= 31; $day++): ?>
                                    <th class="px-2 py-2 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"><?= $day ?></th>
                                <?php endfor; ?>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($stats['monthlyRevenueTable'] as $monthNumber => $monthData): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-3 py-2 whitespace-nowrap font-medium text-gray-900 sticky left-0 z-10 bg-white">
                                        <?= $monthData['month_name'] ?>
                                        <?php if (isset($monthData['total_revenue'])): ?>
                                            <span class="text-xs text-gray-500 block"><?= number_format($monthData['total_revenue']) ?> VNĐ</span>
                                            <span class="text-xs text-gray-500 block"><?= number_format($monthData['transaction_count']) ?> giao dịch</span>
                                        <?php endif; ?>
                                    </td>
                                    <?php for ($day = 1; $day <= 31; $day++): ?>
                                        <?php
                                            $dailyRevenue = $monthData['daily_revenues'][$day] ?? null;
                                            $cellClass = '';
                                            $textClass = '';

                                            if ($dailyRevenue !== null) {
                                                if ($dailyRevenue >= 100000000) { // 100 million
                                                    $cellClass = 'bg-green-100';
                                                    $textClass = 'text-green-800';
                                                } elseif ($dailyRevenue >= 50000000) { // 50 million
                                                    $cellClass = 'bg-emerald-100';
                                                    $textClass = 'text-emerald-800';
                                                } elseif ($dailyRevenue >= 10000000) { // 10 million
                                                    $cellClass = 'bg-teal-100';
                                                    $textClass = 'text-teal-800';
                                                } elseif ($dailyRevenue >= 5000000) { // 5 million
                                                    $cellClass = 'bg-cyan-100';
                                                    $textClass = 'text-cyan-800';
                                                } elseif ($dailyRevenue >= 1000000) { // 1 million
                                                    $cellClass = 'bg-blue-100';
                                                    $textClass = 'text-blue-800';
                                                } elseif ($dailyRevenue > 0) {
                                                    $cellClass = 'bg-gray-100';
                                                    $textClass = 'text-gray-800';
                                                }
                                            }

                                            // Format the revenue for display
                                            $formattedRevenue = '';
                                            if ($dailyRevenue !== null) {
                                                if ($dailyRevenue >= 1000000) {
                                                    $formattedRevenue = number_format($dailyRevenue / 1000000, 1) . 'M';
                                                } elseif ($dailyRevenue >= 1000) {
                                                    $formattedRevenue = number_format($dailyRevenue / 1000, 1) . 'K';
                                                } else {
                                                    $formattedRevenue = number_format($dailyRevenue);
                                                }
                                            }
                                        ?>
                                        <td class="px-2 py-2 text-center whitespace-nowrap text-sm <?= $cellClass ?>"
                                            title="<?= $dailyRevenue !== null ? number_format($dailyRevenue) . ' VNĐ' : '' ?>">
                                            <?php if ($dailyRevenue !== null): ?>
                                                <span class="font-medium <?= $textClass ?>"><?= $formattedRevenue ?></span>
                                            <?php else: ?>
                                                <span class="text-gray-300">-</span>
                                            <?php endif; ?>
                                        </td>
                                    <?php endfor; ?>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Performance Section -->
    <div class="dashboard-section p-6 mt-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-server mr-2 text-indigo-600"></i>
            Hiệu suất game
        </h2>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Peak Concurrent Users Today -->
            <div class="stat-card bg-gradient-to-br from-amber-50 to-amber-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-amber-500 to-amber-600 text-white">
                        <i class="fas fa-bolt text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-amber-100 text-amber-800">
                        <i class="fas fa-calendar-day mr-1"></i> Hôm nay
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Đỉnh người chơi đồng thời</div>
                    <div class="stat-value text-amber-700"><?= number_format($stats['peakConcurrentUsers']) ?></div>
                </div>
            </div>

            <!-- Peak Concurrent Users Month -->
            <div class="stat-card bg-gradient-to-br from-orange-50 to-orange-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-orange-500 to-orange-600 text-white">
                        <i class="fas fa-fire text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-orange-100 text-orange-800">
                        <i class="fas fa-calendar-alt mr-1"></i> Tháng này
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Đỉnh người chơi trong tháng</div>
                    <div class="stat-value text-orange-700"><?= number_format($stats['peakConcurrentUsersMonth']) ?></div>
                </div>
            </div>

            <!-- Active Users in Month -->
            <div class="stat-card bg-gradient-to-br from-lime-50 to-lime-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-lime-500 to-lime-600 text-white">
                        <i class="fas fa-gamepad text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-lime-100 text-lime-800">
                        <i class="fas fa-users mr-1"></i> Hoạt động
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Người dùng hoạt động trong tháng</div>
                    <div class="stat-value text-lime-700"><?= number_format($stats['activeUsersInMonth']) ?></div>
                </div>
            </div>

            <!-- Server Count -->
            <div class="stat-card bg-gradient-to-br from-fuchsia-50 to-fuchsia-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-fuchsia-500 to-fuchsia-600 text-white">
                        <i class="fas fa-server text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-fuchsia-100 text-fuchsia-800">
                        <i class="fas fa-network-wired mr-1"></i> Servers
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Số lượng server</div>
                    <div class="stat-value text-fuchsia-700"><?= count($stats['serverDistribution']) ?></div>
                </div>
            </div>
        </div>

        <!-- Biểu đồ role online hôm nay -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <i class="fas fa-chart-bar mr-2 text-indigo-600"></i>
                Số role online theo giờ hôm nay
            </h3>
            <div class="chart-container bg-white p-5 rounded-lg border border-gray-200">
                <canvas id="onlineChart" height="120"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Thống kê và phân tích dữ liệu -->
<div class="p-4 sm:p-6 bg-white shadow-lg rounded-lg mb-6">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900">
            <i class="fas fa-chart-line mr-2 text-indigo-600"></i> Thống kê và phân tích dữ liệu
        </h1>
        
        <div class="flex flex-col sm:flex-row items-center gap-3">
            <!-- Bộ lọc tháng năm -->
            <div class="flex items-center gap-2">
                <form id="filterForm" method="GET" action="/public/" class="flex flex-wrap items-center gap-2">
                    <input type="hidden" name="route" value="dashboard">
                    
                    <div class="flex items-center">
                        <select name="month" id="monthSelect" class="rounded-l-lg border-gray-300 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500">
                            <?php
                            $monthsName = [
                                1 => 'Tháng 1',
                                2 => 'Tháng 2',
                                3 => 'Tháng 3',
                                4 => 'Tháng 4',
                                5 => 'Tháng 5',
                                6 => 'Tháng 6',
                                7 => 'Tháng 7',
                                8 => 'Tháng 8',
                                9 => 'Tháng 9',
                                10 => 'Tháng 10',
                                11 => 'Tháng 11',
                                12 => 'Tháng 12'
                            ];
                            foreach ($monthsName as $m => $name): ?>
                                <option value="<?= $m ?>" <?= $m == $stats['month'] ? 'selected' : '' ?>>
                                    <?= $name ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        
                        <select name="year" id="yearSelect" class="rounded-r-lg border-gray-300 py-2 text-sm focus:ring-indigo-500 focus:border-indigo-500 -ml-px">
                            <?php for ($y = date('Y'); $y >= date('Y') - 5; $y--): ?>
                                <option value="<?= $y ?>" <?= $y == $stats['year'] ? 'selected' : '' ?>>
                                    <?= $y ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                    
                    <button type="submit" class="py-2 px-3 bg-indigo-600 text-white text-sm rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <i class="fas fa-filter mr-1"></i> Lọc
                    </button>
                </form>
                
                <!-- Nút xuất Excel -->
                <div class="relative">
                    <button id="exportBtn" class="inline-flex items-center py-2 px-3 bg-green-600 text-white text-sm rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                        <i class="fas fa-file-excel mr-1"></i> Xuất Excel
                    </button>
                    <div id="exportMenu" class="hidden absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                        <div class="py-1" role="menu" aria-orientation="vertical">
                            <a href="/public/?route=dashboard&action=export_excel&type=users&year=<?= $stats['year'] ?>&month=<?= $stats['month'] ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
                                <i class="fas fa-users mr-2"></i> Người dùng mới
                            </a>
                            <a href="/public/?route=dashboard&action=export_excel&type=revenue&year=<?= $stats['year'] ?>&month=<?= $stats['month'] ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
                                <i class="fas fa-money-bill-wave mr-2"></i> Doanh thu
                            </a>
                            <a href="/public/?route=dashboard&action=export_excel&type=sources&year=<?= $stats['year'] ?>&month=<?= $stats['month'] ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
                                <i class="fas fa-share-alt mr-2"></i> Nguồn đăng ký
                            </a>
                            <a href="/public/?route=dashboard&action=export_excel&type=payment_sources&year=<?= $stats['year'] ?>&month=<?= $stats['month'] ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100" role="menuitem">
                                <i class="fas fa-credit-card mr-2"></i> Nguồn thanh toán
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Thông tin thời gian -->
    <div class="mb-6 text-sm text-gray-500">
        Phân tích dữ liệu từ <strong><?= date('d/m/Y', strtotime($stats['startDate'])) ?></strong> đến <strong><?= date('d/m/Y', strtotime($stats['endDate'])) ?></strong>
</div>

<!-- Thống kê theo tháng đã chọn -->
    <div class="dashboard-section p-6 mb-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-calendar-alt mr-2 text-indigo-600"></i>
            Thống kê theo tháng đã chọn
        </h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Tổng số người chơi đăng ký mới -->
            <div class="stat-card bg-gradient-to-br from-indigo-50 to-indigo-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-indigo-500 to-indigo-600 text-white">
                        <i class="fas fa-user-plus text-xl"></i>
                </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-indigo-100 text-indigo-800">
                        <i class="fas fa-user-plus mr-1"></i> Đăng ký
                            </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Người dùng mới trong tháng</div>
                    <div class="stat-value text-indigo-700">
                        <?= number_format($stats['totalRegistrations']) ?>
                        <?php if ($stats['userGrowth'] != 0): ?>
                            <span class="text-sm font-medium ml-2 <?= $stats['userGrowth'] > 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['userGrowth'] > 0 ? '↑' : '↓' ?> <?= number_format(abs($stats['userGrowth']), 1) ?>%
                            </span>
                        <?php endif; ?>
            </div>
                    <div class="text-xs text-gray-500 mt-1">So với tháng trước: <?= number_format($stats['prevMonthRegistrations']) ?></div>
        </div>
    </div>

    <!-- Doanh thu trong tháng -->
            <div class="stat-card bg-gradient-to-br from-yellow-50 to-yellow-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-yellow-500 to-yellow-600 text-white">
                        <i class="fas fa-money-bill-wave text-xl"></i>
                </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-yellow-100 text-yellow-800">
                        <i class="fas fa-coins mr-1"></i> Doanh thu
                            </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Doanh thu trong tháng</div>
                    <div class="stat-value text-yellow-700">
                        <?= number_format($stats['totalRevenue']) ?> <span class="text-sm font-medium">VNĐ</span>
                        <?php if ($stats['revenueGrowth'] != 0): ?>
                            <span class="text-sm font-medium ml-2 <?= $stats['revenueGrowth'] > 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['revenueGrowth'] > 0 ? '↑' : '↓' ?> <?= number_format(abs($stats['revenueGrowth']), 1) ?>%
                            </span>
                        <?php endif; ?>
            </div>
                    <div class="text-xs text-gray-500 mt-1">So với tháng trước: <?= number_format($stats['prevMonthRevenue']) ?> VNĐ</div>
        </div>
    </div>

    <!-- Giao dịch thành công -->
            <div class="stat-card bg-gradient-to-br from-green-50 to-green-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-green-500 to-green-600 text-white">
                        <i class="fas fa-check-circle text-xl"></i>
                </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-green-100 text-green-800">
                        <i class="fas fa-check mr-1"></i> Thành công
                            </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Giao dịch thành công</div>
                    <div class="stat-value text-green-700"><?= number_format($stats['successTransactions']) ?></div>
        </div>
    </div>
    
    <!-- Tỉ lệ người dùng thanh toán (Rate Pay) -->
            <div class="stat-card bg-gradient-to-br from-blue-50 to-blue-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-blue-500 to-blue-600 text-white">
                        <i class="fas fa-percentage text-xl"></i>
                </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-blue-100 text-blue-800">
                        <i class="fas fa-chart-pie mr-1"></i> Tỉ lệ
                            </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">Rate Pay</div>
                    <div class="stat-value text-blue-700"><?= number_format($stats['payingRate'], 2) ?>%</div>
        </div>
    </div>

    <!-- ARPU -->
            <div class="stat-card bg-gradient-to-br from-red-50 to-red-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-red-500 to-red-600 text-white">
                        <i class="fas fa-credit-card text-xl"></i>
                </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-red-100 text-red-800">
                        <i class="fas fa-calculator mr-1"></i> ARPU
                            </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">ARPU</div>
                    <div class="stat-value text-red-700"><?= number_format($stats['arpu']) ?> <span class="text-sm font-medium">VNĐ</span></div>
            </div>
        </div>

            <!-- ARPPU -->
            <div class="stat-card bg-gradient-to-br from-violet-50 to-violet-100 p-5">
                <div class="flex items-center justify-between">
                    <div class="stat-icon bg-gradient-to-r from-violet-500 to-violet-600 text-white">
                        <i class="fas fa-gem text-xl"></i>
                    </div>
                    <div class="text-xs font-medium px-2 py-1 rounded-full bg-violet-100 text-violet-800">
                        <i class="fas fa-calculator mr-1"></i> ARPPU
                    </div>
                </div>
                <div class="mt-4">
                    <div class="stat-label">ARPPU</div>
                    <div class="stat-value text-violet-700"><?= number_format($stats['arppu']) ?> <span class="text-sm font-medium">VNĐ</span></div>
                    <div class="text-xs text-gray-500 mt-1">Doanh thu trung bình trên mỗi người trả phí</div>
                </div>
            </div>
    </div>
</div>

<!-- Biểu đồ tổng quan -->
    <div class="dashboard-section p-6 mb-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-chart-area mr-2 text-indigo-600"></i>
            Biểu đồ tổng quan
        </h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Biểu đồ đăng ký người dùng mới và tỉ lệ quay lại -->
            <div class="chart-container bg-white p-5 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-user-clock mr-2 text-indigo-600"></i>
                    Người dùng mới & tỉ lệ quay lại
                </h3>
        <div class="relative" style="height: 300px;">
            <canvas id="newUsersRetentionChart"></canvas>
        </div>
    </div>

    <!-- Biểu đồ doanh thu theo ngày -->
            <div class="chart-container bg-white p-5 border border-gray-200">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-coins mr-2 text-yellow-500"></i>
                    Doanh thu theo ngày
                </h3>
        <div class="relative" style="height: 300px;">
            <canvas id="dailyRevenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Biểu đồ doanh thu theo server -->
    <div class="dashboard-section p-6 mb-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-chart-line mr-2 text-indigo-600"></i>
            Doanh thu theo máy chủ
        </h2>

        <div class="bg-white p-5 rounded-xl shadow-md">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
                <h4 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-server mr-2 text-indigo-600"></i>
                    Biểu đồ doanh thu theo máy chủ
                </h4>

                <div class="flex flex-wrap gap-2 mt-2 sm:mt-0">
                    <div class="relative">
                        <select id="chartViewType" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5 pr-8">
                            <option value="daily">Theo ngày</option>
                            <option value="weekly">Theo tuần</option>
                            <option value="monthly">Theo tháng</option>
                        </select>
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <i class="fas fa-chevron-down text-xs"></i>
                        </div>
                    </div>

                    <div class="relative">
                        <input type="text" id="dateRangePicker" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full p-2.5" placeholder="Chọn khoảng thời gian">
                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <i class="fas fa-calendar text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Server Selection -->
            <div class="mb-4">
                <h5 class="text-sm font-medium text-gray-700 mb-2">Chọn máy chủ để hiển thị:</h5>
                <div class="flex flex-wrap gap-2 max-h-32 overflow-y-auto p-2 border border-gray-200 rounded-lg server-selection-container">
                    <?php foreach ($stats['allServers'] as $index => $server): ?>
                        <label class="inline-flex items-center px-3 py-1.5 bg-gray-100 hover:bg-gray-200 rounded-full cursor-pointer text-sm">
                            <input type="checkbox" class="server-checkbox form-checkbox h-4 w-4 text-indigo-600 rounded mr-2" value="<?= $server['server_id'] ?>" <?= $index < 5 ? 'checked' : '' ?>>
                            <span>Server <?= $server['server_id'] ?></span>
                            <span class="text-xs text-gray-500 ml-1">(<?= number_format($server['transaction_count']) ?>)</span>
                        </label>
                    <?php endforeach; ?>
                </div>

                <!-- Server search for large number of servers -->
                <?php if (count($stats['allServers']) > 20): ?>
                <div class="mt-2 relative">
                    <input type="text" id="serverChartSearchInput" placeholder="Tìm server..." class="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500">
                    <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>

                    <script>
                        document.getElementById('serverChartSearchInput').addEventListener('input', function() {
                            const searchTerm = this.value.toLowerCase();
                            const serverLabels = document.querySelectorAll('.server-selection-container label');

                            serverLabels.forEach(label => {
                                const serverText = label.textContent.toLowerCase();
                                if (serverText.includes(searchTerm)) {
                                    label.style.display = '';
                                } else {
                                    label.style.display = 'none';
                                }
                            });
                        });
                    </script>
                </div>
                <?php endif; ?>
            </div>

            <!-- Revenue Chart -->
            <div class="mt-4">
                <canvas id="revenueByServerChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Phân tích hành vi chi trả theo cấp độ -->
    <div class="dashboard-section p-6 mb-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-level-up-alt mr-2 text-indigo-600"></i>
            Phân tích hành vi chi trả theo cấp độ
        </h2>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
            <!-- Cấp độ chi trả đầu tiên -->
            <div class="stat-card bg-gradient-to-br from-cyan-50 to-cyan-100 p-5">
                <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-level-up-alt mr-2 text-cyan-600"></i>
                    Cấp độ chi trả đầu tiên
                </h3>
                <div class="grid grid-cols-3 gap-3">
                    <div class="bg-cyan-50 p-3 rounded-lg text-center">
                        <div class="text-xs text-gray-500">Trung bình</div>
                        <div class="text-xl font-bold text-cyan-700"><?= number_format($stats['avgFirstPaymentLevel'], 1) ?></div>
                    </div>
                    <div class="bg-cyan-50 p-3 rounded-lg text-center">
                        <div class="text-xs text-gray-500">Thấp nhất</div>
                        <div class="text-xl font-bold text-cyan-700"><?= number_format($stats['minFirstPaymentLevel']) ?></div>
                    </div>
                    <div class="bg-cyan-50 p-3 rounded-lg text-center">
                        <div class="text-xs text-gray-500">Cao nhất</div>
                        <div class="text-xl font-bold text-cyan-700"><?= number_format($stats['maxFirstPaymentLevel']) ?></div>
                    </div>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p>Cấp độ trung bình khi người chơi bắt đầu chi tiền lần đầu tiên.</p>
                </div>
            </div>

            <!-- Chi tiêu theo nhóm cấp độ -->
            <div class="stat-card bg-gradient-to-br from-blue-50 to-blue-100 p-5 col-span-2">
                <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-chart-bar mr-2 text-blue-600"></i>
                    Chi tiêu theo nhóm cấp độ
                </h3>

                <?php if (!empty($stats['paymentByLevel'])): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-3 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cấp độ</th>
                                <th class="px-3 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số người</th>
                                <th class="px-3 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Giao dịch</th>
                                <th class="px-3 py-2 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng chi tiêu</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php
                            $totalAmount = array_sum(array_column($stats['paymentByLevel'], 'total_amount'));
                            foreach ($stats['paymentByLevel'] as $index => $payment):
                                $avgSpend = $payment['unique_payers'] > 0 ? $payment['total_amount'] / $payment['unique_payers'] : 0;
                                $percentOfTotal = $totalAmount > 0 ? ($payment['total_amount'] / $totalAmount) * 100 : 0;
                            ?>
                            <tr class="<?= $index % 2 === 0 ? 'bg-white' : 'bg-gray-50'; ?>">
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">Cấp <?= $payment['level_range'] ?></div>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= number_format($payment['unique_payers']) ?></div>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= number_format($payment['transaction_count']) ?></div>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= number_format($payment['total_amount']) ?></div>
                                    <div class="text-xs text-gray-500"><?= number_format($percentOfTotal, 1) ?>% tổng doanh thu</div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4 text-gray-500">
                    <i class="fas fa-info-circle text-blue-400 text-xl mb-2"></i>
                    <p>Không có dữ liệu chi tiêu theo cấp độ</p>
                </div>
                <?php endif; ?>
        </div>
    </div>
</div>

<!-- Thống kê chi tiết -->
    <div class="dashboard-section p-6 mb-6">
        <h2 class="section-title flex items-center">
            <i class="fas fa-list-alt mr-2 text-indigo-600"></i>
            Thống kê chi tiết
        </h2>
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Thống kê theo nguồn đăng ký -->
            <div class="stat-card bg-gradient-to-br from-indigo-50 to-indigo-100 p-5">
                <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-share-alt mr-2 text-indigo-600"></i>
                    Người dùng theo nguồn đăng ký
                </h3>
        <?php
        // Lấy dữ liệu cho biểu đồ nguồn đăng ký người dùng
        $sourceData = $db->fetchAll("
            SELECT 
                COALESCE(register_source, '') as register_source,
                COUNT(*) as count 
            FROM game_users 
            WHERE created_at >= ? AND created_at <= ?
            GROUP BY register_source 
            ORDER BY count DESC 
            LIMIT 5
        ", [$stats['startDate'] . ' 00:00:00', $stats['endDate'] . ' 23:59:59']);

                                
        $totalSourceUsers = 0;
        foreach ($sourceData as $row) {
            $totalSourceUsers += $row['count'];
        }
        ?>
        <div class="flex flex-col space-y-4">
            <?php foreach ($sourceData as $row): 
                $sourceName = $row['register_source'] ?: 'Không xác định';
                $sourceCount = $row['count'];
                $sourcePercent = $totalSourceUsers > 0 ? round(($sourceCount / $totalSourceUsers) * 100, 1) : 0;
                $bgColor = 'bg-indigo-500';
                if (stripos($sourceName, 'facebook') !== false) {
                    $bgColor = 'bg-blue-600';
                } elseif (stripos($sourceName, 'google') !== false) {
                    $bgColor = 'bg-red-500';
                        } elseif (stripos($sourceName, 'ios') !== false) {
                    $bgColor = 'bg-gray-800';
                } elseif (stripos($sourceName, 'android') !== false) {
                    $bgColor = 'bg-green-600';
                } elseif (stripos($sourceName, 'web') !== false) {
                    $bgColor = 'bg-yellow-500';
                }
            ?>
            <div>
                <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium text-gray-700 truncate"><?= htmlspecialchars($sourceName) ?></span>
                    <span class="text-sm font-medium text-gray-700"><?= number_format($sourceCount) ?> (<?= $sourcePercent ?>%)</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="h-2.5 rounded-full <?= $bgColor ?>" style="width: <?= $sourcePercent ?>%"></div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php if (empty($sourceData)): ?>
            <div class="py-8 text-center text-gray-500">Không có dữ liệu</div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Thống kê theo nguồn thanh toán -->
            <div class="stat-card bg-gradient-to-br from-purple-50 to-purple-100 p-5">
                <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-credit-card mr-2 text-purple-600"></i>
                    Thanh toán theo nguồn
                </h3>
        <?php
        // Phân tích doanh thu theo nguồn
        $totalSourceAmount = 0;
        foreach ($stats['revenueBySource'] as $row) {
            $totalSourceAmount += $row['total'];
        }
        ?>
        <div class="flex flex-col space-y-4">
            <?php foreach ($stats['revenueBySource'] as $row): 
                $sourceName = $row['source'];
                $sourceCount = $row['count'];
                $sourceAmount = $row['total'];
                $sourcePercent = $totalSourceAmount > 0 ? round(($sourceAmount / $totalSourceAmount) * 100, 1) : 0;
                $bgColor = 'bg-purple-500';
                if ($sourceName === 'ios') {
                    $bgColor = 'bg-gray-800';
                } elseif ($sourceName === 'android') {
                    $bgColor = 'bg-green-600';
                } elseif ($sourceName === 'web') {
                    $bgColor = 'bg-yellow-500';
                } elseif ($sourceName === 'facebook') {
                    $bgColor = 'bg-blue-600';
                } elseif ($sourceName === 'google') {
                    $bgColor = 'bg-red-500';
                } elseif ($sourceName === 'guest') {
                    $bgColor = 'bg-gray-500';
                }
            ?>
            <div>
                <div class="flex justify-between items-center mb-1">
                    <div class="flex items-center">
                        <span class="text-sm font-medium text-gray-700 capitalize"><?= htmlspecialchars($sourceName) ?></span>
                        <span class="ml-2 text-xs text-gray-500">(<?= number_format($sourceCount) ?> giao dịch)</span>
                    </div>
                    <span class="text-sm font-medium text-gray-700"><?= $sourcePercent ?>%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="h-2.5 rounded-full <?= $bgColor ?>" style="width: <?= $sourcePercent ?>%"></div>
                </div>
                <div class="mt-1 text-right text-xs text-gray-500"><?= number_format($sourceAmount) ?> VNĐ</div>
            </div>
            <?php endforeach; ?>
            <?php if (empty($stats['revenueBySource'])): ?>
            <div class="py-8 text-center text-gray-500">Không có dữ liệu</div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Thống kê theo trạng thái thanh toán -->
            <div class="stat-card bg-gradient-to-br from-green-50 to-green-100 p-5">
                <h3 class="text-base font-semibold text-gray-800 mb-4 flex items-center">
                    <i class="fas fa-tasks mr-2 text-green-600"></i>
                    Trạng thái thanh toán
                </h3>
        <?php
        // Trạng thái thanh toán
        $totalPayments = 0;
        $successTotal = 0;
        $pendingTotal = 0;
        $errorTotal = 0;
        
        foreach ($stats['paymentStatus'] as $row) {
            $totalPayments += $row['count'];
            if ($row['status'] === 'success') {
                $successTotal = $row['count'];
            } elseif ($row['status'] === 'pending') {
                $pendingTotal = $row['count'];
            } elseif ($row['status'] === 'error') {
                $errorTotal = $row['count'];
            }
        }
        
        $successPercent = $totalPayments > 0 ? round(($successTotal / $totalPayments) * 100, 1) : 0;
        $pendingPercent = $totalPayments > 0 ? round(($pendingTotal / $totalPayments) * 100, 1) : 0;
        $errorPercent = $totalPayments > 0 ? round(($errorTotal / $totalPayments) * 100, 1) : 0;
        ?>
        
        <div class="mb-6">
            <div class="flex justify-center mb-2">
                <div class="inline-flex rounded-full items-center justify-center h-20 w-20 border-8 border-gray-200">
                            <span class="text-xl font-bold text-green-700"><?= $successPercent ?>%</span>
                </div>
            </div>
            <div class="text-center text-sm text-gray-600">Tỉ lệ thanh toán thành công</div>
        </div>
        
        <div class="flex flex-col space-y-4">
            <div>
                <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium text-gray-700">Thành công</span>
                    <span class="text-sm font-medium text-gray-700"><?= number_format($successTotal) ?> (<?= $successPercent ?>%)</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="h-2.5 rounded-full bg-green-600" style="width: <?= $successPercent ?>%"></div>
                </div>
            </div>
            
            <div>
                <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium text-gray-700">Đang xử lý</span>
                    <span class="text-sm font-medium text-gray-700"><?= number_format($pendingTotal) ?> (<?= $pendingPercent ?>%)</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="h-2.5 rounded-full bg-yellow-500" style="width: <?= $pendingPercent ?>%"></div>
                </div>
            </div>
            
            <div>
                <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium text-gray-700">Lỗi</span>
                    <span class="text-sm font-medium text-gray-700"><?= number_format($errorTotal) ?> (<?= $errorPercent ?>%)</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5">
                    <div class="h-2.5 rounded-full bg-red-600" style="width: <?= $errorPercent ?>%"></div>
                </div>
            </div>
            
            <div class="mt-2 text-center text-sm text-gray-600">
                Tổng số: <?= number_format($totalPayments) ?> giao dịch
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Khởi tạo biểu đồ người dùng mới và tỉ lệ quay lại
document.addEventListener('DOMContentLoaded', function() {
    // Cấu hình menu xuất Excel
    const exportBtn = document.getElementById('exportBtn');
    const exportMenu = document.getElementById('exportMenu');
    
    if (exportBtn && exportMenu) {
        exportBtn.addEventListener('click', function() {
            exportMenu.classList.toggle('hidden');
        });
        
        // Đóng menu khi click ra ngoài
        document.addEventListener('click', function(event) {
            if (!exportBtn.contains(event.target) && !exportMenu.contains(event.target)) {
                exportMenu.classList.add('hidden');
            }
        });
    }
    
    // Lấy dữ liệu cho biểu đồ người dùng mới và tỉ lệ quay lại
    fetch(`/public/?route=dashboard&action=get_chart_data&type=users&year=<?= $stats['year'] ?>&month=<?= $stats['month'] ?>`)
        .then(response => response.json())
        .then(data => {
            // Khởi tạo biểu đồ người dùng mới và tỉ lệ quay lại
            const ctx1 = document.getElementById('newUsersRetentionChart').getContext('2d');
            new Chart(ctx1, {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                callback: function(value, index, values) {
                                    // Hiển thị ngày dưới dạng DD/MM
                                    const date = new Date(this.getLabelForValue(value));
                                    return date.getDate() + '/' + (date.getMonth() + 1);
                                }
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Người dùng mới'
                            },
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Tỉ lệ quay lại (%)'
                            },
                            min: 0,
                            max: 100,
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.dataset.yAxisID === 'y') {
                                        label += context.parsed.y.toLocaleString() + ' người dùng';
                                    } else {
                                        label += context.parsed.y.toLocaleString() + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        });
    
    // Lấy dữ liệu cho biểu đồ doanh thu theo ngày
    fetch(`/public/?route=dashboard&action=get_chart_data&type=revenue&year=<?= $stats['year'] ?>&month=<?= $stats['month'] ?>`)
        .then(response => response.json())
        .then(data => {
            // Khởi tạo biểu đồ doanh thu theo ngày
            const ctx2 = document.getElementById('dailyRevenueChart').getContext('2d');
            new Chart(ctx2, {
                type: 'bar',
                data: {
                    labels: data.labels,
                    datasets: data.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 45,
                                callback: function(value, index, values) {
                                    // Hiển thị ngày dưới dạng DD/MM
                                    const date = new Date(this.getLabelForValue(value));
                                    return date.getDate() + '/' + (date.getMonth() + 1);
                                }
                            }
                        },
                        y: {
                            beginAtZero: true,
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Doanh thu (VNĐ)'
                            },
                            ticks: {
                                callback: function(value, index, values) {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toLocaleString() + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toLocaleString() + 'K';
                                    } else {
                                        return value;
                                    }
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Số giao dịch'
                            },
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.dataset.yAxisID === 'y') {
                                        label += context.parsed.y.toLocaleString() + ' VNĐ';
                                    } else {
                                        label += context.parsed.y.toLocaleString() + ' giao dịch';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
        });

    // Khởi tạo biểu đồ role online
    const onlineCtx = document.getElementById('onlineChart').getContext('2d');
    new Chart(onlineCtx, {
        type: 'bar',
        data: <?= json_encode($stats['onlineChartData']) ?>,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Số role online'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Giờ'
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.parsed.y.toLocaleString() + ' role';
                        }
                    }
                }
            }
        }
    });

    // Khởi tạo biểu đồ doanh thu theo server
    if (document.getElementById('revenueByServerChart')) {
        const revenueByServerCtx = document.getElementById('revenueByServerChart').getContext('2d');
        const revenueByServerData = <?= json_encode($stats['revenueByServerChartData']) ?>;

        const revenueByServerChart = new Chart(revenueByServerCtx, {
            type: 'line',
            data: revenueByServerData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Doanh thu (VNĐ)'
                        },
                        ticks: {
                            callback: function(value, index, values) {
                                if (value >= 1000000) {
                                    return (value / 1000000).toLocaleString() + 'M';
                                } else if (value >= 1000) {
                                    return (value / 1000).toLocaleString() + 'K';
                                } else {
                                    return value;
                                }
                            }
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Ngày'
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    },
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            }
        });

        // Server selection for revenue chart
        const serverCheckboxes = document.querySelectorAll('.server-checkbox');
        serverCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const serverId = this.value;
                const isChecked = this.checked;

                // Find the dataset for this server
                const datasetIndex = revenueByServerChart.data.datasets.findIndex(
                    dataset => dataset.label === `Server ${serverId}`
                );

                if (datasetIndex !== -1) {
                    // If dataset exists, toggle its visibility
                    revenueByServerChart.setDatasetVisibility(
                        datasetIndex,
                        isChecked
                    );
                }

                revenueByServerChart.update();
            });
        });

        // Chart view type (daily, weekly, monthly)
        document.getElementById('chartViewType').addEventListener('change', function() {
            const viewType = this.value;
            // This would require backend changes to implement properly
            // For now, we'll just show an alert
            alert(`Chế độ xem ${viewType} sẽ được triển khai trong phiên bản tiếp theo.`);
        });

        // Date range picker (placeholder for future implementation)
        document.getElementById('dateRangePicker').addEventListener('click', function() {
            alert('Chức năng chọn khoảng thời gian sẽ được triển khai trong phiên bản tiếp theo.');
        });
    }
});

// Modal functions
function openRevenueServerModal() {
    document.getElementById('revenueServerModal').classList.add('show');
}

function closeRevenueServerModal() {
    document.getElementById('revenueServerModal').classList.remove('show');
}

// Đóng modal khi click ra ngoài
document.getElementById('revenueServerModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRevenueServerModal();
    }
});

// Revenue server search functionality
document.getElementById('revenueServerSearchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const serverItems = document.querySelectorAll('.revenue-server-item');

    serverItems.forEach(item => {
        const serverId = item.getAttribute('data-server-id');
        if (serverId.includes(searchTerm)) {
            item.style.display = '';
        } else {
            item.style.display = 'none';
        }
        });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 