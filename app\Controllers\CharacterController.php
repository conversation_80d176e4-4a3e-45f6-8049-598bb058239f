<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Core\GameDatabaseManager;

class CharacterController extends Controller {

    private $serverConfig;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->serverConfig = require __DIR__ . '/../../config/server.php';
        $this->initGameLogDb();
    }

    public function index() {
        $this->requireLogin();
        if (!$this->hasPermission('character.view')) {
            $this->redirectToFirstAccessiblePage();
        }
        // $this->setViewConfig([
        //     'container' => 'max-w-full',
        //     'padding' => 'py-4 px-4'
        // ]);
        $paginationConfig = require __DIR__ . '/../../config/character_pagination.php';
        $perPage = $paginationConfig['per_page'] ?? 20;
        $searchFields = $paginationConfig['search_fields'] ?? [];
        $keyword = trim($_GET['keyword'] ?? '');
        $page = max(1, intval($_GET['page'] ?? 1));
        $servers = $this->db->getServers();
        $groupedServers = [];
        foreach ($this->serverConfig['server_groups'] as $group) {
            $groupServers = [];
            $serversPerGroup = $group['servers_per_group'];
            $groupServers = array_filter($servers, function($server) use ($group) {
                return $server['id'] >= $group['start_id'] && $server['id'] <= $group['end_id'];
            });
            usort($groupServers, function($a, $b) {
                return $a['id'] - $b['id'];
            });
            $subGroups = array_chunk($groupServers, $serversPerGroup);
            foreach ($subGroups as $index => $subGroup) {
                $startId = $subGroup[0]['id'];
                $endId = end($subGroup)['id'];
                $groupedServers[] = [
                    'start_id' => $startId,
                    'end_id' => $endId,
                    'servers' => $subGroup
                ];
            }
        }
        $characters = [];
        $totalCharacters = 0;
        $totalPages = 1;
        if (isset($_GET['server_id'])) {
            $serverId = (int)$_GET['server_id'];
            $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
            if ($this->checkGameDbConnections($connectResults, false)) {
                $fields = ['role_id', 'role_name', 'level', 'create_time', 'online_time', 'is_online', 'vip_level'];
                $where = ['role_id>0'];
                $params = [];
                $joinPlatUser = false;
                if ($keyword !== '') {
                    // Tìm role_id hoặc role_name hoặc plat_user_name
                    $where[] = '(role_id LIKE ? OR role_name LIKE ? OR role_id IN (SELECT role_id FROM role_name_map WHERE plat_user_name LIKE ?))';
                    $params[] = "%$keyword%";
                    $params[] = "%$keyword%";
                    $params[] = "%$keyword%";
                    $joinPlatUser = true;
                }
                $whereStr = implode(' AND ', $where);

                // Truy vấn lấy toàn bộ role cho thống kê
                $sqlAll = $this->gameSqlBuilder->buildSelect('role', $whereStr, $params, $fields, 'create_time DESC');
                $resultAll = $this->gameDatabaseManager->executeOnServer($serverId, $sqlAll['sql'], $sqlAll['params']);

                if ($resultAll['success']) {
                    $allCharacters = $resultAll['data'];
                    $totalCharacters = count($allCharacters);

                    // Lấy tổng doanh thu từ payment_transactions
                    $sql = "SELECT
                        SUM(amount) as total_amount,
                        SUM(CASE WHEN DATE(created_at) = CURDATE() THEN amount ELSE 0 END) as today_amount,
                        COUNT(DISTINCT user_id) as total_payers,
                        COUNT(*) as total_orders
                    FROM payment_transactions
                    WHERE server_id = ? AND status = 'success'";
                    $revenueResult = $this->db->fetch($sql, [$serverId]);
                    $totalRevenue = $revenueResult['total_amount'] ?? 0;
                    $todayRevenue = $revenueResult['today_amount'] ?? 0;
                    $totalPayers = $revenueResult['total_payers'] ?? 0;
                    $totalOrders = $revenueResult['total_orders'] ?? 0;

                    // Tính ARPU (Average Revenue Per User)
                    $arpu = $totalCharacters > 0 ? round($totalRevenue / $totalCharacters) : 0;

                    // Tính ARPPU (Average Revenue Per Paying User)
                    $arppu = $totalPayers > 0 ? round($totalRevenue / $totalPayers) : 0;

                    // Lấy thống kê online từ bảng login
                    $sql = $this->gameSqlBuilder->buildSelect(
                        'login',
                        'lastlogintime >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 24 HOUR))',
                        [],
                        ['COUNT(DISTINCT plat_user_name) as total_online'],
                        ''
                    );

                    $onlineResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);
                    $onlineCount = $onlineResult['data'][0]['total_online'] ?? 0;

                    // Lấy thời gian online trung bình từ bảng role
                    $sql = $this->gameSqlBuilder->buildSelect(
                        'role',
                        'role_id>0',
                        [],
                        ['AVG(online_time) as avg_online_time', 'MAX(level) as max_level', 'AVG(level) as avg_level', 'MAX(vip_level) as max_vip', 'AVG(vip_level) as avg_vip'],
                        ''
                    );
                    $avgTimeResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);
                    $avgOnlineTime = round(($avgTimeResult['data'][0]['avg_online_time'] ?? 0) / 3600, 1); // Chuyển giây sang giờ
                    $maxLevel = $avgTimeResult['data'][0]['max_level'] ?? 0;
                    $avgLevel = round($avgTimeResult['data'][0]['avg_level'] ?? 0, 1);
                    $maxVip = $avgTimeResult['data'][0]['max_vip'] ?? 0;
                    $avgVip = round($avgTimeResult['data'][0]['avg_vip'] ?? 0, 1);

                    // Tính tỷ lệ active
                    $activeRate = $totalCharacters > 0 ? round(($onlineCount / $totalCharacters) * 100, 1) : 0;

                    // Lấy thống kê người chơi theo cấp độ (tất cả cấp độ)
                    $sql = $this->gameSqlBuilder->buildSelect(
                        'role',
                        'role_id>0',
                        [],
                        ['level', 'COUNT(*) as count'],
                        'level ASC'
                    );
                    $levelDistResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);
                    $allLevelData = $levelDistResult['success'] ? $levelDistResult['data'] : [];

                    // Nhóm cấp độ theo khoảng (1-10, 11-20, 21-30, ...)
                    $levelGroups = [
                        '1-10' => 0,
                        '11-20' => 0,
                        '21-30' => 0,
                        '31-40' => 0,
                        '41-50' => 0,
                        '51-60' => 0,
                        '61-70' => 0,
                        '71-80' => 0,
                        '81-90' => 0,
                        '91+' => 0
                    ];

                    foreach ($allLevelData as $data) {
                        $level = (int)$data['level'];
                        $count = (int)$data['count'];

                        if ($level <= 10) {
                            $levelGroups['1-10'] += $count;
                        } elseif ($level <= 20) {
                            $levelGroups['11-20'] += $count;
                        } elseif ($level <= 30) {
                            $levelGroups['21-30'] += $count;
                        } elseif ($level <= 40) {
                            $levelGroups['31-40'] += $count;
                        } elseif ($level <= 50) {
                            $levelGroups['41-50'] += $count;
                        } elseif ($level <= 60) {
                            $levelGroups['51-60'] += $count;
                        } elseif ($level <= 70) {
                            $levelGroups['61-70'] += $count;
                        } elseif ($level <= 80) {
                            $levelGroups['71-80'] += $count;
                        } elseif ($level <= 90) {
                            $levelGroups['81-90'] += $count;
                        } else {
                            $levelGroups['91+'] += $count;
                        }
                    }

                    // Chuyển đổi thành mảng để hiển thị
                    $levelDistribution = [];
                    foreach ($levelGroups as $range => $count) {
                        if ($count > 0) {
                            $levelDistribution[] = [
                                'level_range' => $range,
                                'count' => $count
                            ];
                        }
                    }

                    // Lấy thống kê người chơi theo VIP (tất cả cấp VIP)
                    $sql = $this->gameSqlBuilder->buildSelect(
                        'role',
                        'role_id>0',
                        [],
                        ['vip_level', 'COUNT(*) as count'],
                        'vip_level ASC'
                    );
                    $vipDistResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);
                    $allVipData = $vipDistResult['success'] ? $vipDistResult['data'] : [];

                    // Nhóm VIP theo khoảng (VIP 0, VIP 1-3, VIP 4-6, VIP 7+)
                    $vipGroups = [
                        'VIP 0' => 0,
                        'VIP 1-3' => 0,
                        'VIP 4-6' => 0,
                        'VIP 7+' => 0
                    ];

                    foreach ($allVipData as $data) {
                        $vip = (int)$data['vip_level'];
                        $count = (int)$data['count'];

                        if ($vip == 0) {
                            $vipGroups['VIP 0'] += $count;
                        } elseif ($vip >= 1 && $vip <= 3) {
                            $vipGroups['VIP 1-3'] += $count;
                        } elseif ($vip >= 4 && $vip <= 6) {
                            $vipGroups['VIP 4-6'] += $count;
                        } else {
                            $vipGroups['VIP 7+'] += $count;
                        }
                    }

                    // Chuyển đổi thành mảng để hiển thị
                    $vipDistribution = [];
                    foreach ($vipGroups as $range => $count) {
                        if ($count > 0) {
                            $vipDistribution[] = [
                                'vip_range' => $range,
                                'count' => $count
                            ];
                        }
                    }

                    // Lấy thống kê người chơi mới theo ngày (7 ngày gần nhất)
                    $sql = "SELECT FROM_UNIXTIME(create_time, '%Y-%m-%d') as date, COUNT(*) as count
                           FROM role
                           WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 7 DAY))
                           GROUP BY date
                           ORDER BY date DESC";
                    $newPlayersResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql, []);
                    $newPlayersData = $newPlayersResult['success'] ? $newPlayersResult['data'] : [];

                    // Lấy thống kê top người chơi nạp tiền
                    $sql = "SELECT r.role_id, r.role_name, r.level, r.vip_level, a.gold_history
                           FROM role r
                           JOIN role_name_map m ON r.role_id = m.role_id
                           JOIN accountgold a ON m.plat_user_name = a.plat_user_name
                           WHERE a.gold_history > 0
                           ORDER BY a.gold_history DESC
                           LIMIT 5";
                    $topPayersResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql, []);
                    $topPayers = $topPayersResult['success'] ? $topPayersResult['data'] : [];

                    // Phân tích hành vi chi trả theo cấp độ
                    $sql = "SELECT
                           AVG(game_level) as avg_first_payment_level,
                           MIN(game_level) as min_payment_level,
                           MAX(game_level) as max_payment_level,
                           COUNT(*) as total_payments
                           FROM (
                               SELECT user_id, MIN(game_level) as game_level
                               FROM payment_transactions
                               WHERE server_id = ? AND status = 'success'
                               GROUP BY user_id
                           ) as first_payments";
                    $paymentLevelResult = $this->db->fetch($sql, [$serverId]);
                    $avgFirstPaymentLevel = round($paymentLevelResult['avg_first_payment_level'] ?? 0, 1);
                    $minPaymentLevel = $paymentLevelResult['min_payment_level'] ?? 0;
                    $maxPaymentLevel = $paymentLevelResult['max_payment_level'] ?? 0;

                    // Phân tích chi tiêu theo nhóm cấp độ
                    $sql = "SELECT
                           CASE
                               WHEN game_level <= 10 THEN '1-10'
                               WHEN game_level <= 20 THEN '11-20'
                               WHEN game_level <= 30 THEN '21-30'
                               WHEN game_level <= 40 THEN '31-40'
                               WHEN game_level <= 50 THEN '41-50'
                               WHEN game_level <= 60 THEN '51-60'
                               WHEN game_level <= 70 THEN '61-70'
                               WHEN game_level <= 80 THEN '71-80'
                               WHEN game_level <= 90 THEN '81-90'
                               ELSE '91+'
                           END as level_range,
                           COUNT(*) as transaction_count,
                           SUM(amount) as total_amount,
                           COUNT(DISTINCT user_id) as unique_payers
                           FROM payment_transactions
                           WHERE server_id = ? AND status = 'success'
                           GROUP BY level_range
                           ORDER BY MIN(game_level)";
                    $paymentByLevelResult = $this->db->fetchAll($sql, [$serverId]);
                    $paymentByLevel = $paymentByLevelResult ?: [];

                    // Lấy thống kê active theo ngày từ bảng login
                    $activeRates = [];
                    for ($i = 2; $i <= 10; $i++) {
                        $sql = "SELECT COUNT(DISTINCT plat_user_name) as active_count
                               FROM login
                               WHERE FROM_UNIXTIME(lastlogintime, '%Y-%m-%d') = DATE_SUB(CURDATE(), INTERVAL ? DAY)";
                        $result = $this->gameDatabaseManager->executeOnServer($serverId, $sql, [$i]);
                        if ($result['success']) {
                            $dayCount = $result['data'][0]['active_count'] ?? 0;
                            $activeRates[$i] = $totalCharacters > 0 ? round(($dayCount / $totalCharacters) * 100, 1) : 0;
                        }
                    }

                    // Truy vấn phân trang cho bảng hiển thị
                    $totalPages = max(1, ceil($totalCharacters / $perPage));
                    $offset = ($page - 1) * $perPage;
                    $sql = $this->gameSqlBuilder->buildSelect('role', $whereStr, $params, $fields, 'level DESC', $perPage, $offset);
                    $result = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);

                    if ($result['success']) {
                        $characters = $result['data'];
                        // Mapping plat_user_name từ role_name_map
                        $map = [];
                        if (!empty($characters)) {
                            $roleIds = array_column($characters, 'role_id');
                            $placeholders = str_repeat('?,', count($roleIds) - 1) . '?';

                            // Lấy plat_user_name và lastlogintime từ role_name_map và login
                            $sqlMap = "SELECT r.role_id, r.plat_user_name, l.lastlogintime
                                     FROM role_name_map r
                                     LEFT JOIN login l ON r.plat_user_name = l.plat_user_name
                                     WHERE r.role_id IN ($placeholders)";
                            $mapRes = $this->gameDatabaseManager->executeOnServer($serverId, $sqlMap, $roleIds);

                            if ($mapRes['success']) {
                                foreach ($mapRes['data'] as $row) {
                                    $map[$row['role_id']] = [
                                        'plat_user_name' => $row['plat_user_name'],
                                        'lastlogintime' => $row['lastlogintime']
                                    ];
                                }
                            }
                            foreach ($characters as &$char) {
                                $char['plat_user_name'] = $map[$char['role_id']]['plat_user_name'] ?? '';
                                $char['lastlogintime'] = date('Y-m-d H:i:s', $map[$char['role_id']]['lastlogintime'] ?? 0);
                            }
                            unset($char);
                        }

                        // Lấy gold_history từ accountgold
                        $platUserNames = array_filter(array_values(array_column($map, 'plat_user_name')));
                        if (!empty($platUserNames)) {
                            $placeholders = str_repeat('?,', count($platUserNames) - 1) . '?';
                            $sql = "SELECT plat_user_name, gold_history FROM accountgold WHERE plat_user_name IN ($placeholders)";
                            $goldResult = $this->gameDatabaseManager->executeOnServer($serverId, $sql, $platUserNames);

                            if ($goldResult['success']) {
                                $goldMap = array_column($goldResult['data'], 'gold_history', 'plat_user_name');
                            }
                        }

                        // Chuyển đổi thời gian và thêm thông tin mới
                        foreach ($characters as &$character) {
                            $character['create_time'] = date('Y-m-d H:i:s', $character['create_time']);
                            $character['online_time'] = $this->formatOnlineTime($character['online_time']);
                            $character['is_online'] = $character['is_online'] ? 'Online' : 'Offline';

                            // Thêm plat_user_name và gold_history
                            if (!isset($character['plat_user_name'])) $character['plat_user_name'] = '';
                            $character['gold_history'] = isset($goldMap[$character['plat_user_name']]) ?
                                number_format($goldMap[$character['plat_user_name']]) : '0';
                        }

                        unset($character);

                    }
                }
                $this->gameDatabaseManager->closeAll();
            }
        }
        $db = $this->db;
        $config = $this->config;
        $serverConfig = $this->serverConfig;

        // Đảm bảo các biến thống kê có giá trị mặc định
        if (!isset($maxLevel)) $maxLevel = 0;
        if (!isset($avgLevel)) $avgLevel = 0;
        if (!isset($maxVip)) $maxVip = 0;
        if (!isset($avgVip)) $avgVip = 0;
        if (!isset($levelDistribution)) $levelDistribution = [];
        if (!isset($vipDistribution)) $vipDistribution = [];
        if (!isset($newPlayersData)) $newPlayersData = [];
        if (!isset($topPayers)) $topPayers = [];
        if (!isset($activeRates)) $activeRates = [];
        if (!isset($totalRevenue)) $totalRevenue = 0;
        if (!isset($todayRevenue)) $todayRevenue = 0;
        if (!isset($totalPayers)) $totalPayers = 0;
        if (!isset($totalOrders)) $totalOrders = 0;
        if (!isset($onlineCount)) $onlineCount = 0;
        if (!isset($activeRate)) $activeRate = 0;
        if (!isset($avgOnlineTime)) $avgOnlineTime = 0;
        if (!isset($arpu)) $arpu = 0;
        if (!isset($arppu)) $arppu = 0;
        if (!isset($avgFirstPaymentLevel)) $avgFirstPaymentLevel = 0;
        if (!isset($minPaymentLevel)) $minPaymentLevel = 0;
        if (!isset($maxPaymentLevel)) $maxPaymentLevel = 0;
        if (!isset($paymentByLevel)) $paymentByLevel = [];

        require_once __DIR__ . '/../../resources/views/character/index.php';
    }

    public function mute() {
        $this->requireLogin();

        if (!$this->hasPermission('character.mute')) {
            $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền thực hiện lệnh này']);
            return;
        }

        $roleId = $_POST['role_id'] ?? null;
        $serverId = $_POST['server_id'] ?? null;
        $duration = $_POST['duration'] ?? 3600;

        if (!$roleId || !$serverId) {
            $this->jsonResponse(['status' => false, 'message' => 'Thiếu thông tin cần thiết']);
            return;
        }

        // Kết nối tới server game
        $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
        if (!$this->checkGameDbConnections($connectResults, false)) {
            $this->jsonResponse(['status' => false, 'message' => 'Không thể kết nối tới server game']);
            return;
        }

        $command = [
            'creator' => 'CharacterController',
            'createtime' => $this->THIS_DATETIME,
            'type' => 2,
            'cmd' => "Mute {$roleId} {$duration}"
        ];

        $insertSql = $this->gameSqlBuilder->buildInsert('command', $command);
        $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);

        $this->gameDatabaseManager->closeAll();

        if ($result['success']) {
            // Log hành động
            $this->db->log('mute_player', 'success', [
                'server_id' => $serverId,
                'role_id' => $roleId,
                'duration' => $duration
            ]);

            $this->jsonResponse(['status' => true, 'message' => 'Lệnh cấm chat đã được thực hiện']);
        } else {
            $this->jsonResponse(['status' => false, 'message' => 'Không thể thực hiện lệnh']);
        }
    }

    public function kick() {
        $this->requireLogin();

        if (!$this->hasPermission('character.kick')) {
            $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền thực hiện lệnh này']);
            return;
        }

        $roleId = $_POST['role_id'] ?? null;
        $serverId = $_POST['server_id'] ?? null;

        if (!$roleId || !$serverId) {
            $this->jsonResponse(['status' => false, 'message' => 'Thiếu thông tin cần thiết']);
            return;
        }

        // Kết nối tới server game
        $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
        if (!$this->checkGameDbConnections($connectResults, false)) {
            $this->jsonResponse(['status' => false, 'message' => 'Không thể kết nối tới server game']);
            return;
        }

        $command = [
            'creator' => 'CharacterController',
            'createtime' => $this->THIS_DATETIME,
            'type' => 2,
            'cmd' => "CmdToRoleKickOut {$roleId}"
        ];

        $insertSql = $this->gameSqlBuilder->buildInsert('command', $command);
        $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);

        $this->gameDatabaseManager->closeAll();

        if ($result['success']) {
            // Log hành động
            $this->db->log('kick_player', 'success', [
                'server_id' => $serverId,
                'role_id' => $roleId
            ]);

            $this->jsonResponse(['status' => true, 'message' => 'Lệnh đá khỏi game đã được thực hiện']);
        } else {
            $this->jsonResponse(['status' => false, 'message' => 'Không thể thực hiện lệnh']);
        }
    }

    public function logs() {
        $this->requireLogin();
        if (!$this->hasPermission('character.view.logs')) {
            $this->redirectToFirstAccessiblePage();
        }
        $this->initItems();
        $tab = $_GET['tab'] ?? 'item';
        $role_id = $_GET['role_id'] ?? '';
        $log_type = $_GET['log_type'] ?? '';
        $action_type = $_GET['action_type'] ?? '';
        $item_id = $_GET['item_id'] ?? '';
        $hero_id = $_GET['hero_id'] ?? '';
        $itemLogs = [];
        $heroLogs = [];
        $itemLogTypes = [];
        $heroLogTypes = [];
        // Điều kiện lọc chung
        $where = [];
        $params = [];
        if ($role_id !== '') {
            $where[] = 'uid = ?';
            $params[] = $role_id;
        }
        if ($log_type !== '') {
            $where[] = 'log_type = ?';
            $params[] = $log_type;
        }
        if ($action_type !== '') {
            $where[] = 'action_type = ?';
            $params[] = $action_type;
        }
        // Lấy danh sách log_type distinct cho select
        if ($role_id !== '') {
            $itemLogTypes = $this->gameLogDb->fetchAll('SELECT DISTINCT log_type FROM item_log WHERE uid = ?', [$role_id]);
            $itemLogTypes = array_column($itemLogTypes, 'log_type');
            $heroLogTypes = $this->gameLogDb->fetchAll('SELECT DISTINCT log_type FROM hero_log WHERE uid = ?', [$role_id]);
            $heroLogTypes = array_column($heroLogTypes, 'log_type');
        }
        // Truy vấn item_log
        if ($tab === 'item') {
            if ($item_id !== '') {
                $where[] = 'item_id = ?';
                $params[] = $item_id;
            }
            $sql = 'SELECT time, log_type, action_type, item_id, change_num, uid FROM item_log';
            if ($where) $sql .= ' WHERE ' . implode(' AND ', $where);
            $sql .= ' ORDER BY time DESC LIMIT 100';
            $itemLogs = $this->gameLogDb->fetchAll($sql, $params);
            // Gán thêm tên item
            foreach ($itemLogs as &$log) {
                $iid = $log['item_id'];
                $log['item_name'] = isset($this->allItems[$iid]['name']) ? $this->allItems[$iid]['name'] : '';
            }
            unset($log);
        }
        // Truy vấn hero_log
        if ($tab === 'hero') {
            if ($hero_id !== '') {
                $where[] = 'hero_id = ?';
                $params[] = $hero_id;
            }
            $sql = 'SELECT time, log_type, action_type, hero_id, uid FROM hero_add_log';
            if ($where) $sql .= ' WHERE ' . implode(' AND ', $where);
            $sql .= ' ORDER BY time DESC LIMIT 100';
            $heroLogs = $this->gameLogDb->fetchAll($sql, $params);
        }
        $db = $this->db;
        $config = $this->config;
        require_once __DIR__ . '/../../resources/views/character/logs.php';
    }

    public function logs_export() {
        $this->requireLogin();
        if (!$this->hasPermission('character.view.logs')) {
            exit('No permission');
        }
        $tab = $_GET['tab'] ?? 'item';
        $role_id = $_GET['role_id'] ?? '';
        $log_type = $_GET['log_type'] ?? '';
        $action_type = $_GET['action_type'] ?? '';
        $item_id = $_GET['item_id'] ?? '';
        $hero_id = $_GET['hero_id'] ?? '';
        $where = [];
        $params = [];
        if ($role_id !== '') {
            $where[] = 'uid = ?';
            $params[] = $role_id;
        }
        if ($log_type !== '') {
            $where[] = 'log_type = ?';
            $params[] = $log_type;
        }
        if ($action_type !== '') {
            $where[] = 'action_type = ?';
            $params[] = $action_type;
        }
        if ($tab === 'item') {
            if ($item_id !== '') {
                $where[] = 'item_id = ?';
                $params[] = $item_id;
            }
            $sql = 'SELECT time, log_type, action_type, item_id, change_num, uid FROM item_log';
            if ($where) $sql .= ' WHERE ' . implode(' AND ', $where);
            $sql .= ' ORDER BY time DESC';
            $logs = $this->gameLogDb->fetchAll($sql, $params);
            $filename = 'item_log_export_' . date('Ymd_His') . '.xls';
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            echo "Time\tlog_type\taction_type\titem_id\tNum\tRole ID\n";
            foreach ($logs as $log) {
                echo date('Y-m-d H:i:s', $log['time']) . "\t" . $log['log_type'] . "\t" . $log['action_type'] . "\t" . $log['item_id'] . "\t" . $log['change_num'] . "\t" . $log['uid'] . "\n";
            }
            exit;
        }
        if ($tab === 'hero') {
            if ($hero_id !== '') {
                $where[] = 'hero_id = ?';
                $params[] = $hero_id;
            }
            $sql = 'SELECT time, log_type, action_type, hero_id, uid FROM hero_log';
            if ($where) $sql .= ' WHERE ' . implode(' AND ', $where);
            $sql .= ' ORDER BY time DESC';
            $logs = $this->gameLogDb->fetchAll($sql, $params);
            $filename = 'hero_log_export_' . date('Ymd_His') . '.xls';
            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            echo "Thời gian\tlog_type\taction_type\thero_id\tRole ID\n";
            foreach ($logs as $log) {
                echo date('Y-m-d H:i:s', $log['time']) . "\t" . $log['log_type'] . "\t" . $log['action_type'] . "\t" . $log['hero_id'] . "\t" . $log['uid'] . "\n";
            }
            exit;
        }
    }

    private function formatOnlineTime($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        return sprintf("%02d:%02d", $hours, $minutes);
    }
}