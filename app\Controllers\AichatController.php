<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Services\ChatGPTService;

class AichatController extends Controller {
    private $chatGPTService;
    
    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->chatGPTService = new ChatGPTService();
    }
    
    public function index() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('ai_chat.view')) {
            header('Location: /public/?route=auth&action=login');
            exit;
        }
        
        require_once __DIR__ . '/../../resources/views/ai_chat/index.php';
    }

    public function analyze() {
        $this->requireLogin();

        if (!$this->hasPermission('ai_chat.view')) {
            $this->jsonResponse(['error' => 'Không có quyền truy cập'], 403);
            return;
        }

        // Kiểm tra CSRF token
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            $this->jsonResponse(['error' => 'Token không hợp lệ'], 403);
            return;
        }

        $query = $_POST['query'] ?? '';
        if (empty($query)) {
            $this->jsonResponse(['error' => 'Vui lòng nhập câu hỏi'], 400);
            return;
        }

        try {
            // Phân tích câu hỏi và xác định loại phản hồi
            $response = $this->chatGPTService->processQuery($query);
            
            if ($response['type'] === 'chat') {
                // Nếu là loại chat, trả về phản hồi trực tiếp
                $this->jsonResponse([
                    'success' => true,
                    'type' => 'chat',
                    'response' => $response['content']
                ]);
            } else {
                // Nếu là loại SQL, kiểm tra xem có phải là SELECT không
                if (!preg_match('/^SELECT/i', trim($response['content']))) {
                    $this->jsonResponse([
                        'success' => true,
                        'type' => 'chat',
                        'response' => "Lỗi: Chỉ cho phép thực hiện các truy vấn SELECT"
                    ]);
                }

                // Thực thi truy vấn và phân tích kết quả
                $results = $this->db->fetchAll($response['content']);
                $analysis = $this->chatGPTService->analyzeResults($results, $query);
                
                $this->jsonResponse([
                    'success' => true,
                    'type' => 'sql',
                    'sql' => $response['content'],
                    'results' => $results,
                    'analysis' => $analysis
                ]);
            }
        } catch (\Exception $e) {
            $this->jsonResponse(['error' => $e->getMessage()], 500);
        }
    }
} 