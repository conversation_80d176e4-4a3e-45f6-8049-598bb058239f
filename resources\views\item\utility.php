<?php
require_once __DIR__ . '/../layouts/header.php';
?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col lg:flex-row lg:space-x-4">
        <!-- <PERSON><PERSON> v<PERSON><PERSON> hiển thị kết quả chung -->
        <div class="w-full lg:w-1/3 mb-4 lg:mb-0">
            <div class="bg-white rounded-lg shadow-md p-4 sticky top-4">
                <div class="flex justify-between items-center mb-2">
                    <label class="block text-sm font-medium text-gray-700">K<PERSON><PERSON> quả</label>
                    <button onclick="copyResult('commonResult')" class="text-blue-500 hover:text-blue-700 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                        </svg>
                    </button>
                </div>
                <textarea id="commonResult" rows="20" readonly class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"></textarea>
            </div>
        </div>

        <!-- Các tiện ích -->
        <div class="w-full lg:w-2/3">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h1 class="text-2xl font-bold mb-6">Tiện ích Vật phẩm</h1>
                <!-- Chuyển ID thành tên -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4">Chuyển ID thành tên</h2>
                    <div class="flex flex-col lg:flex-row lg:space-x-4">
                        <div class="w-full lg:w-1/3 mb-4 lg:mb-0">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-medium text-gray-700 mb-2">Cấu trúc:</h3>
                                <div class="space-y-2">
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Đầu vào:</p>
                                        <code class="text-xs text-blue-600">10000:1,10001:1,10002:1</code>
                                    </div>
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Kết quả:</p>
                                        <code class="text-xs text-green-600">Pháo Hoa Mùa Hè *1|Lệnh Bài Hiệp Khách Cao Cấp *1|Ngưng Tụ Đơn *1</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-full lg:w-2/3">
                            <div class="flex flex-col space-y-4">
                                <div>
                                    <label for="itemIds" class="block text-sm font-medium text-gray-700 mb-2">Nhập danh sách ID</label>
                                    <textarea id="itemIds" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="10000:13,10001:1,10002:3"></textarea>
                                </div>
                                <div class="flex space-x-4">
                                    <button onclick="convertIdsToNames()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        Chuyển đổi
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chuyển tên thành ID -->
                <div>
                    <h2 class="text-xl font-semibold mb-4">Chuyển tên thành ID</h2>
                    <div class="flex flex-col lg:flex-row lg:space-x-4">
                        <div class="w-full lg:w-1/3 mb-4 lg:mb-0">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-medium text-gray-700 mb-2">Cấu trúc:</h3>
                                <div class="space-y-2">
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Đầu vào:</p>
                                        <code class="text-xs text-blue-600">Pháo Hoa Mùa Hè *1000000|Lệnh Bài Hiệp Khách Cao Cấp *10</code>
                                    </div>
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Kết quả:</p>
                                        <code class="text-xs text-green-600">10000:1000000,10001:10</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-full lg:w-2/3">
                            <div class="flex flex-col space-y-4">
                                <div>
                                    <label for="itemNamesInput" class="block text-sm font-medium text-gray-700 mb-2">Nhập danh sách tên</label>
                                    <textarea id="itemNamesInput" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Pháo Hoa Mùa Hè *1000000|Lệnh Bài Hiệp Khách Cao Cấp *10"></textarea>
                                </div>
                                <div class="flex space-x-4">
                                    <button onclick="convertNamesToIds()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        Chuyển đổi
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Merge Item IDs -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4">Merge Item IDs</h2>
                    <div class="flex flex-col lg:flex-row lg:space-x-4">
                        <div class="w-full lg:w-1/3 mb-4 lg:mb-0">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-medium text-gray-700 mb-2">Cấu trúc:</h3>
                                <div class="space-y-2">
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Mỗi dòng một danh sách:</p>
                                        <code class="text-xs text-blue-600">10000:1,10001:1,10002:1</code>
                                        <code class="text-xs text-blue-600 block">10000:1,10001:1,10002:1</code>
                                    </div>
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Kết quả:</p>
                                        <code class="text-xs text-green-600">10000:2,10001:2,10002:2</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-full lg:w-2/3">
                            <div class="flex flex-col space-y-4">
                                <div>
                                    <label for="mergeInput" class="block text-sm font-medium text-gray-700 mb-2">Nhập danh sách Item IDs</label>
                                    <textarea id="mergeInput" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="10000:1,10001:1,10002:1&#10;10000:1,10001:1,10002:1"></textarea>
                                </div>
                                <div class="flex space-x-4">
                                    <button onclick="mergeItemIds()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        Merge
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                 <!-- Tính toán List ItemId -->
                 <div class="mb-8">
                    <h2 class="text-xl font-semibold mb-4">Tính toán List ItemId</h2>
                    <div class="flex flex-col lg:flex-row lg:space-x-4">
                        <div class="w-full lg:w-1/3 mb-4 lg:mb-0">
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-medium text-gray-700 mb-2">Cấu trúc:</h3>
                                <div class="space-y-2">
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Items cần thay đổi:</p>
                                        <code class="text-xs text-blue-600">* (tất cả) hoặc 10000 (một item cụ thể)</code>
                                    </div>
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Phép tính:</p>
                                        <code class="text-xs text-blue-600">+ - * /</code>
                                    </div>
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">Giá trị:</p>
                                        <code class="text-xs text-blue-600">Số nguyên</code>
                                    </div>
                                    <div class="bg-white p-2 rounded border border-gray-200">
                                        <p class="text-sm text-gray-600">List ItemId:</p>
                                        <code class="text-xs text-blue-600">10000:1,10001:1,10002:1</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="w-full lg:w-2/3">
                            <div class="flex flex-col space-y-4">
                                <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                                    <div>
                                        <label for="itemsToChange" class="block text-sm font-medium text-gray-700 mb-2">Items cần thay đổi</label>
                                        <input type="text" id="itemsToChange" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="* hoặc 10000">
                                    </div>
                                    <div>
                                        <label for="operator" class="block text-sm font-medium text-gray-700 mb-2">Phép tính</label>
                                        <select id="operator" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            <option value="+">+ (Cộng)</option>
                                            <option value="-">- (Trừ)</option>
                                            <option value="*">* (Nhân)</option>
                                            <option value="/">/ (Chia)</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="value" class="block text-sm font-medium text-gray-700 mb-2">Giá trị</label>
                                        <input type="number" id="value" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Nhập số">
                                    </div>
                                </div>
                                <div>
                                    <label for="listItems" class="block text-sm font-medium text-gray-700 mb-2">List ItemId</label>
                                    <textarea id="listItems" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="10000:1,10001:1,10002:1"></textarea>
                                </div>
                                <div class="flex space-x-4">
                                    <button onclick="calculateItemIds()" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                        Tính toán
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
const csrfToken = '<?php echo $_SESSION['csrf_token']; ?>';

function calculateItemIds() {
    const itemsToChange = document.getElementById('itemsToChange').value;
    const operator = document.getElementById('operator').value;
    const value = document.getElementById('value').value;
    const listItems = document.getElementById('listItems').value;

    if (!itemsToChange.trim() || !operator || !value || !listItems.trim()) {
        showToast('Vui lòng nhập đầy đủ thông tin', 'error');
        return;
    }

    fetch('?route=item&action=apiCalculateItemIds', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `items_to_change=${encodeURIComponent(itemsToChange)}&operator=${encodeURIComponent(operator)}&value=${encodeURIComponent(value)}&list_items=${encodeURIComponent(listItems)}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            document.getElementById('commonResult').value = data.result;
            showToast('Tính toán thành công!');
        } else {
            showToast(data.message || 'Có lỗi xảy ra', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Có lỗi xảy ra', 'error');
    });
}

function mergeItemIds() {
    const input = document.getElementById('mergeInput').value;
    if (!input.trim()) {
        showToast('Vui lòng nhập dữ liệu', 'error');
        return;
    }

    fetch('?route=item&action=apiMergeItemIds', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `input=${encodeURIComponent(input)}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            document.getElementById('commonResult').value = data.result;
            showToast('Merge thành công!');
        } else {
            showToast(data.message || 'Có lỗi xảy ra', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Có lỗi xảy ra', 'error');
    });
}

function convertIdsToNames() {
    const input = document.getElementById('itemIds').value;
    if (!input.trim()) {
        showToast('Vui lòng nhập dữ liệu', 'error');
        return;
    }

    fetch('?route=item&action=apiConvertIdsToNames', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `input=${encodeURIComponent(input)}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            document.getElementById('commonResult').value = data.result;
            showToast('Chuyển đổi thành công!');
        } else {
            showToast(data.message || 'Có lỗi xảy ra', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Có lỗi xảy ra', 'error');
    });
}

function convertNamesToIds() {
    const input = document.getElementById('itemNamesInput').value;
    if (!input.trim()) {
        showToast('Vui lòng nhập dữ liệu', 'error');
        return;
    }

    fetch('?route=item&action=apiConvertNamesToIds', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `input=${encodeURIComponent(input)}&csrf_token=${csrfToken}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            document.getElementById('commonResult').value = data.result;
            showToast('Chuyển đổi thành công!');
        } else {
            showToast(data.message || 'Có lỗi xảy ra', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Có lỗi xảy ra', 'error');
    });
}

function copyResult(elementId) {
    const textarea = document.getElementById(elementId);
    if (!textarea) {
        showToast('Không tìm thấy phần tử để sao chép', 'error');
        return;
    }
    
    navigator.clipboard.writeText(textarea.value)
        .then(() => {
            showToast('Đã sao chép vào clipboard!');
        })
        .catch(err => {
            console.error('Lỗi khi sao chép:', err);
            showToast('Không thể sao chép vào clipboard', 'error');
        });
}

function showToast(message, type = 'success') {
    showToastMessage(type, message);
}

</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 