<?php
namespace App\Controllers;

use App\Core\Controller;

class FlashSaleController extends Controller {
    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->initItems();
    }

    public function index() {
        $this->requireLogin();
        
        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('flashsale.create')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy danh sách server
        $servers = $this->db->getServers(true);
        
        require_once __DIR__ . '/../../resources/views/flashsale/index.php';
    }

    public function create() {
        $this->requireLogin();
        
        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('flashsale.create')) {
            $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền thực hiện chức năng này']);
        }
        
        // Lấy dữ liệu từ POST
        $serverIds = $_POST['server_ids'] ?? [];
        $packageId = $_POST['package_id'] ?? '';
        $rewards = $_POST['rewards'] ?? '';
        $price = $_POST['price'] ?? '';
        $quantity = $_POST['quantity'] ?? '';
        $reason = $_POST['reason'] ?? '';

        // Kiểm tra dữ liệu
        if (empty($serverIds)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng chọn ít nhất một máy chủ']);
        }

        if (empty($packageId) || !is_numeric($packageId)) {
            $this->jsonResponse(['status' => false, 'message' => 'ID gói không hợp lệ']);
        }

        if (empty($rewards)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập danh sách phần thưởng']);
        }

        if (empty($price)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập giá bán']);
        }

        if (empty($quantity) || !is_numeric($quantity) || $quantity <= 0) {
            $this->jsonResponse(['status' => false, 'message' => 'Số lượng gói không hợp lệ']);
        }

        try {
            // Kết nối tới game database
            $connectResults = $this->gameDatabaseManager->connectServers($serverIds);
            $this->checkGameDbConnections($connectResults);
            
            // Chuẩn bị lệnh
            $cmd = "AddPublicSale {$packageId} {$rewards} {$price} {$quantity}";
            
            $results = [];
            $successCount = 0;
            $errorCount = 0;

            // Gửi lệnh cho từng server
            foreach ($serverIds as $serverId) {
                $serverResult = [
                    'server_id' => $serverId,
                    'status' => true,
                    'message' => 'Tạo Flash Sale thành công',
                    'details' => []
                ];

                $command = [
                    'creator' => 'FlashSaleController',
                    'createtime' => time(),
                    'type' => 2,
                    'cmd' => $cmd
                ];

                $insertSql = $this->gameSqlBuilder->buildInsert('command', $command);
                $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);
                
                if (!$result['success']) {
                    $serverResult['status'] = false;
                    $serverResult['message'] = 'Tạo Flash Sale thất bại';
                    $serverResult['details'][] = [
                        'error' => $result['error']
                    ];
                    $errorCount++;
                } else {
                    $successCount++;
                }

                $results[] = $serverResult;
            }

            // Ghi log
            $this->db->log('flashsale_create', 'success', [
                'server_ids' => $serverIds,
                'package_id' => $packageId,
                'rewards' => $rewards,
                'price' => $price,
                'quantity' => $quantity,
                'reason' => $reason,
                'results' => $results
            ]);

            $this->jsonResponse([
                'status' => true,
                'message' => 'Tạo Flash Sale thành công',
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'results' => $results
                ]
            ]);
        } catch (\Exception $e) {
            $this->db->log('flashsale_create', 'error', [
                'error' => $e->getMessage(),
                'server_ids' => $serverIds,
                'package_id' => $packageId,
                'rewards' => $rewards,
                'price' => $price,
                'quantity' => $quantity
            ]);
            $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            // Đóng kết nối
            $this->gameDatabaseManager->closeAll();
        }
    }
}
