#!/bin/bash

# <PERSON><PERSON><PERSON> th<PERSON> mục chứa SSL
mkdir -p ../nginx/ssl

# Tạo private key
openssl genrsa -out ../nginx/ssl/gmtool.local.key 2048

# T<PERSON>o CSR (Certificate Signing Request)
openssl req -new -key ../nginx/ssl/gmtool.local.key -out ../nginx/ssl/gmtool.local.csr -subj "/CN=gmtool.local"

# Tạo self-signed certificate
openssl x509 -req -days 365 -in ../nginx/ssl/gmtool.local.csr -signkey ../nginx/ssl/gmtool.local.key -out ../nginx/ssl/gmtool.local.crt

# Cấp quyền cho certificate
chmod 644 ../nginx/ssl/gmtool.local.crt
chmod 644 ../nginx/ssl/gmtool.local.key 