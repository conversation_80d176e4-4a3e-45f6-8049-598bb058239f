# Hệ Thống Cache App Settings

## Tổng Quan

Hệ thống cache App Settings được thiết kế để:
- ✅ **Tăng tốc độ** truy xuất thông tin app settings
- ✅ **Giảm tải database** bằng Redis cache
- ✅ **Đồng bộ dữ liệu** tự động khi có thay đổi
- ✅ **Fallback graceful** khi Redis không khả dụng

## Kiến Trúc

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   GM Tool       │    │  AppSettings     │    │  Redis Cache    │
│   (CRUD)        │───▶│  Service         │───▶│                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                          │
                              ▼                          │
┌─────────────────┐    ┌──────────────────┐             │
│   API           │    │    Database      │◀────────────┘
│   (Read)        │───▶│  (app_settings)  │
└─────────────────┘    └──────────────────┘
```

## Components

### 1. AppSettingsService
**Vị trí**: `app/Services/AppSettingsService.php`

**Chức năng**:
- Quản lý cache Redis cho app settings
- CRUD operations với auto cache invalidation
- Validate app credentials với cache
- Fallback to database khi Redis không khả dụng

### 2. AppSetting Model
**Vị trí**: `app/Models/AppSetting.php`

**Chức năng**:
- Đ<PERSON>i diện data structure
- Helper methods để format data
- Business logic đơn giản

### 3. RedisService
**Vị trí**: `app/Services/RedisService.php`

**Chức năng mới**:
- `get()` - Lấy giá trị theo key
- `setex()` - Set với expiration time
- `del()` - Xóa keys
- `keys()` - Tìm keys theo pattern

## Cách Sử Dụng

### Trong Controllers

```php
// Initialize service
$appSettingsService = AppSettingsService::getInstance($this->db);

// Get app settings (with cache)
$appSettings = $appSettingsService->getByAppId('123');

// Create (auto invalidate cache)
$id = $appSettingsService->create($data);

// Update (auto invalidate cache)
$appSettingsService->update($id, $data);

// Delete (auto invalidate cache)
$appSettingsService->delete($id);

// Validate app (with cache)
$appSettings = $appSettingsService->validateApp($appId, $appSecret);
```

### Cache Management

```php
// Clear all cache
$appSettingsService->clearAllCache();

// Get cache stats
$stats = $appSettingsService->getCacheStats();
```

## Cache Strategy

### Cache Keys
- `app_settings:{app_id}` - Cache cho từng app
- `app_settings:all` - Cache cho tất cả apps

### Cache Expiration
- **Default**: 3600 seconds (1 hour)
- **Auto invalidation** khi có CRUD operations

### Fallback Behavior
- ✅ Redis unavailable → Fallback to database
- ✅ Cache miss → Load from database + cache result
- ✅ JSON decode error → Fallback to database

## Endpoints

### GM Tool
- `GET /public/?route=appsettings&action=view` - Xem danh sách (cached)
- `POST /public/?route=appsettings&action=create` - Tạo mới (invalidate cache)
- `POST /public/?route=appsettings&action=edit&id={id}` - Sửa (invalidate cache)
- `GET /public/?route=appsettings&action=delete&id={id}` - Xóa (invalidate cache)

### Cache Management
- `GET /public/?route=appsettings&action=cache&cache_action=stats` - Cache statistics
- `GET /public/?route=appsettings&action=cache&cache_action=clear` - Clear cache

### API
- `POST /api/app/info` - Lấy thông tin app (cached)

## Monitoring

### Cache Statistics
```json
{
  "redis_available": true,
  "cached_keys": 5,
  "cache_prefix": "app_settings:",
  "cache_expire": 3600
}
```

### Redis Status
```json
{
  "connected": true,
  "memory_used": "1.2M",
  "total_connections": "150",
  "keys": 25
}
```

## Best Practices

1. **Luôn sử dụng Service** thay vì truy cập database trực tiếp
2. **Không lo lắng về cache** - Service tự động quản lý
3. **Monitor Redis status** qua cache stats
4. **Test fallback behavior** khi Redis down
5. **Clear cache** khi cần cập nhật ngay lập tức

## Environment Variables

```env
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your_password
```

## Troubleshooting

### Redis Connection Issues
- Service sẽ tự động fallback to database
- Kiểm tra Redis server status
- Xem logs cho connection errors

### Cache Inconsistency
- Gọi `clearAllCache()` để reset
- Kiểm tra auto invalidation logic
- Monitor cache statistics

### Performance Issues
- Tăng cache expiration time
- Monitor Redis memory usage
- Optimize database queries cho fallback 