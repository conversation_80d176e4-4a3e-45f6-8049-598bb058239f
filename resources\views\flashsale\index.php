<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <!-- Thêm div thông báo -->
    <div id="notification" class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 hidden">
        <div class="bg-white p-4 sm:p-8 rounded-xl shadow-2xl border-2 w-[90vw] sm:min-w-[400px] sm:max-w-[600px]">
            <div class="text-center">
                <div class="mb-4">
                    <svg id="notification-icon" class="h-12 sm:h-16 w-12 sm:w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <!-- Icon sẽ được thay đổi tùy theo trạng thái -->
                    </svg>
                </div>
                <h3 id="notification-title" class="text-xl sm:text-2xl font-bold mb-3"></h3>
                <p id="notification-message" class="text-gray-700 text-base sm:text-lg mb-4 sm:mb-6"></p>
                <div id="notification-details" class="text-left mb-4 sm:mb-6 bg-gray-50 p-3 sm:p-4 rounded-lg"></div>
                <button onclick="closeNotification()" class="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out text-sm sm:text-base">
                    Đóng
                </button>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <h2 class="text-xl sm:text-2xl font-bold text-gray-900">
                    Tạo Flash Sale
                </h2>
            </div>
            
            <div class="p-3 sm:p-6">
                <form id="flashsale-form" class="space-y-4 sm:space-y-6">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <!-- Chọn máy chủ -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300">
                        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2">
                            <label class="block text-base sm:text-lg font-medium text-gray-700">Máy chủ</label>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="select-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-blue-600 hover:text-blue-800 bg-blue-50 rounded-md border border-blue-200">Chọn tất cả</button>
                                <button type="button" id="deselect-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-red-600 hover:text-red-800 bg-red-50 rounded-md border border-red-200">Bỏ chọn tất cả</button>
                            </div>
                        </div>
                        <div class="relative">
                            <input type="text" id="server-search" placeholder="Tìm kiếm máy chủ..." 
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border-2 border-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute right-3 top-2.5">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="mt-3 sm:mt-4 max-h-96 overflow-y-auto border-2 border-gray-300 rounded-md">
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 p-2 sm:p-3">
                                <?php 
                                // Tạo mảng để lưu trữ các mysql_db và server tương ứng
                                $dbServers = [];
                                foreach ($servers as $server) {
                                    if (!empty($server['mysql_db'])) {
                                        if (!isset($dbServers[$server['mysql_db']])) {
                                            $dbServers[$server['mysql_db']] = [];
                                        }
                                        $dbServers[$server['mysql_db']][] = $server;
                                    }
                                }
                                
                                foreach ($servers as $server): ?>
                                    <?php if (!$server['merged_into']): ?>
                                        <div class="server-item flex flex-col p-3 hover:bg-gray-100 rounded-md border border-gray-200">
                                            <div class="flex items-center">
                                                <input type="checkbox" name="server_ids[]" value="<?php echo $server['id']; ?>" 
                                                       class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400 rounded">
                                                <div class="ml-3">
                                                    <label class="block text-base font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($server['name']); ?>
                                                    </label>
                                                    <span class="text-sm text-gray-500">ID: <?php echo $server['id']; ?></span>
                                                </div>
                                            </div>
                                            <?php if (!empty($server['mysql_db']) && count($dbServers[$server['mysql_db']]) > 1): ?>
                                                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                                                    <div class="flex items-start">
                                                        <svg class="h-5 w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                        </svg>
                                                        <div class="text-sm text-yellow-700">
                                                            <p class="font-medium">Cảnh báo: Database trùng lặp</p>
                                                            <p class="mt-1">Database này cũng được sử dụng bởi:</p>
                                                            <ul class="list-disc list-inside mt-1">
                                                                <?php foreach ($dbServers[$server['mysql_db']] as $otherServer): ?>
                                                                    <?php if ($otherServer['id'] != $server['id']): ?>
                                                                        <li>Server <?php echo $otherServer['id']; ?>: <?php echo htmlspecialchars($otherServer['name']); ?></li>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Thông tin Flash Sale -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Thông tin Flash Sale</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- ID gói -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">ID gói</label>
                                <input type="number" name="package_id" placeholder="Nhập ID gói" 
                                       class="w-full px-3 py-2 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <!-- Số lượng gói -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Số lượng gói</label>
                                <input type="number" name="quantity" placeholder="Nhập số lượng gói" 
                                       class="w-full px-3 py-2 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <!-- Danh sách phần thưởng -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Danh sách phần thưởng</label>
                                <input type="text" name="rewards" placeholder="Định dạng: item_id:quantity,item_id:quantity" 
                                       class="w-full px-3 py-2 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <p class="mt-1 text-sm text-gray-500">Ví dụ: 10002:100,10006:200</p>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <!-- Giá bán -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Giá bán</label>
                                <input type="text" name="price" placeholder="Định dạng: item_id:quantity" 
                                       class="w-full px-3 py-2 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                <p class="mt-1 text-sm text-gray-500">Ví dụ: 10001:1000</p>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <!-- Lý do -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Lý do (ghi chú nội bộ)</label>
                                <textarea name="reason" rows="2" placeholder="Nhập lý do tạo Flash Sale" 
                                          class="w-full px-3 py-2 border-2 border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Nút gửi -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Tạo Flash Sale
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
// Thêm hàm hiển thị thông báo
function showNotification(title, message, details = '') {
    const isSuccess = title === 'Thành công';
    
    let html = `<div class="text-left">`;
    html += `<p class="mb-3 sm:mb-4">${message}</p>`;
    if (details) {
        html += `<div class="bg-gray-50 p-2 sm:p-4 rounded-lg">${details}</div>`;
    }
    html += `</div>`;

    Swal.fire({
        title: title,
        html: html,
        icon: isSuccess ? 'success' : 'error',
        confirmButtonText: 'Đóng',
        confirmButtonColor: isSuccess ? '#2563eb' : '#dc2626',
        width: '90vw',
        maxWidth: '600px',
        customClass: {
            popup: 'rounded-xl',
            title: 'text-xl sm:text-2xl font-bold mb-3 sm:mb-4',
            content: 'text-base sm:text-lg',
            confirmButton: 'px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base'
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('flashsale-form');
    const selectAllButton = document.getElementById('select-all');
    const deselectAllButton = document.getElementById('deselect-all');
    const serverSearch = document.getElementById('server-search');
    const serverItems = document.querySelectorAll('.server-item');

    // Kiểm tra sự tồn tại của form
    if (!form) {
        console.error('Form not found');
        return;
    }

    // Tìm kiếm server
    if (serverSearch) {
        serverSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            serverItems.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                const id = item.querySelector('span').textContent.toLowerCase();
                if (label.includes(searchTerm) || id.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Chọn tất cả server
    if (selectAllButton) {
        selectAllButton.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('input[name="server_ids[]"]');
            checkboxes.forEach(checkbox => {
                if (checkbox.closest('.server-item').style.display !== 'none') {
                    checkbox.checked = true;
                }
            });
        });
    }

    // Bỏ chọn tất cả server
    if (deselectAllButton) {
        deselectAllButton.addEventListener('click', function() {
            const checkboxes = document.querySelectorAll('input[name="server_ids[]"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });
        });
    }

    // Xử lý submit form
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Hiển thị loading
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Đang xử lý...
        `;

        // Gửi request
        fetch('?route=flashsale&action=create', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: $(form).serialize()
        })
        .then(res => res.json())
        .then(data => {
            console.log('Response data:', data);
            
            if (!data.status) {
                // Hiển thị thông báo lỗi từ API
                showNotification(
                    'Lỗi',
                    data.message || 'Có lỗi xảy ra khi tạo Flash Sale. Vui lòng thử lại sau.'
                );
                return;
            }

            let details = '<div class="space-y-2">';
            data.data.results.forEach(result => {
                if (result.status) {
                    details += `<p class="text-green-600 font-medium">Server ${result.server_id}: ${result.message}</p>`;
                } else {
                    details += `<p class="text-red-600 font-medium">Server ${result.server_id}: ${result.message}</p>`;
                    if (result.details && result.details.length > 0) {
                        details += '<div class="pl-4 mt-1">';
                        result.details.forEach(detail => {
                            details += `<p class="text-sm text-red-500">- ${detail.error}</p>`;
                        });
                        details += '</div>';
                    }
                }
            });
            details += '</div>';

            showNotification(
                'Thành công',
                `Tạo Flash Sale thành công: ${data.data.success_count} máy chủ, Thất bại: ${data.data.error_count} máy chủ`,
                details
            );
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification(
                'Lỗi',
                'Có lỗi xảy ra khi kết nối đến máy chủ. Vui lòng thử lại sau.'
            );
        })
        .finally(() => {
            // Khôi phục nút submit
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        });
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
