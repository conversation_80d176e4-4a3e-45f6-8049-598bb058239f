<?php
namespace App\Controllers;

use App\Core\Controller;
use Exception;

class AdminController extends Controller {
    public function view() {
        $this->requireLogin();

        // Kiểm tra quyền
        if (!$this->hasPermission('admin.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy danh sách admin
        $admins = $this->db->fetchAll("SELECT * FROM gm_users ORDER BY created_at DESC");
        
        // Lấy danh sách role từ permissions.php
        $roles = $this->config["permissions"]['roles'];
        
        require_once __DIR__ . '/../../resources/views/admin/view.php';
    }
    
    public function create() {
        $this->requireLogin();

        // Kiểm tra quyền
        if (!$this->hasPermission('admin.create')) {
            $this->redirectToFirstAccessiblePage();
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            $email = $_POST['email'] ?? '';
            $role = $_POST['role'] ?? 'viewer';
            
            // Validate
            $errors = [];
            if (empty($username)) $errors[] = "Tên đăng nhập không được để trống";
            if (empty($password)) $errors[] = "Mật khẩu không được để trống";
            if (empty($email)) $errors[] = "Email không được để trống";
            if (!isset($this->config["permissions"]['roles'][$role])) $errors[] = "Role không hợp lệ";
            
            // Kiểm tra username đã tồn tại
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM gm_users WHERE username = ?", [$username]);
            if ($result['count'] > 0) {
                $errors[] = "Tên đăng nhập đã tồn tại";
            }
            
            if (empty($errors)) {
                // Tạo admin mới
                $userId = $this->db->insert('gm_users', [
                    'username' => $username,
                    'password' => password_hash($password, PASSWORD_DEFAULT),
                    'email' => $email,
                    'role' => $role
                ]);
                
                // Ghi log
                $this->db->log('create_admin', 'success', [
                    'user_id' => $userId,
                    'username' => $username,
                    'email' => $email,
                    'role' => $role
                ]);
                
                header('Location: /public/?route=admin&action=view');
                exit;
            }
        }
        
        // Lấy danh sách role từ permissions.php
        $roles = $this->config["permissions"]['roles'];
        
        require_once __DIR__ . '/../../resources/views/admin/create.php';
    }
    
    public function edit() {
        $this->requireLogin();

        // Kiểm tra quyền
        if (!$this->hasPermission('admin.edit')) {
            $this->redirectToFirstAccessiblePage();
        }
        
        $id = $_GET['id'] ?? 0;
        
        // Lấy thông tin admin
        $admin = $this->db->fetch("SELECT * FROM gm_users WHERE id = ?", [$id]);
        
        if (!$admin) {
            header('Location: /public/?route=admin&action=view');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $_POST['username'] ?? '';
            $email = $_POST['email'] ?? '';
            $role = $_POST['role'] ?? 'viewer';
            $password = $_POST['password'] ?? '';
            
            // Validate
            $errors = [];
            if (empty($username)) $errors[] = "Tên đăng nhập không được để trống";
            if (empty($email)) $errors[] = "Email không được để trống";
            if (!isset($this->config["permissions"]['roles'][$role])) $errors[] = "Role không hợp lệ";
            
            // Kiểm tra username đã tồn tại
            $result = $this->db->fetch("SELECT COUNT(*) as count FROM gm_users WHERE username = ? AND id != ?", [$username, $id]);
            if ($result['count'] > 0) {
                $errors[] = "Tên đăng nhập đã tồn tại";
            }
            
            if (empty($errors)) {
                // Cập nhật thông tin
                $data = [
                    'username' => $username,
                    'email' => $email,
                    'role' => $role
                ];
                
                if (!empty($password)) {
                    $data['password'] = password_hash($password, PASSWORD_DEFAULT);
                }
                
                $this->db->update('gm_users', $data, 'id = ?', [$id]);
                
                // Ghi log
                $this->db->log('update_admin', 'success', [
                    'user_id' => $id,
                    'username' => $username,
                    'email' => $email,
                    'role' => $role,
                    'changed_password' => !empty($password)
                ]);
                
                header('Location: /public/?route=admin&action=view');
                exit;
            }
        }
        
        // Lấy danh sách role từ permissions.php
        $roles = $this->config["permissions"]['roles'];
        
        require_once __DIR__ . '/../../resources/views/admin/edit.php';
    }
    
    public function delete() {
        $this->requireLogin();

        // Kiểm tra quyền
        if (!$this->hasPermission('admin.delete')) {
            $this->redirectToFirstAccessiblePage();
        }
        
        $id = $_GET['id'] ?? 0;
        
        // Không cho xóa chính mình
        if ($id == $_SESSION['user_id']) {
            header('Location: /public/?route=admin&action=view');
            exit;
        }
        
        try {
            // Bắt đầu transaction
            $this->db->beginTransaction();
            
            // Lấy thông tin admin trước khi xóa
            $admin = $this->db->fetch("SELECT * FROM gm_users WHERE id = ?", [$id]);
            
            // Xóa các bản ghi liên quan trong gm_logs
            $this->db->delete('gm_logs', 'user_id = ?', [$id]);
            
            // Xóa admin
            $this->db->delete('gm_users', 'id = ?', [$id]);
            
            // Ghi log
            $this->db->log('delete_admin', 'success', [
                'user_id' => $id,
                'username' => $admin['username'],
                'email' => $admin['email'],
                'role' => $admin['role']
            ]);
            
            // Commit transaction
            $this->db->commit();
            
            header('Location: /public/?route=admin&action=view');
            exit;
        } catch (Exception $e) {
            // Rollback transaction nếu có lỗi
            $this->db->rollBack();
            
            // Ghi log lỗi
            $this->db->log('delete_admin', 'error', [
                'user_id' => $id,
                'error' => $e->getMessage()
            ]);
            
            die("Lỗi khi xóa admin: " . $e->getMessage());
        }
    }
} 