<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h3 class="text-lg sm:text-xl font-medium text-gray-900">
                Duyệt đơn hàng
            </h3>
            <a href="?route=order" class="text-indigo-600 hover:text-indigo-900 text-sm sm:text-base">
                <i class="fas fa-arrow-left mr-1 sm:mr-2"></i>Quay lại
            </a>
        </div>
    </div>
    
    <div class="border-t border-gray-200">
        <div class="p-4">
            <?php if (isset($_SESSION['error'])): ?>
                <div class="mb-4 rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">
                                <?= htmlspecialchars($_SESSION['error']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['success'])): ?>
                <div class="mb-4 rounded-md bg-green-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">
                                <?= htmlspecialchars($_SESSION['success']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <form method="POST" id="approveForm" class="space-y-4 sm:space-y-6">
                <input type="hidden" name="route" value="order/approve">
                <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                
                <div>
                    <label for="orderId" class="block text-sm sm:text-base font-medium text-gray-700">
                        Order ID <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <input type="text" 
                               id="orderId" 
                               name="order_id"
                               value="<?= htmlspecialchars($_GET['id'] ?? '') ?>"
                               required
                               readonly
                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm sm:text-base bg-gray-100">
                    </div>
                </div>

                <div>
                    <label for="status" class="block text-sm sm:text-base font-medium text-gray-700">
                        Trạng thái <span class="text-red-500">*</span>
                    </label>
                    <div class="mt-1">
                        <select id="status" 
                                name="status" 
                                required
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm sm:text-base">
                            <option value="">-- Chọn trạng thái --</option>
                            <?php foreach ($orderConfig['display']['status_options'] as $value => $label): ?>
                                <option value="<?= htmlspecialchars($value) ?>" 
                                        <?= isset($status) && $status === $value ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>

                <div>
                    <label for="note" class="block text-sm sm:text-base font-medium text-gray-700">
                        Ghi chú
                    </label>
                    <div class="mt-1">
                        <textarea id="note" 
                                  name="note" 
                                  rows="3"
                                  class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm sm:text-base"
                                  placeholder="Nhập ghi chú (nếu có)"><?= htmlspecialchars($note ?? '') ?></textarea>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4">
                    <button type="submit" 
                            id="submitBtn"
                            class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm sm:text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-check mr-2"></i>
                        Xác nhận
                    </button>
                    <a href="?route=order" 
                       class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm sm:text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Quay lại
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.getElementById('approveForm').addEventListener('submit', function(e) {
    var submitBtn = document.getElementById('submitBtn');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang xử lý...';
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 