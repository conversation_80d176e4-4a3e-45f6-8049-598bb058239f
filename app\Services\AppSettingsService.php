<?php
namespace App\Services;

use App\Core\Database;

class AppSettingsService 
{
    private static $instance = null;
    private $db;
    private $redis;
    private $cachePrefix = 'app_settings:';
    private $cacheExpire = 3600; // 1 hour
    
    private function __construct(Database $db) {
        $this->db = $db;
        $this->redis = RedisService::getInstance();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(Database $db): self
    {
        if (self::$instance === null) {
            self::$instance = new self($db);
        }
        return self::$instance;
    }
    
    /**
     * Get app settings by app_id with cache
     */
    public function getByAppId(string $appId): ?array
    {
        $cacheKey = $this->cachePrefix . $appId;
        
        // Try to get from Redis cache first
        if ($this->redis->isAvailable()) {
            try {
                $cached = $this->redis->get($cacheKey);
                if ($cached !== false) {
                    $data = json_decode($cached, true);
                    if ($data !== null && isset($data['data'])) {
                        return $data['data'];
                    }
                }
            } catch (\Exception $e) {
                // Silent fail, fallback to database
            }
        }
        
        // Get from database
        $appSettings = $this->db->fetch("SELECT * FROM app_settings WHERE app_id = ?", [$appId]);
        
        // Cache the result if Redis is available
        if ($appSettings && $this->redis->isAvailable()) {
            try {
                $cacheData = [
                    'data' => $appSettings,
                    'cached_at' => time(),
                    'expires_at' => time() + $this->cacheExpire
                ];
                $this->redis->setex($cacheKey, $this->cacheExpire, json_encode($cacheData));
            } catch (\Exception $e) {
                // Silent fail
            }
        }
        
        return $appSettings ?: null;
    }
    
    /**
     * Get all app settings with cache
     */
    public function getAll(): array
    {
        $cacheKey = $this->cachePrefix . 'all';
        
        // Try to get from Redis cache first
        if ($this->redis->isAvailable()) {
            try {
                $cached = $this->redis->get($cacheKey);
                if ($cached !== false) {
                    $data = json_decode($cached, true);
                    if ($data !== null && isset($data['data'])) {
                        return $data['data'];
                    }
                }
            } catch (\Exception $e) {
                // Silent fail, fallback to database
            }
        }
        
        // Get from database
        $appSettings = $this->db->fetchAll("SELECT * FROM app_settings ORDER BY app_id ASC");
        
        // Cache the result if Redis is available
        if ($this->redis->isAvailable()) {
            try {
                $cacheData = [
                    'data' => $appSettings,
                    'cached_at' => time(),
                    'expires_at' => time() + $this->cacheExpire
                ];
                $this->redis->setex($cacheKey, $this->cacheExpire, json_encode($cacheData));
            } catch (\Exception $e) {
                // Silent fail
            }
        }
        
        return $appSettings;
    }
    
    /**
     * Create new app settings and invalidate cache
     */
    public function create(array $data): int
    {
        $id = $this->db->insert('app_settings', $data);
        
        // Invalidate cache
        $this->invalidateCache($data['app_id']);
        
        return $id;
    }
    
    /**
     * Update app settings and invalidate cache
     */
    public function update(int $id, array $data): bool
    {
        // Get current app_id before update
        $current = $this->db->fetch("SELECT app_id FROM app_settings WHERE id = ?", [$id]);
        
        try {
            $this->db->update('app_settings', $data, 'id = ?', [$id]);
            
            // Invalidate cache for both old and new app_id
            if ($current) {
                $this->invalidateCache($current['app_id']);
            }
            if (isset($data['app_id'])) {
                $this->invalidateCache($data['app_id']);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Delete app settings and invalidate cache
     */
    public function delete(int $id): bool
    {
        // Get app_id before deletion
        $appSettings = $this->db->fetch("SELECT app_id FROM app_settings WHERE id = ?", [$id]);
        
        try {
            $this->db->delete('app_settings', 'id = ?', [$id]);
            
            // Invalidate cache
            if ($appSettings) {
                $this->invalidateCache($appSettings['app_id']);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Invalidate cache for specific app_id and all cache
     */
    private function invalidateCache(?string $appId = null): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }
        
        try {
            // Always invalidate 'all' cache
            $this->redis->del($this->cachePrefix . 'all');
            
            // Invalidate specific app_id cache if provided
            if ($appId !== null) {
                $this->redis->del($this->cachePrefix . $appId);
            }
        } catch (\Exception $e) {
            // Silent fail
        }
    }
    
    /**
     * Clear all app settings cache
     */
    public function clearAllCache(): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }
        
        try {
            $keys = $this->redis->keys($this->cachePrefix . '*');
            if (!empty($keys)) {
                $this->redis->del($keys);
            }
        } catch (\Exception $e) {
            // Silent fail
        }
    }
    
    /**
     * Validate app credentials with cache
     */
    public function validateApp(string $appId, string $appSecret): ?array
    {
        $appSettings = $this->getByAppId($appId);
        
        if (!$appSettings) {
            return null;
        }
        
        if ($appSettings['app_secret'] !== $appSecret) {
            return null;
        }
        
        if (!$appSettings['is_active']) {
            return null;
        }
        
        return $appSettings;
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        if (!$this->redis->isAvailable()) {
            return ['redis_available' => false];
        }
        
        try {
            $keys = $this->redis->keys($this->cachePrefix . '*');
            $stats = [
                'redis_available' => true,
                'cached_keys' => count($keys),
                'cache_prefix' => $this->cachePrefix,
                'cache_expire' => $this->cacheExpire,
                'last_updated' => null,
                'cache_details' => []
            ];
            
            // Get cache details for each key
            foreach ($keys as $key) {
                try {
                    $cached = $this->redis->get($key);
                    if ($cached !== false) {
                        $data = json_decode($cached, true);
                        if ($data !== null && isset($data['cached_at'])) {
                            $keyName = str_replace($this->cachePrefix, '', $key);
                            $stats['cache_details'][$keyName] = [
                                'cached_at' => $data['cached_at'],
                                'expires_at' => $data['expires_at'] ?? null
                            ];
                            
                            // Update last_updated with most recent cache time
                            if (!$stats['last_updated'] || $data['cached_at'] > $stats['last_updated']) {
                                $stats['last_updated'] = $data['cached_at'];
                            }
                        }
                    }
                } catch (\Exception $e) {
                    // Continue with other keys
                }
            }
            
            return $stats;
        } catch (\Exception $e) {
            return ['redis_available' => false, 'error' => $e->getMessage()];
        }
    }
} 