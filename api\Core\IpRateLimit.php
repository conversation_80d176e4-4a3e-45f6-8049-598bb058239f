<?php
namespace Api\Core;

use App\Services\RedisService;
use Api\Core\Validator;

trait IpRateLimit
{
    /**
     * Kiểm tra rate limiting theo IP cho endpoint public
     */
    protected function checkIpRateLimit(string $endpointName): void
    {
        $authConfig = $this->config['api']['authentication'] ?? [];
        $ipRateLimits = $authConfig['ip_rate_limits'] ?? [];
        
        $limit = $ipRateLimits[$endpointName] ?? $ipRateLimits['default'] ?? 60;
        
        // Lấy IP address
        $validator = Validator::getInstance();
        $clientIp = $validator->getIp();
        
        // Key để track rate limit cho Redis
        $redisKey = "ip_rate_limit:{$endpointName}:{$clientIp}";
        
        // Thử sử dụng Redis trước
        try {
            $redisService = RedisService::getInstance();
            if ($redisService->isAvailable()) {
                $result = $redisService->checkRateLimit($redisKey, $limit, 60);
                if (!$result['allowed']) {
                    $response = [
                        'status' => false,
                        'message' => "Bạn đã vượt quá giới hạn sử dụng từ IP này. Vui lòng thử lại sau " . date('Y-m-d H:i:s', $result['reset_time']),
                        'error_code' => 'RATE_LIMIT_EXCEEDED',
                        'retry_after' => $result['reset_time'] - time()
                    ];
                    $this->jsonResponse($response, 429); // 429 Too Many Requests
                    exit;
                }
                return;
            }
        } catch (\Exception $e) {
            error_log("Redis IP rate limit failed, fallback to database: " . $e->getMessage());
        }
        
        // Fallback to database-based rate limiting
        $this->checkIpRateLimitDatabase($endpointName, $clientIp, $limit);
    }
    
    /**
     * Database fallback cho IP rate limiting
     */
    private function checkIpRateLimitDatabase(string $endpointName, string $clientIp, int $limit): void
    {
        try {
            // Tạo bảng rate_limit_ip nếu chưa tồn tại
            $this->db->query("
                CREATE TABLE IF NOT EXISTS rate_limit_ip (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    endpoint VARCHAR(100) NOT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    request_count INT DEFAULT 1,
                    window_start DATETIME NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_endpoint_ip (endpoint, ip_address),
                    INDEX idx_window_start (window_start)
                ) ENGINE=InnoDB
            ");
            
            $currentTime = date('Y-m-d H:i:s');
            $windowStart = date('Y-m-d H:i:s', strtotime('-1 minute'));
            
            // Xóa các records cũ hơn 1 phút
            $this->db->query(
                "DELETE FROM rate_limit_ip WHERE window_start < ?",
                [$windowStart]
            );
            
            // Kiểm tra số lượng requests trong window hiện tại
            $currentCount = $this->db->fetch(
                "SELECT SUM(request_count) as total 
                FROM rate_limit_ip 
                WHERE endpoint = ? AND ip_address = ? AND window_start > ?",
                [$endpointName, $clientIp, $windowStart]
            );
            
            $totalRequests = (int)($currentCount['total'] ?? 0);
            
            if ($totalRequests >= $limit) {
                $response = [
                    'status' => false,
                    'message' => "Bạn đã vượt quá giới hạn sử dụng từ IP này. Vui lòng thử lại sau 1 phút.",
                    'error_code' => 'RATE_LIMIT_EXCEEDED',
                    'retry_after' => 60
                ];
                $this->jsonResponse($response, 429);
                exit;
            }
            
            // Thêm request mới
            $this->db->query(
                "INSERT INTO rate_limit_ip (endpoint, ip_address, window_start) 
                VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE request_count = request_count + 1",
                [$endpointName, $clientIp, $currentTime]
            );
            
        } catch (\Exception $e) {
            error_log("Database IP rate limit error: " . $e->getMessage());
            // Không block request nếu có lỗi database
        }
    }
    
    /**
     * Kiểm tra rate limiting cho endpoint (tự động chọn user hoặc IP)
     */
    protected function checkEndpointRateLimit(string $endpointName): void
    {
        // Kiểm tra endpoint có cần authentication không
        $endpointConfig = $this->config['api']['endpoints'][$endpointName] ?? [];
        $requireAuth = $endpointConfig['require_auth'] ?? true;
        
        if ($requireAuth && $this->authenticatedUser) {
            // Endpoint cần auth và user đã authenticated -> dùng user-based rate limiting
            $this->checkRateLimit($endpointName);
        } else {
            // Endpoint public hoặc user chưa authenticated -> dùng IP-based rate limiting
            $this->checkIpRateLimit($endpointName);
        }
    }
    
    /**
     * Wrapper cho endpoint với automatic rate limiting
     */
    protected function withRateLimit(string $endpointName, callable $callback)
    {
        // Kiểm tra rate limiting trước
        $this->checkEndpointRateLimit($endpointName);
        
        // Execute callback
        return $callback();
    }
    
    /**
     * Lấy thông tin rate limit hiện tại cho IP
     */
    protected function getIpRateLimitInfo(string $endpointName): array
    {
        $authConfig = $this->config['api']['authentication'] ?? [];
        $ipRateLimits = $authConfig['ip_rate_limits'] ?? [];
        $limit = $ipRateLimits[$endpointName] ?? $ipRateLimits['default'] ?? 60;
        
        $validator = Validator::getInstance();
        $clientIp = $validator->getIp();
        $redisKey = "ip_rate_limit:{$endpointName}:{$clientIp}";
        
        try {
            $redisService = RedisService::getInstance();
            if ($redisService->isAvailable()) {
                // Simulate check để lấy thông tin mà không tăng counter
                $current = time();
                $currentCount = $redisService->getRedis()->zCard($redisKey);
                
                return [
                    'limit' => $limit,
                    'remaining' => max(0, $limit - $currentCount),
                    'reset_time' => $current + 60,
                    'window' => 60
                ];
            }
        } catch (\Exception $e) {
            // Silent fail
        }
        
        return [
            'limit' => $limit,
            'remaining' => $limit,
            'reset_time' => time() + 60,
            'window' => 60
        ];
    }
}
