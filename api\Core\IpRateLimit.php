<?php
namespace Api\Core;

use App\Services\RedisService;
use Api\Core\Validator;

trait IpRateLimit
{
    /**
     * Kiểm tra rate limiting theo IP cho endpoint public
     */
    protected function checkIpRateLimit(string $endpointName): void
    {
        $authConfig = $this->config['api']['authentication'] ?? [];
        $ipRateLimits = $authConfig['ip_rate_limits'] ?? [];

        $limit = $ipRateLimits[$endpointName] ?? $ipRateLimits['default'] ?? 60;

        // Lấy IP address
        $validator = Validator::getInstance();
        $clientIp = $validator->getIp();

        // Key để track rate limit cho Redis
        $redisKey = "ip_rate_limit:{$endpointName}:{$clientIp}";

        try {
            $redisService = RedisService::getInstance();
            $result = $redisService->checkRateLimit($redisKey, $limit, 60);

            if (!$result['allowed']) {
                $response = [
                    'status' => false,
                    'message' => "Bạn đã vượt quá giới hạn sử dụng từ IP này. Vui lòng thử lại sau " . date('Y-m-d H:i:s', $result['reset_time']),
                    'error_code' => 'RATE_LIMIT_EXCEEDED',
                    'retry_after' => $result['reset_time'] - time()
                ];
                $this->jsonResponse($response, 429); // 429 Too Many Requests
                exit;
            }
        } catch (\Exception $e) {
            error_log("Redis IP rate limit failed: " . $e->getMessage());
            // Không block request nếu Redis fail
        }
    }


    /**
     * Kiểm tra rate limiting cho endpoint (tự động chọn user hoặc IP)
     */
    protected function checkEndpointRateLimit(string $endpointName): void
    {
        // Kiểm tra endpoint có cần authentication không
        $endpointConfig = $this->config['api']['endpoints'][$endpointName] ?? [];
        $requireAuth = $endpointConfig['require_auth'] ?? true;

        if ($requireAuth && $this->authenticatedUser) {
            // Endpoint cần auth và user đã authenticated -> dùng user-based rate limiting
            $this->checkRateLimit($endpointName);
        } else {
            // Endpoint public hoặc user chưa authenticated -> dùng IP-based rate limiting
            $this->checkIpRateLimit($endpointName);
        }
    }

    /**
     * Wrapper cho endpoint với automatic rate limiting
     */
    protected function withRateLimit(string $endpointName, callable $callback)
    {
        // Kiểm tra rate limiting trước
        $this->checkEndpointRateLimit($endpointName);

        // Execute callback
        return $callback();
    }

    /**
     * Lấy thông tin rate limit hiện tại cho IP
     */
    protected function getIpRateLimitInfo(string $endpointName): array
    {
        $authConfig = $this->config['api']['authentication'] ?? [];
        $ipRateLimits = $authConfig['ip_rate_limits'] ?? [];
        $limit = $ipRateLimits[$endpointName] ?? $ipRateLimits['default'] ?? 60;

        return [
            'limit' => $limit,
            'remaining' => $limit, // Simplified - không cần check exact count
            'reset_time' => time() + 60,
            'window' => 60
        ];
    }
}
