<?php
namespace App\Controllers;

use App\Core\Controller;
class LogController extends Controller{

    public function index() {
        $this->requireLogin();

        // Ki<PERSON>m tra quyền truy cập
        if (!$this->hasPermission('logs.view')) {
            header('Location: /public/?route=auth&action=login');
            exit;
        }

        // Lấy tham số tìm kiếm và phân trang
        $logAction = '';
        $status = '';
        $adminId = '';
        
        if (isset($_GET['log_action']) && is_string($_GET['log_action'])) {
            $logAction = trim($_GET['log_action']);
        }
        
        if (isset($_GET['status']) && is_string($_GET['status'])) {
            $status = trim($_GET['status']);
        }

        if (isset($_GET['admin_id']) && is_string($_GET['admin_id'])) {
            $adminId = trim($_GET['admin_id']);
        }
        
        $page = max(1, intval($_GET['page'] ?? 1));
        $perPage = 20;
        $offset = ($page - 1) * $perPage;

        // Xây dựng query tìm kiếm
        $where = [];
        $params = [];

        if (!empty($logAction)) {
            $where[] = "l.action = ?";
            $params[] = $logAction;
        }

        if (!empty($status)) {
            $where[] = "l.status = ?";
            $params[] = $status;
        }

        if (!empty($adminId)) {
            $where[] = "l.user_id = ?";
            $params[] = $adminId;
        }

        // Lấy danh sách admin từ gm_logs
        $admins = $this->db->fetchAll(
            "SELECT DISTINCT u.id, u.username 
             FROM gm_logs l 
             LEFT JOIN gm_users u ON l.user_id = u.id 
             WHERE u.id IS NOT NULL 
             ORDER BY u.username"
        );

        // Xây dựng query đếm tổng số bản ghi
        $countQuery = "SELECT COUNT(*) as total FROM gm_logs l";
        if (!empty($where)) {
            $countQuery .= " WHERE " . implode(' AND ', $where);
        }
        $total = (int)$this->db->fetch($countQuery, $params)['total'];

        // Tính toán phân trang
        $totalPages = max(1, ceil($total / $perPage));
        $page = min($page, $totalPages);
        $offset = ($page - 1) * $perPage;
        
        $pagination = [
            'current' => $page,
            'total' => $totalPages,
            'per_page' => $perPage,
            'total_items' => $total,
            'max_links' => 5
        ];

        // Xây dựng query lấy dữ liệu
        $query = "SELECT l.*, u.username 
                 FROM gm_logs l 
                 LEFT JOIN gm_users u ON l.user_id = u.id";
        if (!empty($where)) {
            $query .= " WHERE " . implode(' AND ', $where);
        }
        $query .= " ORDER BY l.created_at DESC";
        
        if ($total > 0) {
            $query .= " LIMIT " . (int)$perPage . " OFFSET " . (int)$offset;
        }
        
        // Lấy danh sách logs
        $logs = $this->db->fetchAll($query, $params);

        // Format dữ liệu
        if (!empty($logs)) {
            foreach ($logs as &$log) {
                $log['data'] = json_decode($log['data'], true);
                $log['created_at'] = date('Y-m-d H:i:s', strtotime($log['created_at']));
            }
            unset($log);
        }

        // Lấy danh sách action và status duy nhất
        $actionQuery = "SELECT DISTINCT action FROM gm_logs";
        $statusQuery = "SELECT DISTINCT status FROM gm_logs";
        
        if (!empty($adminId)) {
            $actionQuery .= " WHERE user_id = ?";
            $statusQuery .= " WHERE user_id = ?";
            $actionParams = [$adminId];
            $statusParams = [$adminId];
        } else {
            $actionParams = [];
            $statusParams = [];
        }
        
        $actionQuery .= " ORDER BY action";
        $statusQuery .= " ORDER BY status";
        
        $actions = $this->db->fetchAll($actionQuery, $actionParams);
        $statuses = $this->db->fetchAll($statusQuery, $statusParams);

        // Nếu không có dữ liệu và có tham số tìm kiếm, chuyển hướng về trang chính
        if (empty($logs) && (!empty($logAction) || !empty($status) || !empty($adminId))) {
            $redirectUrl = '/public/?route=log';
            if (!empty($adminId)) {
                $redirectUrl .= '&admin_id=' . urlencode($adminId);
            }
            if (!empty($logAction)) {
                $redirectUrl .= '&log_action=' . urlencode($logAction);
            }
            if (!empty($status)) {
                $redirectUrl .= '&status=' . urlencode($status);
            }
            header('Location: ' . $redirectUrl);
            exit;
        }

        require_once __DIR__ . '/../../resources/views/log/index.php';
    }
} 