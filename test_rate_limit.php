<?php
/**
 * Test script for IP Rate Limiting
 * Run this script to test rate limiting functionality
 */

// Test configuration
$apiBaseUrl = 'http://localhost/api'; // Change this to your API URL
$testEmail = '<EMAIL>';

echo "Testing IP Rate Limiting...\n\n";

/**
 * Test sendEmailVerification rate limiting
 */
function testSendEmailVerificationRateLimit($apiBaseUrl, $testEmail) {
    echo "=== Testing sendEmailVerification Rate Limiting ===\n";
    echo "Limit: 3 requests per minute per IP\n\n";
    
    // Create a dummy token for testing (you may need to get a real token)
    $token = 'dummy_token_for_testing';
    
    for ($i = 1; $i <= 5; $i++) {
        echo "Request #{$i}: ";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiBaseUrl . '/GameUser/SendEmailVerification');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['email' => $testEmail]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $token
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            echo "CURL Error\n";
            continue;
        }
        
        $data = json_decode($response, true);
        
        if ($httpCode === 429) {
            echo "✅ Rate limited (HTTP 429) - " . ($data['message'] ?? 'Rate limit exceeded') . "\n";
        } elseif ($httpCode === 200) {
            echo "✅ Request successful (HTTP 200)\n";
        } else {
            echo "❌ Unexpected response (HTTP {$httpCode}) - " . ($data['message'] ?? 'Unknown error') . "\n";
        }
        
        // Small delay between requests
        usleep(100000); // 0.1 second
    }
    
    echo "\n";
}

/**
 * Test forgotPassword rate limiting
 */
function testForgotPasswordRateLimit($apiBaseUrl, $testEmail) {
    echo "=== Testing forgotPassword Rate Limiting ===\n";
    echo "Limit: 5 requests per minute per IP\n\n";
    
    for ($i = 1; $i <= 7; $i++) {
        echo "Request #{$i}: ";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiBaseUrl . '/GameUser/ForgotPassword');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['email' => $testEmail]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            echo "CURL Error\n";
            continue;
        }
        
        $data = json_decode($response, true);
        
        if ($httpCode === 429) {
            echo "✅ Rate limited (HTTP 429) - " . ($data['message'] ?? 'Rate limit exceeded') . "\n";
        } elseif ($httpCode === 200) {
            echo "✅ Request successful (HTTP 200)\n";
        } else {
            echo "❌ Unexpected response (HTTP {$httpCode}) - " . ($data['message'] ?? 'Unknown error') . "\n";
        }
        
        // Small delay between requests
        usleep(100000); // 0.1 second
    }
    
    echo "\n";
}

/**
 * Test resetPassword rate limiting
 */
function testResetPasswordRateLimit($apiBaseUrl) {
    echo "=== Testing resetPassword Rate Limiting ===\n";
    echo "Limit: 10 requests per minute per IP\n\n";
    
    $dummyToken = 'dummy_reset_token_for_testing';
    
    for ($i = 1; $i <= 12; $i++) {
        echo "Request #{$i}: ";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiBaseUrl . '/GameUser/ResetPassword');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
            'token' => $dummyToken,
            'new_password' => 'newpassword123',
            'confirm_password' => 'newpassword123'
        ]));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response === false) {
            echo "CURL Error\n";
            continue;
        }
        
        $data = json_decode($response, true);
        
        if ($httpCode === 429) {
            echo "✅ Rate limited (HTTP 429) - " . ($data['message'] ?? 'Rate limit exceeded') . "\n";
        } elseif ($httpCode === 200 || $httpCode === 400) {
            // 400 is expected for invalid token, but rate limiting should still work
            echo "✅ Request processed (HTTP {$httpCode})\n";
        } else {
            echo "❌ Unexpected response (HTTP {$httpCode}) - " . ($data['message'] ?? 'Unknown error') . "\n";
        }
        
        // Small delay between requests
        usleep(100000); // 0.1 second
    }
    
    echo "\n";
}

// Run tests
echo "Starting Rate Limiting Tests...\n";
echo "API Base URL: {$apiBaseUrl}\n";
echo "Test Email: {$testEmail}\n\n";

// Test each endpoint
testForgotPasswordRateLimit($apiBaseUrl, $testEmail);
testResetPasswordRateLimit($apiBaseUrl);
// testSendEmailVerificationRateLimit($apiBaseUrl, $testEmail); // Requires valid token

echo "=== Rate Limiting Tests Completed ===\n";
echo "Note: sendEmailVerification test is commented out because it requires a valid authentication token.\n";
echo "You can uncomment and provide a real token to test that endpoint.\n";
