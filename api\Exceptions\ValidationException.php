<?php
namespace Api\Exceptions;

class ValidationException extends \Exception
{
    protected $errors = [];

    public function __construct(array $errors = [], string $message = 'Validation failed', int $code = 0, \Throwable $previous = null)
    {
        $this->errors = $errors;
        parent::__construct($message, $code, $previous);
    }

    /**
     * <PERSON><PERSON>y tất cả lỗi validation
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    /**
     * Lấy lỗi đầu tiên
     */
    public function getFirstError(): string
    {
        if (empty($this->errors)) {
            return $this->getMessage();
        }

        $firstFieldErrors = reset($this->errors);
        return is_array($firstFieldErrors) ? $firstFieldErrors[0] : $firstFieldErrors;
    }

    /**
     * Lấy lỗi theo field
     */
    public function getErrorsForField(string $field): array
    {
        return $this->errors[$field] ?? [];
    }

    /**
     * Ki<PERSON>m tra có lỗi cho field không
     */
    public function hasErrorsForField(string $field): bool
    {
        return isset($this->errors[$field]) && !empty($this->errors[$field]);
    }
} 