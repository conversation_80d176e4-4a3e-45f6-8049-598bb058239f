<?php

namespace Api\Controllers;

use App\Core\Controller;
use Api\Core\ApiHandler;
use Exception;

class NoticeController extends Controller
{
    use ApiHandler;
    
    private $noticeFile;
    private $policyFile;
    private $termsFile;

    public function __construct($db, $config)
    {
        parent::__construct($db, $config);
        $this->noticeFile = __DIR__ . '/../../data/notice.json';
        $this->policyFile = __DIR__ . '/../../data/chinhsach.txt';
        $this->termsFile = __DIR__ . '/../../data/dieukhoan.txt';
        // Lazy loading - chỉ khởi tạo khi cần
    }

    private function checkFilePermission($file)
    {
        if (!file_exists($file)) {
            throw new Exception('File không tồn tại');
        }

        if (!is_readable($file)) {
            throw new Exception('Không có quyền đọc file');
        }

        return true;
    }

    public function getNotices($params = [])
    {
        return $this->apiEndpoint('get_notices', function() {
            $this->checkFilePermission($this->noticeFile);

            $notices = json_decode(file_get_contents($this->noticeFile), true);
            if (!isset($notices['data']) || !is_array($notices['data'])) {
                throw new Exception('Định dạng file thông báo không hợp lệ');
            }

            $this->jsonResponse([
                'ret' => 0,
                'data' => $notices['data']
            ]);
        });
    }

    public function getPolicy($params = [])
    {
        return $this->apiEndpoint('get_policy', function() {
            $this->checkFilePermission($this->policyFile);

            $content = file_get_contents($this->policyFile);
            header('Content-Type: text/plain; charset=utf-8');
            echo $content;
            exit;
        });
    }

    public function getTerms($params = [])
    {
        return $this->apiEndpoint('get_terms', function() {
            $this->checkFilePermission($this->termsFile);

            $content = file_get_contents($this->termsFile);
            header('Content-Type: text/plain; charset=utf-8');
            echo $content;
            exit;
        });
    }
} 