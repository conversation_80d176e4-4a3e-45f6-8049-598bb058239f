<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">Quản lý máy chủ</h1>
        <?php if ($canCreate): ?>
        <a href="/public/?route=server&action=create" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            <i class="fas fa-plus mr-2"></i>Thêm máy chủ
        </a>
        <?php endif; ?>
    </div>

    <div class="mb-4">
        <span class="font-semibold">Trạng thái bảo trì toàn hệ thống:</span>
        <?php if ($isGlobalMaintenance): ?>
            <span class="px-2 py-1 text-xs rounded bg-yellow-200 text-yellow-900 font-semibold">Bảo trì</span>
        <?php else: ?>
            <span class="px-2 py-1 text-xs rounded bg-green-100 text-green-800">Hoạt động</span>
        <?php endif; ?>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <?php 
        echo $_SESSION['success'];
        unset($_SESSION['success']);
        ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <?php 
        echo $_SESSION['error'];
        unset($_SESSION['error']);
        ?>
    </div>
    <?php endif; ?>

    <?php
    // Xây dựng danh sách server con cho từng server mẹ
    $childrenMap = [];
    foreach ($servers as $s) {
        if (!empty($s['merged_into'])) {
            $childrenMap[$s['merged_into']][] = $s['id'];
        }
    }
    ?>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tên máy chủ</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Port</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian mở</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($servers)): ?>
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">Không có máy chủ nào</td>
                </tr>
                <?php else: ?>
                <?php foreach ($servers as $server): ?>
                    <?php if (empty($server['merged_into'])): ?>
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $server['id']; ?></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo htmlspecialchars($server['name']); ?>
                            <?php
                            // Hiển thị số lượng và danh sách id các server con đã gộp vào
                            if (!empty($childrenMap[$server['id']])) {
                                $ids = $childrenMap[$server['id']];
                                echo '<span class="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">Gộp: ' . count($ids) . ' (ID: ' . implode(', ', $ids) . ')</span>';
                            }
                            ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php 
                            $fp = @fsockopen($server['ip'], $server['port'], $errno, $errstr, 1);
                            $isConnected = $fp !== false;
                            if ($fp) fclose($fp);
                            ?>
                            <span class="px-2 py-1 text-xs rounded <?php echo $isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo htmlspecialchars($server['ip']); ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <span class="px-2 py-1 text-xs rounded <?php echo $isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo $server['port']; ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php echo date('d/m/Y H:i', strtotime($server['opentime'])); ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <?php if (strtotime($server['opentime']) > $now): ?>
                            <span class="px-2 py-1 text-xs rounded bg-blue-100 text-blue-800">Chưa mở</span>
                            <?php else: ?>
                            <span class="px-2 py-1 text-xs rounded <?php echo $server['status'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                <?php echo $serverStatus[$server['status']] ?? 'Không xác định'; ?>
                            </span>
                            <?php endif; ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <?php if ($canEdit): ?>
                            <a href="/public/?route=server&action=edit&id=<?php echo $server['id']; ?>" class="text-blue-600 hover:text-blue-900 mr-3">
                                <i class="fas fa-edit"></i>
                            </a>
                            <?php endif; ?>
                            <?php if ($canMerge): ?>
                            <a href="/public/?route=server&action=merge&id=<?php echo $server['id']; ?>" class="text-purple-600 hover:text-purple-900">
                                <i class="fas fa-compress-arrows-alt"></i>
                            </a>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endif; ?>
                <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <!-- Phân trang -->
    <?php if ($pagination['total'] > 1): ?>
    <div class="mt-4 flex justify-center">
        <nav class="inline-flex -space-x-px">
            <?php for ($i = 1; $i <= $pagination['total']; $i++): ?>
                <a href="?route=server&page=<?php echo $i; ?>" class="px-3 py-1 border <?php echo $i == $pagination['current'] ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'; ?> border-gray-300 text-sm">
                    <?php echo $i; ?>
                </a>
            <?php endfor; ?>
        </nav>
    </div>
    <?php endif; ?>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 