<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
            <h3 class="text-lg leading-6 font-medium text-gray-900 text-left">
                <i class="fas fa-gift mr-2"></i>Danh sách nhóm Gift Code
            </h3>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-4 items-stretch w-full sm:w-auto">
                <form method="GET" action="/public/?route=giftcode" class="flex flex-col sm:flex-row gap-2 sm:gap-4 w-full sm:w-auto">
                    <input type="hidden" name="route" value="giftcode">
                    <div class="relative w-full sm:w-auto">
                        <input type="text" name="search" value="<?= htmlspecialchars($search ?? '') ?>" placeholder="Tìm kiếm nhóm code" class="block w-full sm:w-64 pl-3 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full sm:w-auto">
                        <i class="fas fa-search mr-2"></i>Tìm kiếm
                    </button>
                </form>
                <a href="?route=giftcode&action=create" class="inline-flex items-center px-4 py-2 border border-green-600 text-sm font-medium rounded-md shadow-sm text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 w-full sm:w-auto justify-center">
                    <i class="fas fa-plus mr-2"></i>Tạo nhóm code
                </a>
            </div>
        </div>

        <?php if (isset($_SESSION['success'])): ?>
            <div class="mt-4 p-4 rounded-md bg-green-50">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-green-800">
                            <?= htmlspecialchars($_SESSION['success']) ?>
                        </p>
                    </div>
                </div>
            </div>
            <?php unset($_SESSION['success']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mt-4 p-4 rounded-md bg-red-50">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium text-red-800">
                            <?= htmlspecialchars($_SESSION['error']) ?>
                        </p>
                    </div>
                </div>
            </div>
            <?php unset($_SESSION['error']); ?>
        <?php endif; ?>
    </div>
    <div class="border-t border-gray-200">
        <div class="p-4">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tên nhóm</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Loại</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Số code</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Đã dùng</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Item</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tình trạng</th>
                            <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($groups)): ?>
                        <tr>
                            <td colspan="7" class="px-4 py-4 text-center text-sm text-gray-500">Chưa có dữ liệu nhóm code.</td>
                        </tr>
                        <?php else: 
                            foreach ($groups as $group) {
                        ?>
                        <tr>
                            <td class="px-4 py-3"><?= htmlspecialchars($group['id']) ?></td>
                            <td class="px-4 py-3"><?= htmlspecialchars($group['group_name']) ?></td>
                            <td class="px-4 py-3"><?= $group['type'] === 'single' ? 'Code riêng' : 'Code chung' ?></td>
                            <td class="px-4 py-3"><?= $group['total_code'] ?></td>
                            <td class="px-4 py-3"><?= $group['used_code'] ?></td>
                            <td class="px-4 py-3">
                                <button type="button" 
                                        onclick="requestAndShowItemInfo('<?= htmlspecialchars($group['items']) ?>')"
                                        class="text-indigo-600 hover:text-indigo-900">
                                    <i class="fas fa-info-circle"></i> Xem
                                </button>
                            </td>
                            <td class="px-4 py-3">
                                <?php if ($group['is_disabled']): ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                        Vô hiệu hóa
                                    </span>
                                <?php else: ?>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        Hoạt động
                                    </span>
                                <?php endif; ?>
                                <?php if ($group['expired_at'] && strtotime($group['expired_at']) < time()): ?>
                                    <div class="mt-1">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Hết hạn
                                        </span>
                                    </div>
                                <?php endif; ?>
                            </td>
                            <td class="px-4 py-3">
                                <a href="?route=giftcode&action=export&id=<?= $group['id'] ?>" class="text-indigo-600 hover:underline mr-2"><i class="fas fa-file-excel"></i> Xuất</a>
                                <a href="?route=giftcode&action=edit&id=<?= $group['id'] ?>" class="text-indigo-600 hover:underline"><i class="fas fa-edit"></i> Sửa</a>
                            </td>
                        </tr>
                        <?php 
                            } // end foreach
                        endif; ?>
                    </tbody>
                </table>
            </div>
            <?php if ($pagination['total'] > 1): ?>
            <div class="mt-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 overflow-x-auto">
                <div class="text-xs sm:text-sm text-gray-700">
                    Hiển thị <span class="font-medium"><?= ($pagination['offset'] + 1) ?></span> đến 
                    <span class="font-medium"><?= min($pagination['offset'] + $pagination['perPage'], $pagination['total_items']) ?></span> của 
                    <span class="font-medium"><?= $pagination['total_items'] ?></span> kết quả
                </div>
                <div class="flex flex-wrap gap-1">
                    <?php if ($pagination['current'] > 1): ?>
                        <a href="?route=giftcode&page=1<?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                        <a href="?route=giftcode&page=<?= ($pagination['current'] - 1) ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $pagination['current'] - floor($pagination['max_links'] / 2));
                    $end = min($pagination['total'], $start + $pagination['max_links'] - 1);
                    $start = max(1, $end - $pagination['max_links'] + 1);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <a href="?route=giftcode&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md <?= $i == $pagination['current'] ? 'bg-indigo-600 text-white' : 'text-gray-700 bg-white hover:bg-gray-50' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($pagination['current'] < $pagination['total']): ?>
                        <a href="?route=giftcode&page=<?= ($pagination['current'] + 1) ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="?route=giftcode&page=<?= $pagination['total'] ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 