<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-50">
    <!-- Main Content -->
    <div class="flex flex-col lg:flex-row">
        <!-- Sidebar -->
        <div class="w-full lg:w-72 bg-gradient-to-br from-indigo-900 via-blue-900 to-indigo-800 text-white p-5 shadow-xl">
            <h2 class="text-xl font-bold mb-5 flex items-center bg-white/10 p-3 rounded-lg backdrop-blur-sm">
                <i class="fas fa-server mr-3 text-indigo-300"></i>
                <span class="bg-gradient-to-r from-blue-100 to-indigo-100 text-transparent bg-clip-text">Danh sách máy chủ</span>
            </h2>

            <!-- Server Search and Filter -->
            <div class="mb-4 space-y-3">
                <div class="relative">
                    <input type="text" id="serverSearchInput" placeholder="Tìm máy chủ..." class="w-full bg-indigo-800/30 border border-indigo-700 rounded-lg py-2 px-4 text-indigo-100 placeholder-indigo-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                    <i class="fas fa-search absolute right-3 top-3 text-indigo-300"></i>
                </div>

                <div class="flex flex-wrap gap-2">
                    <button type="button" id="showAllServersBtn" class="px-3 py-1.5 bg-indigo-700 hover:bg-indigo-600 text-indigo-100 rounded-lg text-xs font-medium transition-colors active-filter">
                        Tất cả
                    </button>
                    <button type="button" id="showNewServersBtn" class="px-3 py-1.5 bg-emerald-700 hover:bg-emerald-600 text-emerald-100 rounded-lg text-xs font-medium transition-colors">
                        Mới (30 ngày)
                    </button>
                    <button type="button" id="showOldServersBtn" class="px-3 py-1.5 bg-amber-700 hover:bg-amber-600 text-amber-100 rounded-lg text-xs font-medium transition-colors">
                        Cũ (>30 ngày)
                    </button>
                </div>
            </div>

            <div class="space-y-3" id="serverList">
                <?php
                $now = time();
                foreach ($groupedServers as $group):
                    $hasSelectedServer = false;
                    $hasNewServer = false;

                    // Check if group has selected server or new servers
                    if (isset($_GET['server_id'])) {
                        foreach ($group['servers'] as $server) {
                            if ($server['id'] == $_GET['server_id']) {
                                $hasSelectedServer = true;
                            }

                            $openTime = strtotime($server['opentime']);
                            $daysSinceOpen = floor(($now - $openTime) / (60 * 60 * 24));
                            if ($daysSinceOpen <= 30) {
                                $hasNewServer = true;
                            }
                        }
                    }
                ?>
                    <div class="server-group" data-has-new="<?php echo $hasNewServer ? 'true' : 'false'; ?>">
                        <div class="flex items-center justify-between p-3 bg-gradient-to-r from-blue-800/40 to-indigo-800/40 hover:from-blue-700/50 hover:to-indigo-700/50 rounded-lg cursor-pointer transition-all duration-300 shadow-md"
                             onclick="toggleGroup(this)">
                            <div class="flex items-center">
                                <i class="fas fa-chevron-right mr-3 transition-transform duration-300 text-indigo-300 group-indicator"></i>
                                <span class="font-medium text-indigo-100">Khu <?php echo $group['start_id']; ?></span>
                                <?php if ($hasNewServer): ?>
                                <span class="ml-2 text-xs bg-emerald-600 text-white px-2 py-0.5 rounded-full">Mới</span>
                                <?php endif; ?>
                            </div>
                            <span class="text-xs bg-indigo-800/50 text-indigo-200 px-2 py-1 rounded-full hidden sm:inline">[S<?php echo $group['start_id']; ?>-S<?php echo $group['end_id']; ?>]</span>
                        </div>
                        <div class="pl-4 space-y-2 mt-2 server-items <?php echo $hasSelectedServer ? '' : 'hidden'; ?>">
                            <?php foreach ($group['servers'] as $server):
                                $openTime = strtotime($server['opentime']);
                                $daysSinceOpen = floor(($now - $openTime) / (60 * 60 * 24));
                                $isNewServer = $daysSinceOpen <= 30;
                                $serverClass = $isNewServer ? 'new-server' : 'old-server';
                            ?>
                                <a href="?route=character&server_id=<?php echo $server['id']; ?>"
                                   class="server-item <?php echo $serverClass; ?> block p-3 rounded-lg transition-all duration-300 <?php
                                       echo isset($_GET['server_id']) && $_GET['server_id'] == $server['id']
                                           ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg'
                                           : 'text-blue-100 hover:bg-gradient-to-r hover:from-blue-800/30 hover:to-indigo-800/30 hover:text-white hover:shadow-md';
                                   ?>"
                                   data-server-name="<?php echo htmlspecialchars(strtolower($server['name'])); ?>"
                                   data-server-id="S<?php echo $server['id']; ?>"
                                   data-days="<?php echo $daysSinceOpen; ?>">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <span class="font-medium text-lg">S<?php echo $server['id']; ?></span>
                                            <span class="ml-2 truncate max-w-[150px] sm:max-w-none"><?php echo htmlspecialchars($server['name']); ?></span>
                                            <?php if ($isNewServer): ?>
                                            <span class="ml-2 text-xs bg-emerald-600 text-white px-1.5 py-0.5 rounded-full">Mới</span>
                                            <?php endif; ?>
                                        </div>
                                        <?php
                                            $statusClass = '';
                                            $statusText = '';
                                            $statusIcon = '';
                                            switch ($server['status']) {
                                                case 1:
                                                    $statusClass = 'bg-gradient-to-r from-green-500 to-emerald-600';
                                                    $statusText = 'Mượt';
                                                    $statusIcon = 'fa-check-circle';
                                                    break;
                                                case 2:
                                                    $statusClass = 'bg-gradient-to-r from-yellow-400 to-amber-500';
                                                    $statusText = 'Đông';
                                                    $statusIcon = 'fa-users';
                                                    break;
                                                case 5:
                                                    $statusClass = 'bg-gradient-to-r from-red-500 to-rose-600';
                                                    $statusText = 'Bảo Trì';
                                                    $statusIcon = 'fa-tools';
                                                    break;
                                            }
                                        ?>
                                        <span class="text-xs <?php echo $statusClass; ?> text-white px-3 py-1 rounded-full flex items-center shadow-md">
                                            <i class="fas <?php echo $statusIcon; ?> mr-1"></i>
                                            <?php echo $statusText; ?>
                                        </span>
                                    </div>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-1 p-4 sm:p-6">
            <?php if (isset($_GET['server_id'])): ?>
                <div class="bg-white rounded-xl shadow-xl overflow-hidden border border-gray-100">
                    <!-- Thống kê -->
                    <div class="p-6 bg-gradient-to-r from-indigo-50 to-blue-50">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-chart-bar text-indigo-600 mr-2"></i>
                            Thống kê tổng quan
                        </h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-indigo-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-indigo-500 to-indigo-600 text-white shadow-lg">
                                        <i class="fas fa-users text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Tổng nhân vật</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 text-transparent bg-clip-text"><?php echo number_format($totalCharacters); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-green-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg">
                                        <i class="fas fa-coins text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Tổng doanh thu</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 text-transparent bg-clip-text"><?php echo number_format($totalRevenue); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-blue-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-blue-500 to-sky-600 text-white shadow-lg">
                                        <i class="fas fa-chart-line text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Doanh thu hôm nay</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-blue-600 to-sky-600 text-transparent bg-clip-text"><?php echo number_format($todayRevenue); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-purple-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-purple-500 to-violet-600 text-white shadow-lg">
                                        <i class="fas fa-crown text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Số Người chơi nạp tiền</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-purple-600 to-violet-600 text-transparent bg-clip-text"><?php echo number_format($totalPayers); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ARPU & ARPPU -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5 mt-5">
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-amber-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-amber-500 to-amber-600 text-white shadow-lg">
                                        <i class="fas fa-dollar-sign text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">ARPU</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-amber-600 to-amber-700 text-transparent bg-clip-text"><?php echo number_format($arpu); ?></div>
                                        <div class="text-xs text-gray-500">Doanh thu trung bình/người chơi</div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-rose-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-rose-500 to-rose-600 text-white shadow-lg">
                                        <i class="fas fa-gem text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">ARPPU</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-rose-600 to-rose-700 text-transparent bg-clip-text"><?php echo number_format($arppu); ?></div>
                                        <div class="text-xs text-gray-500">Doanh thu trung bình/người trả phí</div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-teal-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-teal-500 to-teal-600 text-white shadow-lg">
                                        <i class="fas fa-level-up-alt text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Cấp độ nạp đầu tiên</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-teal-600 to-teal-700 text-transparent bg-clip-text"><?php echo number_format($avgFirstPaymentLevel, 1); ?></div>
                                        <div class="text-xs text-gray-500">Trung bình</div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-fuchsia-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-fuchsia-500 to-fuchsia-600 text-white shadow-lg">
                                        <i class="fas fa-percentage text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Tỷ lệ chi trả</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-fuchsia-600 to-fuchsia-700 text-transparent bg-clip-text"><?php echo $totalCharacters > 0 ? number_format(($totalPayers / $totalCharacters) * 100, 1) : '0'; ?>%</div>
                                        <div class="text-xs text-gray-500">Người chơi có nạp tiền</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thống kê mới -->
                    <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-t border-gray-100">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-user-check text-blue-600 mr-2"></i>
                            Thống kê hoạt động
                        </h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-5">
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-yellow-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-yellow-500 to-amber-600 text-white shadow-lg">
                                        <i class="fas fa-user-clock text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Nhân vật online hôm nay</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-yellow-600 to-amber-600 text-transparent bg-clip-text"><?php echo number_format($onlineCount); ?></div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-red-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-red-500 to-rose-600 text-white shadow-lg">
                                        <i class="fas fa-chart-pie text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Tỷ lệ active hôm nay</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-red-600 to-rose-600 text-transparent bg-clip-text"><?php echo $activeRate; ?>%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-teal-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-teal-500 to-emerald-600 text-white shadow-lg">
                                        <i class="fas fa-clock text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Online trung bình</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-teal-600 to-emerald-600 text-transparent bg-clip-text"><?php echo $avgOnlineTime; ?>h</div>
                                    </div>
                                </div>
                            </div>
                            <div class="bg-white p-5 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-pink-100 transform hover:-translate-y-1">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-gradient-to-br from-pink-500 to-rose-600 text-white shadow-lg">
                                        <i class="fas fa-shopping-cart text-xl"></i>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-500">Số đơn đã thanh toán</div>
                                        <div class="mt-1 text-2xl font-bold bg-gradient-to-r from-pink-600 to-rose-600 text-transparent bg-clip-text"><?php echo number_format($totalOrders); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thống kê cấp độ và VIP -->
                    <div class="p-6 bg-gradient-to-r from-amber-50 to-yellow-50 border-t border-gray-100">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-level-up-alt text-amber-600 mr-2"></i>
                            Phân bố cấp độ và VIP
                        </h3>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Phân bố cấp độ -->
                            <div class="bg-white p-5 rounded-xl shadow-md border border-amber-100">
                                <h4 class="text-lg font-semibold text-amber-700 mb-3 flex items-center">
                                    <i class="fas fa-chart-bar text-amber-500 mr-2"></i>
                                    Phân bố cấp độ
                                </h4>
                                <div class="space-y-3">
                                    <?php
                                    $totalCount = array_sum(array_column($levelDistribution, 'count'));
                                    foreach ($levelDistribution as $level):
                                        $percent = $totalCount > 0 ? ($level['count'] / $totalCount) * 100 : 0;
                                    ?>
                                    <div>
                                        <div class="flex justify-between items-center mb-1">
                                            <span class="text-sm font-medium text-gray-700">Cấp <?php echo $level['level_range']; ?></span>
                                            <span class="text-xs text-gray-500"><?php echo number_format($level['count']); ?> người chơi (<?php echo number_format($percent, 1); ?>%)</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-gradient-to-r from-amber-500 to-yellow-500 h-2.5 rounded-full" style="width: <?php echo $percent; ?>%"></div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Thông tin cấp độ -->
                                <div class="mt-4 pt-4 border-t border-gray-100">
                                    <div class="grid grid-cols-2 gap-3">
                                        <div class="bg-amber-50 p-3 rounded-lg">
                                            <div class="text-xs text-gray-500">Cấp độ cao nhất</div>
                                            <div class="text-lg font-bold text-amber-700"><?php echo number_format($maxLevel); ?></div>
                                        </div>
                                        <div class="bg-amber-50 p-3 rounded-lg">
                                            <div class="text-xs text-gray-500">Cấp độ trung bình</div>
                                            <div class="text-lg font-bold text-amber-700"><?php echo number_format($avgLevel, 1); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Phân bố VIP -->
                            <div class="bg-white p-5 rounded-xl shadow-md border border-purple-100">
                                <h4 class="text-lg font-semibold text-purple-700 mb-3 flex items-center">
                                    <i class="fas fa-crown text-purple-500 mr-2"></i>
                                    Phân bố VIP
                                </h4>
                                <div class="space-y-3">
                                    <?php
                                    $totalVipCount = array_sum(array_column($vipDistribution, 'count'));
                                    foreach ($vipDistribution as $vip):
                                        $vipPercent = $totalVipCount > 0 ? ($vip['count'] / $totalVipCount) * 100 : 0;
                                    ?>
                                    <div>
                                        <div class="flex justify-between items-center mb-1">
                                            <span class="text-sm font-medium text-gray-700"><?php echo $vip['vip_range']; ?></span>
                                            <span class="text-xs text-gray-500"><?php echo number_format($vip['count']); ?> người chơi (<?php echo number_format($vipPercent, 1); ?>%)</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2.5">
                                            <div class="bg-gradient-to-r from-purple-500 to-fuchsia-500 h-2.5 rounded-full" style="width: <?php echo $vipPercent; ?>%"></div>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>

                                <!-- Thông tin VIP -->
                                <div class="mt-4 pt-4 border-t border-gray-100">
                                    <div class="grid grid-cols-2 gap-3">
                                        <div class="bg-purple-50 p-3 rounded-lg">
                                            <div class="text-xs text-gray-500">VIP cao nhất</div>
                                            <div class="text-lg font-bold text-purple-700"><?php echo number_format($maxVip); ?></div>
                                        </div>
                                        <div class="bg-purple-50 p-3 rounded-lg">
                                            <div class="text-xs text-gray-500">VIP trung bình</div>
                                            <div class="text-lg font-bold text-purple-700"><?php echo number_format($avgVip, 1); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thống kê người chơi mới và top nạp tiền -->
                    <div class="p-6 bg-gradient-to-r from-emerald-50 to-teal-50 border-t border-gray-100">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Người chơi mới theo ngày -->
                            <div>
                                <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-user-plus text-emerald-600 mr-2"></i>
                                    Người chơi mới (7 ngày gần đây)
                                </h3>
                                <div class="bg-white p-5 rounded-xl shadow-md border border-emerald-100">
                                    <?php if (!empty($newPlayersData)): ?>
                                        <div class="space-y-3">
                                            <?php
                                            $maxNewPlayers = max(array_column($newPlayersData, 'count'));
                                            foreach ($newPlayersData as $data):
                                                $percent = $maxNewPlayers > 0 ? ($data['count'] / $maxNewPlayers) * 100 : 0;
                                            ?>
                                            <div>
                                                <div class="flex justify-between items-center mb-1">
                                                    <span class="text-sm font-medium text-gray-700"><?php echo date('d/m/Y', strtotime($data['date'])); ?></span>
                                                    <span class="text-xs text-gray-500"><?php echo number_format($data['count']); ?> người chơi mới</span>
                                                </div>
                                                <div class="w-full bg-gray-200 rounded-full h-2">
                                                    <div class="bg-gradient-to-r from-emerald-500 to-teal-500 h-2 rounded-full" style="width: <?php echo $percent; ?>%"></div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4 text-gray-500">
                                            <i class="fas fa-info-circle text-emerald-400 text-xl mb-2"></i>
                                            <p>Không có dữ liệu người chơi mới</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <!-- Top người chơi nạp tiền -->
                            <div>
                                <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                                    <i class="fas fa-trophy text-amber-600 mr-2"></i>
                                    Top 5 người chơi nạp tiền
                                </h3>
                                <div class="bg-white p-5 rounded-xl shadow-md border border-amber-100">
                                    <?php if (!empty($topPayers)): ?>
                                        <div class="space-y-4">
                                            <?php foreach ($topPayers as $index => $payer): ?>
                                            <div class="flex items-center">
                                                <div class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center <?php echo $index === 0 ? 'bg-gradient-to-br from-amber-400 to-yellow-500' : ($index === 1 ? 'bg-gradient-to-br from-gray-300 to-gray-400' : ($index === 2 ? 'bg-gradient-to-br from-amber-700 to-amber-800' : 'bg-gradient-to-br from-indigo-400 to-indigo-500')); ?> text-white font-bold shadow-md">
                                                    <?php echo $index + 1; ?>
                                                </div>
                                                <div class="ml-4 flex-1">
                                                    <div class="flex justify-between items-center">
                                                        <div>
                                                            <h4 class="text-base font-semibold text-indigo-700"><?php echo htmlspecialchars($payer['role_name']); ?></h4>
                                                            <div class="flex items-center text-xs text-gray-500 mt-1">
                                                                <span class="flex items-center mr-3">
                                                                    <i class="fas fa-level-up-alt text-blue-500 mr-1"></i>
                                                                    Cấp <?php echo $payer['level']; ?>
                                                                </span>
                                                                <span class="flex items-center">
                                                                    <i class="fas fa-crown text-purple-500 mr-1"></i>
                                                                    VIP <?php echo $payer['vip_level']; ?>
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <div class="text-right">
                                                            <div class="text-base font-bold text-emerald-600"><?php echo number_format($payer['gold_history']); ?></div>
                                                            <div class="text-xs text-gray-500">Tổng nạp</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4 text-gray-500">
                                            <i class="fas fa-info-circle text-amber-400 text-xl mb-2"></i>
                                            <p>Không có dữ liệu người chơi nạp tiền</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Phân tích hành vi chi trả theo cấp độ -->
                    <div class="p-6 bg-gradient-to-r from-cyan-50 to-sky-50 border-t border-gray-100">
                        <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                            <i class="fas fa-chart-line text-cyan-600 mr-2"></i>
                            Phân tích hành vi chi trả theo cấp độ
                        </h3>

                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
                            <div class="bg-white p-5 rounded-xl shadow-md border border-cyan-100">
                                <h4 class="text-lg font-semibold text-cyan-700 mb-3 flex items-center">
                                    <i class="fas fa-level-up-alt text-cyan-500 mr-2"></i>
                                    Cấp độ chi trả đầu tiên
                                </h4>
                                <div class="grid grid-cols-3 gap-3">
                                    <div class="bg-cyan-50 p-3 rounded-lg text-center">
                                        <div class="text-xs text-gray-500">Trung bình</div>
                                        <div class="text-xl font-bold text-cyan-700"><?php echo number_format($avgFirstPaymentLevel, 1); ?></div>
                                    </div>
                                    <div class="bg-cyan-50 p-3 rounded-lg text-center">
                                        <div class="text-xs text-gray-500">Thấp nhất</div>
                                        <div class="text-xl font-bold text-cyan-700"><?php echo number_format($minPaymentLevel); ?></div>
                                    </div>
                                    <div class="bg-cyan-50 p-3 rounded-lg text-center">
                                        <div class="text-xs text-gray-500">Cao nhất</div>
                                        <div class="text-xl font-bold text-cyan-700"><?php echo number_format($maxPaymentLevel); ?></div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white p-5 rounded-xl shadow-md border border-sky-100 col-span-2">
                                <h4 class="text-lg font-semibold text-sky-700 mb-3 flex items-center">
                                    <i class="fas fa-info-circle text-sky-500 mr-2"></i>
                                    Thông tin chi trả
                                </h4>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-sky-50 p-3 rounded-lg">
                                        <div class="text-xs text-gray-500">Tổng số người trả phí</div>
                                        <div class="text-xl font-bold text-sky-700"><?php echo number_format($totalPayers); ?></div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            <?php echo $totalCharacters > 0 ? number_format(($totalPayers / $totalCharacters) * 100, 1) : '0'; ?>% tổng người chơi
                                        </div>
                                    </div>
                                    <div class="bg-sky-50 p-3 rounded-lg">
                                        <div class="text-xs text-gray-500">Tổng giao dịch thành công</div>
                                        <div class="text-xl font-bold text-sky-700"><?php echo number_format($totalOrders); ?></div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            <?php echo $totalPayers > 0 ? number_format($totalOrders / $totalPayers, 1) : '0'; ?> giao dịch/người
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Chi tiêu theo nhóm cấp độ -->
                        <div class="bg-white p-5 rounded-xl shadow-md border border-blue-100">
                            <h4 class="text-lg font-semibold text-blue-700 mb-3 flex items-center">
                                <i class="fas fa-chart-bar text-blue-500 mr-2"></i>
                                Chi tiêu theo nhóm cấp độ
                            </h4>

                            <?php if (!empty($paymentByLevel)): ?>
                                <div class="overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead>
                                            <tr>
                                                <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nhóm cấp độ</th>
                                                <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số người chơi</th>
                                                <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Số giao dịch</th>
                                                <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tổng chi tiêu</th>
                                                <th class="px-4 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Chi tiêu trung bình</th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-white divide-y divide-gray-200">
                                            <?php
                                            $totalAmount = array_sum(array_column($paymentByLevel, 'total_amount'));
                                            foreach ($paymentByLevel as $index => $payment):
                                                $avgSpend = $payment['unique_payers'] > 0 ? $payment['total_amount'] / $payment['unique_payers'] : 0;
                                                $percentOfTotal = $totalAmount > 0 ? ($payment['total_amount'] / $totalAmount) * 100 : 0;
                                            ?>
                                            <tr class="<?php echo $index % 2 === 0 ? 'bg-white' : 'bg-gray-50'; ?>">
                                                <td class="px-4 py-3 whitespace-nowrap">
                                                    <div class="text-sm font-medium text-gray-900">Cấp <?php echo $payment['level_range']; ?></div>
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo number_format($payment['unique_payers']); ?></div>
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo number_format($payment['transaction_count']); ?></div>
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo number_format($payment['total_amount']); ?></div>
                                                    <div class="text-xs text-gray-500"><?php echo number_format($percentOfTotal, 1); ?>% tổng doanh thu</div>
                                                </td>
                                                <td class="px-4 py-3 whitespace-nowrap">
                                                    <div class="text-sm text-gray-900"><?php echo number_format($avgSpend); ?></div>
                                                </td>
                                            </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-4 text-gray-500">
                                    <i class="fas fa-info-circle text-blue-400 text-xl mb-2"></i>
                                    <p>Không có dữ liệu chi tiêu theo cấp độ</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Biểu đồ tỷ lệ active -->
                    <div class="p-6 bg-white border-t border-gray-100">
                        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-5 gap-3">
                            <h3 class="text-xl font-bold text-gray-800 flex items-center">
                                <i class="fas fa-chart-area text-indigo-600 mr-2"></i>
                                Tỷ lệ active theo ngày
                            </h3>
                            <?php
                            $opentime = strtotime($server['opentime']);
                            $now = time();
                            $daysSinceOpen = floor(($now - $opentime) / (60 * 60 * 24));
                            ?>
                            <div class="text-sm bg-indigo-50 px-4 py-2 rounded-lg shadow-sm">
                                <span class="font-medium text-gray-700">Đã open:</span>
                                <span class="text-indigo-700 font-bold"><?php echo $daysSinceOpen; ?></span> ngày
                                <span class="text-indigo-500 text-xs ml-2">(<?php echo date('d/m/Y', $opentime); ?>)</span>
                            </div>
                        </div>
                        <div class="grid grid-cols-3 sm:grid-cols-9 gap-3">
                            <?php for ($i = 2; $i <= 10; $i++): ?>
                                <div class="text-center p-3 bg-gradient-to-b from-indigo-50 to-blue-50 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1 transform">
                                    <div class="text-sm font-medium text-gray-600"><?php echo $i; ?> ngày</div>
                                    <div class="text-xl font-bold text-indigo-700 mt-1"><?php echo $activeRates[$i] ?? 0; ?>%</div>
                                    <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                                        <div class="bg-gradient-to-r from-indigo-500 to-blue-600 h-2 rounded-full" style="width: <?php echo min(100, ($activeRates[$i] ?? 0)); ?>%"></div>
                                    </div>
                                </div>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <!-- Search Bar -->
                    <div class="p-6 bg-gradient-to-r from-indigo-600/5 to-blue-600/5 border-t border-gray-100">
                        <form method="get" class="flex flex-col sm:flex-row gap-4 items-end">
                            <input type="hidden" name="route" value="character">
                            <input type="hidden" name="server_id" value="<?php echo htmlspecialchars($_GET['server_id']); ?>">
                            <div class="w-full sm:flex-1">
                                <label class="block text-base font-medium text-gray-700 mb-2 flex items-center">
                                    <i class="fas fa-search text-indigo-600 mr-2"></i>
                                    Tìm kiếm nhân vật
                                </label>
                                <div class="relative">
                                    <input type="text" name="keyword" value="<?php echo htmlspecialchars($_GET['keyword'] ?? ''); ?>"
                                           class="w-full h-12 text-lg rounded-xl border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 pl-4 pr-12 transition-all duration-300"
                                           placeholder="ID, tên nhân vật hoặc tài khoản">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none text-gray-400">
                                        <i class="fas fa-keyboard text-lg"></i>
                                    </div>
                                </div>
                            </div>
                            <button type="submit" class="w-full sm:w-auto h-12 bg-gradient-to-r from-indigo-600 to-blue-600 text-white px-8 py-2 rounded-xl hover:from-indigo-700 hover:to-blue-700 transition-all duration-300 flex items-center justify-center text-lg shadow-md hover:shadow-lg transform hover:-translate-y-1">
                                <i class="fas fa-search mr-2"></i>Tìm kiếm
                            </button>
                        </form>
                    </div>

                    <!-- Character Table -->
                    <div class="overflow-x-auto">
                        <!-- Desktop Table -->
                        <table class="min-w-full divide-y divide-gray-200 hidden sm:table">
                            <thead class="bg-gradient-to-r from-indigo-50 to-blue-50">
                                <tr>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">ID</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Tên nhân vật</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Tài khoản</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Tổng nạp</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Cấp độ</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">VIP</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Trạng thái</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Ngày tạo</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Đăng nhập lần cuối</th>
                                    <th class="px-4 py-4 text-left text-xs font-bold text-indigo-700 uppercase tracking-wider">Thao tác</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-100">
                                <?php if (!empty($characters)): ?>
                                    <?php foreach ($characters as $character): ?>
                                        <tr class="hover:bg-indigo-50/30 transition-colors duration-200">
                                            <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900"><?php echo $character['role_id']; ?></td>
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-indigo-700"><?php echo htmlspecialchars($character['role_name']); ?></div>
                                            </td>
                                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700"><?php echo htmlspecialchars($character['plat_user_name']); ?></td>
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-emerald-600"><?php echo $character['gold_history']; ?></div>
                                            </td>
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-blue-600"><?php echo $character['level']; ?></div>
                                            </td>
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <div class="text-sm font-semibold text-purple-600"><?php echo $character['vip_level']; ?></div>
                                            </td>
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <?php if ($character['is_online'] == 'Online'): ?>
                                                    <span class="px-3 py-1.5 text-xs font-medium rounded-full bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-sm flex items-center w-fit">
                                                        <i class="fas fa-circle text-xs mr-1.5 animate-pulse"></i>
                                                        Online
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-3 py-1.5 text-xs font-medium rounded-full bg-gray-200 text-gray-700 shadow-sm flex items-center w-fit">
                                                        <i class="fas fa-circle text-xs mr-1.5"></i>
                                                        Offline
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700"><?php echo $character['create_time']; ?></td>
                                            <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-700"><?php echo $character['lastlogintime']; ?></td>
                                            <td class="px-4 py-4 whitespace-nowrap">
                                                <div class="flex space-x-3">
                                                    <?php
                                                        $platUserName = explode('_', $character['plat_user_name'])[0];
                                                        $sign = $this->generateSign($platUserName);
                                                        $path = getenv('APP_CLIENT_DOMAIN') . "/index.php?user_id=" . $platUserName . "&sign=" . $sign . "&type=web&debug=true&api=". getenv('API_URL');
                                                    ?>
                                                    <a href="<?php echo $path; ?>"
                                                       class="text-blue-600 hover:text-blue-800 transition-colors duration-200 bg-blue-100 hover:bg-blue-200 p-2 rounded-lg"
                                                       target="_blank"
                                                       title="Vào game">
                                                        <i class="fas fa-gamepad"></i>
                                                    </a>
                                                    <a href="?route=character&action=logs&role_id=<?php echo $character['role_id']; ?>&server_id=<?php echo $serverId; ?>"
                                                       class="text-indigo-600 hover:text-indigo-800 transition-colors duration-200 bg-indigo-100 hover:bg-indigo-200 p-2 rounded-lg"
                                                       title="Xem log">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                    <button onclick="openMuteModal('<?php echo $character['role_id']; ?>', '<?php echo htmlspecialchars($character['role_name']); ?>')"
                                                            class="text-amber-600 hover:text-amber-800 transition-colors duration-200 bg-amber-100 hover:bg-amber-200 p-2 rounded-lg"
                                                            title="Cấm chat">
                                                        <i class="fas fa-comment-slash"></i>
                                                    </button>
                                                    <form method="post" class="inline" action="kick">
                                                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                        <input type="hidden" name="action" value="kick">
                                                        <input type="hidden" name="role_id" value="<?php echo $character['role_id']; ?>">
                                                        <button type="submit" class="text-red-600 hover:text-red-800 transition-colors duration-200 bg-red-100 hover:bg-red-200 p-2 rounded-lg" title="Đá khỏi game">
                                                            <i class="fas fa-sign-out-alt"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="10" class="px-4 py-6 text-center text-gray-500">
                                            <div class="flex flex-col items-center justify-center">
                                                <i class="fas fa-search text-4xl text-gray-300 mb-3"></i>
                                                <p class="text-lg">Không có dữ liệu</p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>

                        <!-- Mobile Cards -->
                        <div class="sm:hidden space-y-5 p-4">
                            <?php if (!empty($characters)): ?>
                                <?php foreach ($characters as $character): ?>
                                    <div class="bg-white rounded-xl shadow-md p-5 border border-indigo-100 hover:shadow-lg transition-all duration-300">
                                        <div class="flex justify-between items-start mb-4">
                                            <div>
                                                <h3 class="text-lg font-bold text-indigo-700"><?php echo htmlspecialchars($character['role_name']); ?></h3>
                                                <p class="text-sm text-gray-600 mt-1 flex items-center">
                                                    <i class="fas fa-id-card text-indigo-500 mr-1.5"></i>
                                                    ID: <?php echo $character['role_id']; ?>
                                                </p>
                                            </div>
                                            <?php if ($character['is_online'] == 'Online'): ?>
                                                <span class="px-3 py-1.5 text-xs font-medium rounded-full bg-gradient-to-r from-green-500 to-emerald-600 text-white shadow-sm flex items-center">
                                                    <i class="fas fa-circle text-xs mr-1.5 animate-pulse"></i>
                                                    Online
                                                </span>
                                            <?php else: ?>
                                                <span class="px-3 py-1.5 text-xs font-medium rounded-full bg-gray-200 text-gray-700 shadow-sm flex items-center">
                                                    <i class="fas fa-circle text-xs mr-1.5"></i>
                                                    Offline
                                                </span>
                                            <?php endif; ?>
                                        </div>

                                        <div class="grid grid-cols-2 gap-3 text-sm bg-indigo-50/50 p-3 rounded-lg mb-4">
                                            <div class="flex items-center">
                                                <i class="fas fa-level-up-alt text-blue-600 mr-2"></i>
                                                <span class="text-gray-600">Cấp độ:</span>
                                                <span class="font-bold text-blue-700 ml-1"><?php echo $character['level']; ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-crown text-purple-600 mr-2"></i>
                                                <span class="text-gray-600">VIP:</span>
                                                <span class="font-bold text-purple-700 ml-1"><?php echo $character['vip_level']; ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-coins text-amber-600 mr-2"></i>
                                                <span class="text-gray-600">Tổng nạp:</span>
                                                <span class="font-bold text-emerald-700 ml-1"><?php echo $character['gold_history']; ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-user text-indigo-600 mr-2"></i>
                                                <span class="text-gray-600">Tài khoản:</span>
                                                <span class="font-bold text-indigo-700 ml-1 truncate"><?php echo htmlspecialchars($character['plat_user_name']); ?></span>
                                            </div>
                                        </div>

                                        <div class="flex flex-col sm:flex-row justify-between gap-3">
                                            <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded-lg">
                                                <div class="flex items-center mb-1">
                                                    <i class="fas fa-calendar-plus text-indigo-500 mr-1.5"></i>
                                                    Ngày tạo: <span class="font-medium ml-1"><?php echo $character['create_time']; ?></span>
                                                </div>
                                                <div class="flex items-center">
                                                    <i class="fas fa-sign-in-alt text-indigo-500 mr-1.5"></i>
                                                    Đăng nhập: <span class="font-medium ml-1"><?php echo $character['lastlogintime']; ?></span>
                                                </div>
                                            </div>
                                            <div class="flex space-x-2 justify-end">
                                                <?php
                                                    $platUserName = explode('_', $character['plat_user_name'])[0];
                                                    $sign = $this->generateSign($platUserName);
                                                    $path = getenv('APP_CLIENT_DOMAIN') . "/index.php?user_id=" . $platUserName . "&sign=" . $sign . "&type=web&debug=true&api=". getenv('API_URL');
                                                ?>
                                                <a href="<?php echo $path; ?>"
                                                   class="text-blue-600 hover:text-blue-800 transition-colors duration-200 bg-blue-100 hover:bg-blue-200 p-2 rounded-lg"
                                                   target="_blank"
                                                   title="Vào game">
                                                    <i class="fas fa-gamepad"></i>
                                                </a>
                                                <a href="?route=character&action=logs&role_id=<?php echo $character['role_id']; ?>&server_id=<?php echo $serverId; ?>"
                                                   class="text-indigo-600 hover:text-indigo-800 transition-colors duration-200 bg-indigo-100 hover:bg-indigo-200 p-2 rounded-lg"
                                                   title="Xem log">
                                                    <i class="fas fa-history"></i>
                                                </a>
                                                <button onclick="openMuteModal('<?php echo $character['role_id']; ?>', '<?php echo htmlspecialchars($character['role_name']); ?>')"
                                                        class="text-amber-600 hover:text-amber-800 transition-colors duration-200 bg-amber-100 hover:bg-amber-200 p-2 rounded-lg"
                                                        title="Cấm chat">
                                                    <i class="fas fa-comment-slash"></i>
                                                </button>
                                                <form method="post" class="inline" action="kick">
                                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                    <input type="hidden" name="action" value="kick">
                                                    <input type="hidden" name="role_id" value="<?php echo $character['role_id']; ?>">
                                                    <button type="submit" class="text-red-600 hover:text-red-800 transition-colors duration-200 bg-red-100 hover:bg-red-200 p-2 rounded-lg" title="Đá khỏi game">
                                                        <i class="fas fa-sign-out-alt"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="bg-white rounded-xl shadow-md p-6 text-center">
                                    <div class="flex flex-col items-center justify-center">
                                        <i class="fas fa-search text-5xl text-gray-300 mb-4"></i>
                                        <p class="text-lg text-gray-500">Không có dữ liệu</p>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <?php if (($totalPages ?? 1) > 1): ?>
                        <?php
                        $maxLinks = $paginationConfig['max_links'] ?? 5;
                        $curPage = $page ?? 1;
                        $perPage = $paginationConfig['per_page'] ?? 10;
                        $start = max(1, $curPage - floor($maxLinks/2));
                        $end = min($totalPages, $start + $maxLinks - 1);
                        if ($end - $start + 1 < $maxLinks) $start = max(1, $end - $maxLinks + 1);
                        ?>
                        <div class="px-6 py-4 bg-gradient-to-r from-indigo-50 to-blue-50 border-t border-gray-200 rounded-b-xl">
                            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                                <div class="flex items-center space-x-2">
                                    <?php if ($curPage > 1): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>"
                                           class="px-3 py-2 rounded-lg border border-indigo-300 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-all duration-200 shadow-sm">
                                            <i class="fas fa-angle-double-left"></i>
                                        </a>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $curPage - 1])); ?>"
                                           class="px-3 py-2 rounded-lg border border-indigo-300 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-all duration-200 shadow-sm">
                                            <i class="fas fa-angle-left"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php for ($i = $start; $i <= $end; $i++): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                           class="px-4 py-2 rounded-lg <?php echo $i == $curPage
                                               ? 'bg-gradient-to-r from-indigo-600 to-blue-600 text-white shadow-md'
                                               : 'border border-indigo-300 text-indigo-700 hover:bg-indigo-100 shadow-sm'; ?> transition-all duration-200">
                                            <?php echo $i; ?>
                                        </a>
                                    <?php endfor; ?>

                                    <?php if ($curPage < $totalPages): ?>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $curPage + 1])); ?>"
                                           class="px-3 py-2 rounded-lg border border-indigo-300 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-all duration-200 shadow-sm">
                                            <i class="fas fa-angle-right"></i>
                                        </a>
                                        <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>"
                                           class="px-3 py-2 rounded-lg border border-indigo-300 text-sm font-medium text-indigo-700 hover:bg-indigo-100 transition-all duration-200 shadow-sm">
                                            <i class="fas fa-angle-double-right"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                                <div class="text-sm text-indigo-800 bg-white px-4 py-2 rounded-lg shadow-sm">
                                    Hiển thị <span class="font-bold"><?php echo ($offset ?? 0) + 1; ?></span> đến
                                    <span class="font-bold"><?php echo min(($offset ?? 0) + ($perPage ?? 10), $totalCharacters ?? 0); ?></span> của
                                    <span class="font-bold"><?php echo number_format($totalCharacters ?? 0); ?></span> kết quả
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php else: ?>
                <div class="bg-white rounded-xl shadow-xl p-8 sm:p-12 text-center border border-indigo-100">
                    <div class="bg-indigo-50 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-gamepad text-indigo-500 text-4xl sm:text-5xl"></i>
                    </div>
                    <h3 class="text-xl sm:text-2xl font-bold text-indigo-700 mb-4">Chọn server để xem danh sách nhân vật</h3>
                    <p class="text-gray-600 max-w-md mx-auto">Vui lòng chọn một server từ danh sách bên trái để xem thông tin chi tiết về nhân vật trong game.</p>
                    <div class="mt-8">
                        <div class="inline-flex items-center text-indigo-600 bg-indigo-100 px-4 py-2 rounded-lg">
                            <i class="fas fa-arrow-left mr-2 animate-pulse"></i>
                            Chọn server từ danh sách
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Modal cấm chat -->
<div id="muteModal" class="fixed z-50 inset-0 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center">
        <div class="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm transition-opacity" aria-hidden="true"></div>
        <div class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-indigo-100 animate-fadeIn">
            <form id="muteForm" method="post" onsubmit="return executeMute(this);">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="action" value="mute">
                <input type="hidden" name="role_id" id="muteRoleId">

                <div class="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4">
                    <h3 class="text-xl font-bold text-indigo-800 flex items-center" id="modal-title">
                        <i class="fas fa-comment-slash text-indigo-600 mr-3"></i>
                        Cấm chat người chơi <span id="mutePlayerName" class="font-bold text-indigo-700 ml-1"></span>
                    </h3>
                </div>

                <div class="bg-white px-6 py-5">
                    <div class="mb-5">
                        <label class="block text-base font-medium text-gray-700 mb-2 flex items-center">
                            <i class="fas fa-clock text-indigo-500 mr-2"></i>
                            Thời gian cấm chat
                        </label>
                        <select name="duration" class="block w-full pl-4 pr-10 py-3 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-lg shadow-sm transition-all duration-200">
                            <option value="0" class="text-green-600 font-medium">Hủy cấm chat</option>
                            <option value="3600" class="text-gray-700">1 giờ</option>
                            <option value="7200" class="text-gray-700">2 giờ</option>
                            <option value="21600" class="text-gray-700">6 giờ</option>
                            <option value="43200" class="text-gray-700">12 giờ</option>
                            <option value="86400" class="text-gray-700">1 ngày</option>
                            <option value="259200" class="text-gray-700">3 ngày</option>
                            <option value="604800" class="text-gray-700">7 ngày</option>
                            <option value="2592000" class="text-gray-700">30 ngày</option>
                            <option value="31536000" class="text-gray-700">1 năm</option>
                        </select>
                    </div>
                    <div class="mt-4 duration-warning bg-red-50 p-4 rounded-lg border border-red-200">
                        <p class="text-sm text-red-600 flex items-start">
                            <i class="fas fa-exclamation-circle mr-2 mt-0.5 text-red-500"></i>
                            <span>Lưu ý: Lệnh cấm chat không thể thu hồi sau khi thực hiện! Vui lòng xác nhận kỹ trước khi thực hiện.</span>
                        </p>
                    </div>
                </div>

                <div class="bg-gray-50 px-6 py-4 flex flex-col-reverse sm:flex-row-reverse sm:justify-between gap-3">
                    <button type="submit" class="w-full sm:w-auto inline-flex justify-center items-center rounded-lg border border-transparent px-5 py-3 bg-gradient-to-r from-red-600 to-rose-600 text-base font-medium text-white hover:from-red-700 hover:to-rose-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-md transition-all duration-200">
                        <i class="fas fa-check-circle mr-2"></i>
                        Thực hiện
                    </button>
                    <button type="button" onclick="closeMuteModal()" class="w-full sm:w-auto inline-flex justify-center items-center rounded-lg border border-gray-300 px-5 py-3 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-sm transition-all duration-200">
                        <i class="fas fa-times-circle mr-2"></i>
                        Hủy
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
@keyframes fadeIn {
    from { opacity: 0; transform: scale(0.95); }
    to { opacity: 1; transform: scale(1); }
}
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
}
</style>

<script>
function toggleGroup(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.fa-chevron-right');

    if (content.classList.contains('hidden')) {
        // Mở nhóm
        content.classList.remove('hidden');
        icon.style.transform = 'rotate(90deg)';

        // Thêm hiệu ứng
        content.style.opacity = '0';
        content.style.maxHeight = '0';

        setTimeout(() => {
            content.style.transition = 'opacity 0.3s ease, max-height 0.5s ease';
            content.style.opacity = '1';
            content.style.maxHeight = '1000px';
        }, 10);
    } else {
        // Đóng nhóm với hiệu ứng
        content.style.transition = 'opacity 0.3s ease, max-height 0.5s ease';
        content.style.opacity = '0';
        content.style.maxHeight = '0';

        setTimeout(() => {
            content.classList.add('hidden');
            icon.style.transform = 'rotate(0deg)';
        }, 300);
    }
}

function openMuteModal(roleId, playerName) {
    // Cập nhật dữ liệu
    document.getElementById('muteRoleId').value = roleId;
    document.getElementById('mutePlayerName').textContent = playerName;

    // Hiển thị modal với hiệu ứng
    const modal = document.getElementById('muteModal');
    modal.classList.remove('hidden');

    // Thêm sự kiện thay đổi cho select
    const durationSelect = document.querySelector('select[name="duration"]');
    const warningText = document.querySelector('.duration-warning');

    // Reset select về giá trị mặc định
    durationSelect.selectedIndex = 0;

    // Cập nhật hiển thị cảnh báo
    updateMuteWarning(durationSelect.value, playerName);

    // Thêm sự kiện thay đổi
    durationSelect.addEventListener('change', function() {
        updateMuteWarning(this.value, playerName);
    });

    // Focus vào select
    setTimeout(() => {
        durationSelect.focus();
    }, 300);
}

function updateMuteWarning(value, playerName) {
    const warningText = document.querySelector('.duration-warning');
    const modalTitle = document.getElementById('modal-title');

    if (value === '0') {
        warningText.classList.add('hidden');
        modalTitle.innerHTML = '<i class="fas fa-comment-slash text-indigo-600 mr-3"></i> Hủy cấm chat người chơi <span id="mutePlayerName" class="font-bold text-indigo-700 ml-1">' + playerName + '</span>';
    } else {
        warningText.classList.remove('hidden');
        modalTitle.innerHTML = '<i class="fas fa-comment-slash text-indigo-600 mr-3"></i> Cấm chat người chơi <span id="mutePlayerName" class="font-bold text-indigo-700 ml-1">' + playerName + '</span>';
    }
}

function closeMuteModal() {
    // Đóng modal với hiệu ứng
    const modalContent = document.querySelector('#muteModal .inline-block');
    modalContent.style.opacity = '0';
    modalContent.style.transform = 'scale(0.95)';

    setTimeout(() => {
        document.getElementById('muteModal').classList.add('hidden');
        modalContent.style.opacity = '';
        modalContent.style.transform = '';
    }, 200);
}

function showToast(message, isSuccess = true) {
    // Tạo toast notification
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 ${isSuccess ? 'bg-gradient-to-r from-green-500 to-emerald-600' : 'bg-gradient-to-r from-red-500 to-rose-600'} text-white px-5 py-3 rounded-lg shadow-lg z-50 flex items-center`;

    // Thêm icon
    const icon = document.createElement('i');
    icon.className = `fas ${isSuccess ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2`;
    toast.appendChild(icon);

    // Thêm nội dung
    const text = document.createElement('span');
    text.textContent = message;
    toast.appendChild(text);

    // Thêm vào body
    document.body.appendChild(toast);

    // Hiệu ứng hiển thị
    toast.style.opacity = '0';
    toast.style.transform = 'translateY(-20px)';

    setTimeout(() => {
        toast.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 10);

    // Tự động ẩn sau 3 giây
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';

        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}

function executeMute(form) {
    const formData = new FormData(form);
    const serverId = new URLSearchParams(window.location.search).get('server_id');
    formData.append('server_id', serverId);

    // Hiển thị loading
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Đang xử lý...';

    fetch('?route=character&action=mute', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        closeMuteModal();

        // Hiển thị thông báo
        showToast(data.message || (data.status ? 'Lệnh đã được thực hiện thành công' : 'Có lỗi xảy ra'), data.status);

        // Khôi phục nút
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    })
    .catch(error => {
        console.error('Error:', error);
        closeMuteModal();

        // Hiển thị thông báo lỗi
        showToast('Có lỗi xảy ra khi thực hiện lệnh', false);

        // Khôi phục nút
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });

    return false;
}

// Xử lý form kick
function executeKick(form) {
    const formData = new FormData(form);
    const serverId = new URLSearchParams(window.location.search).get('server_id');
    formData.append('server_id', serverId);

    // Hiển thị xác nhận trước khi thực hiện
    if (!confirm('Bạn có chắc chắn muốn đá người chơi này khỏi game không?')) {
        return false;
    }

    // Hiển thị loading
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalHTML = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

    fetch('?route=character&action=kick', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Hiển thị thông báo
        showToast(data.message || (data.status ? 'Lệnh đã được thực hiện thành công' : 'Có lỗi xảy ra'), data.status);

        // Khôi phục nút
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalHTML;
    })
    .catch(error => {
        console.error('Error:', error);

        // Hiển thị thông báo lỗi
        showToast('Có lỗi xảy ra khi thực hiện lệnh', false);

        // Khôi phục nút
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalHTML;
    });

    return false;
}

// Thêm event listener cho các form và hiệu ứng
document.addEventListener('DOMContentLoaded', function() {
    // Xử lý form kick
    const kickForms = document.querySelectorAll('form[action="kick"]');
    kickForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            executeKick(this);
        });
    });

    // Thêm hiệu ứng hover cho các card thống kê
    const statCards = document.querySelectorAll('.rounded-xl.shadow-md');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.classList.add('shadow-lg');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('shadow-lg');
        });
    });

    // Tự động mở nhóm server đang được chọn
    const selectedServer = document.querySelector('.bg-gradient-to-r.from-blue-600.to-indigo-600');
    if (selectedServer) {
        const serverGroup = selectedServer.closest('.server-group');
        if (serverGroup) {
            const groupHeader = serverGroup.querySelector('[onclick="toggleGroup(this)"]');
            if (groupHeader) {
                const content = groupHeader.nextElementSibling;
                const icon = groupHeader.querySelector('.fa-chevron-right');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.style.transform = 'rotate(90deg)';
                }
            }
        }
    }

    // Server filter functionality
    const serverGroups = document.querySelectorAll('.server-group');
    const serverItems = document.querySelectorAll('.server-item');
    const showAllBtn = document.getElementById('showAllServersBtn');
    const showNewBtn = document.getElementById('showNewServersBtn');
    const showOldBtn = document.getElementById('showOldServersBtn');
    const searchInput = document.getElementById('serverSearchInput');

    if (showAllBtn && showNewBtn && showOldBtn && searchInput) {
        // Show all servers
        showAllBtn.addEventListener('click', function() {
            resetServerVisibility();
            updateActiveButton(showAllBtn);
        });

        // Show only new servers
        showNewBtn.addEventListener('click', function() {
            resetServerVisibility();

            serverGroups.forEach(group => {
                const hasNew = group.getAttribute('data-has-new') === 'true';
                const items = group.querySelectorAll('.server-item');
                let visibleCount = 0;

                items.forEach(item => {
                    if (!item.classList.contains('new-server')) {
                        item.style.display = 'none';
                    } else {
                        visibleCount++;
                    }
                });

                // If no visible servers in group, hide the group
                if (visibleCount === 0) {
                    group.style.display = 'none';
                }
            });

            updateActiveButton(showNewBtn);
        });

        // Show only old servers
        showOldBtn.addEventListener('click', function() {
            resetServerVisibility();

            serverGroups.forEach(group => {
                const items = group.querySelectorAll('.server-item');
                let visibleCount = 0;

                items.forEach(item => {
                    if (!item.classList.contains('old-server')) {
                        item.style.display = 'none';
                    } else {
                        visibleCount++;
                    }
                });

                // If no visible servers in group, hide the group
                if (visibleCount === 0) {
                    group.style.display = 'none';
                }
            });

            updateActiveButton(showOldBtn);
        });

        // Search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            if (searchTerm === '') {
                // If search is cleared, reset to current filter
                const activeFilter = document.querySelector('.active-filter');
                if (activeFilter) {
                    activeFilter.click();
                } else {
                    resetServerVisibility();
                }
                return;
            }

            resetServerVisibility();

            serverGroups.forEach(group => {
                const items = group.querySelectorAll('.server-item');
                let visibleCount = 0;

                items.forEach(item => {
                    const serverName = item.getAttribute('data-server-name');
                    const serverId = item.getAttribute('data-server-id');

                    if (!serverName.includes(searchTerm) && !serverId.toLowerCase().includes(searchTerm)) {
                        item.style.display = 'none';
                    } else {
                        visibleCount++;
                        // Expand group if it contains matching servers
                        const serverItems = item.closest('.server-items');
                        if (serverItems.classList.contains('hidden')) {
                            serverItems.classList.remove('hidden');
                            const indicator = group.querySelector('.group-indicator');
                            if (indicator) {
                                indicator.style.transform = 'rotate(90deg)';
                            }
                        }
                    }
                });

                // If no visible servers in group, hide the group
                if (visibleCount === 0) {
                    group.style.display = 'none';
                }
            });

            // Reset active button when searching
            document.querySelectorAll('.active-filter').forEach(btn => {
                btn.classList.remove('active-filter');
            });
        });

        // Helper function to update active button
        function updateActiveButton(activeBtn) {
            document.querySelectorAll('.active-filter').forEach(btn => {
                btn.classList.remove('active-filter');
            });
            activeBtn.classList.add('active-filter');
            searchInput.value = ''; // Clear search when filter button is clicked
        }

        // Helper function to reset server visibility
        function resetServerVisibility() {
            serverGroups.forEach(group => {
                group.style.display = '';
            });

            serverItems.forEach(item => {
                item.style.display = '';
            });
        }
    }
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>