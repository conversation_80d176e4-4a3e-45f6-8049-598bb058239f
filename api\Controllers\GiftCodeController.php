<?php

namespace Api\Controllers;

use App\Core\Controller;
use Api\Core\ApiHandler;

class GiftCodeController extends Controller {
    use ApiHandler;
    
    private $validator;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        // Lazy loading - chỉ khởi tạo khi cần
    }

    public function claim() {
        return $this->apiEndpoint('claim_giftcode', function($validator) {
            $spid = $validator->input('spid');
            $userId = $validator->input('user_id');
            $serverId = $validator->input('server_id');
            $roleId = $validator->input('role_id');
            $roleLevel = $validator->input('role_level');
            $vip = $validator->input('vip');
            $code = $validator->input('code');
            $time = $validator->input('time');
            $sign = $validator->input('sign');
            // Kiểm tra SIGN
            $userSign = $this->generateSign($userId);
            $expectedSign = md5($spid . $serverId . $userId . $roleId . $roleLevel . $vip . $code . $time . $userSign);
            if ($sign !== $expectedSign) {
                throw new \Exception('Invalid sign');
            }

            // Kiểm tra thời gian
            $timeDiff = time() - $time;
            if ($timeDiff > 300 || $timeDiff < -300) { // 5 phút
                throw new \Exception('Request hết hạn');
            }

            // Kiểm tra role_id có thuộc về user_id không
            $gameUser = $this->db->fetch(
                "SELECT last_login_servers FROM game_users WHERE user_id = ?",
                [$userId]
            );

            if (!$gameUser) {
                throw new \Exception('Người dùng không tồn tại');
            }

            $lastLoginServers = json_decode($gameUser['last_login_servers'], true);
            $roleFound = false;
            foreach ($lastLoginServers as $serverId => $serverData) {
                if ($serverData['role_id'] == $roleId) {
                    $roleFound = true;
                    break;
                }
            }

            if (!$roleFound) {
                throw new \Exception('Nhân vật không thuộc về tài khoản này');
            }

            // Bắt đầu transaction
            $this->db->beginTransaction();

            try {
                // Bước 1: Tìm code chung trong gift_code_groups
                $code = $this->db->fetch(
                    "SELECT 
                        id as group_id,
                        type,
                        items,
                        max_use,
                        expired_at,
                        is_disabled,
                        code_value
                     FROM gift_code_groups 
                     WHERE code_value = ? AND type = 'common'",
                    [$code]
                );

                // Bước 2: Nếu không tìm thấy code chung, tìm code đơn lẻ trong gift_codes
                if (!$code) {
                    $code = $this->db->fetch(
                        "SELECT 
                            gc.id,
                            gc.group_id,
                            gcg.type,
                            gcg.items,
                            gcg.max_use,
                            gcg.expired_at,
                            gcg.is_disabled,
                            gcg.code_value,
                            gc.code,
                            gc.is_used
                         FROM gift_codes gc 
                         JOIN gift_code_groups gcg ON gc.group_id = gcg.id 
                         WHERE gc.code = ? AND gc.is_used = 0",
                        [$code]
                    );
                }

                if (!$code) {
                    throw new \Exception('Code không tồn tại hoặc đã được sử dụng');
                }

                // Kiểm tra nhóm code
                if ($code['is_disabled']) {
                    throw new \Exception('Nhóm code đã bị vô hiệu hóa');
                }

                if ($code['expired_at'] && strtotime($code['expired_at']) < time()) {
                    throw new \Exception('Code đã hết hạn');
                }

                // Kiểm tra code chung
                if ($code['type'] === 'common') {
                    $usedCount = $this->db->fetchValue(
                        "SELECT COUNT(*) FROM gift_code_claims WHERE group_id = ?",
                        [$code['group_id']]
                    );

                    if ($usedCount >= $code['max_use']) {
                        throw new \Exception('Code đã hết lượt sử dụng');
                    }
                }

                // Kiểm tra đã sử dụng chưa (cho cả code chung và code riêng)
                $alreadyUsed = $this->db->fetch(
                    "SELECT * FROM gift_code_claims 
                     WHERE group_id = ? AND user_id = ? AND server_id = ?",
                    [$code['group_id'], $userId, $serverId]
                );

                if ($alreadyUsed) {
                    throw new \Exception('Bạn đã sử dụng nhóm code này rồi');
                }

                // Cập nhật trạng thái code
                if ($code['type'] === 'single') {
                    $this->db->update(
                        'gift_codes',
                        ['is_used' => 1, 'used_by' => $userId, 'used_at' => date('Y-m-d H:i:s')],
                        'id = ?',
                        [$code['id']]
                    );
                }

                // Thêm vào bảng claims
                $this->db->insert('gift_code_claims', [
                    'group_id' => $code['group_id'],
                    'code_id' => $code['type'] === 'single' ? $code['id'] : null,
                    'code' => $code,
                    'user_id' => $userId,
                    'role_id' => $roleId,
                    'server_id' => $serverId
                ]);

                $this->db->commit();

                $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
                $check = $this->checkGameDbConnections($connectResults, false);
                if (!$check) {
                    throw new \Exception('Không thể kết nối tới server game');
                }

                // Tạo command để thêm item
                $items = $code['items'];
                
                $pcommand = [
                    'creator' => 'GiftCodeController',
                    'createtime' => $this->THIS_DATETIME,
                    'type' => 2,
                    'cmd' => "CmdAddItem {$roleId} Code {$items}",
                    'confirmtime' => 0,
                    'querytime' => $this->THIS_DATETIME
                ];

                $insertCommand = $this->gameSqlBuilder->buildInsert('command', $pcommand);
                $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertCommand['sql'], $insertCommand['params']);
                if (!$result['success']) {
                    throw new \Exception($result['error']);
                }
                $this->gameDatabaseManager->closeAll();

                $this->successResponse([
                    'code' => $code,
                    'items' => $items
                ], 'Nhận giftcode thành công');

            } catch (\Exception $e) {
                $this->db->rollback();
                throw $e;
            }
        });
    }
} 