<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Core\GameDatabaseManager;

class GamenoticeController extends Controller {
    private $serverConfig;

    private $gameNoticeConfig;
    
    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->serverConfig = require __DIR__ . '/../../config/server.php';
        $this->gameNoticeConfig = require __DIR__ . '/../../config/game_notice_pagination.php';
    }
    
    public function index() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        $this->setViewConfig([
            'container' => 'max-w-full',
            'padding' => 'py-4 px-4'
        ]);

        $servers = $this->db->getServers(true);
        $selectedServerId = $_GET['server_id'] ?? null;

        // Nhóm server theo khu giống CharacterController
        $groupedServers = [];
        foreach ($this->serverConfig['server_groups'] as $group) {
            $groupServers = array_filter($servers, function($server) use ($group) {
                return $server['id'] >= $group['start_id'] && $server['id'] <= $group['end_id'];
            });
            usort($groupServers, function($a, $b) {
                return $a['id'] - $b['id'];
            });
            $serversPerGroup = $group['servers_per_group'];
            $subGroups = array_chunk($groupServers, $serversPerGroup);
            foreach ($subGroups as $subGroup) {
                $startId = $subGroup[0]['id'];
                $endId = end($subGroup)['id'];
                $groupedServers[] = [
                    'start_id' => $startId,
                    'end_id' => $endId,
                    'servers' => $subGroup
                ];
            }
        }

        // Phân trang
        $page = max(1, intval($_GET['page'] ?? 1));
        $perPage = $this->gameNoticeConfig['per_page'] ?? 20;
        $totalPages = 1;
        $notices = [];
        if ($selectedServerId) {
            $allNotices = $this->getNotices($selectedServerId);
            $totalNotices = count($allNotices);
            $totalPages = max(1, ceil($totalNotices / $perPage));
            $offset = ($page - 1) * $perPage;
            $notices = array_slice($allNotices, $offset, $perPage);
        }

        require_once __DIR__ . '/../../resources/views/game_notice/index.php';
    }

    public function create() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.create')) {
            $this->redirectToFirstAccessiblePage();
        }

        $servers = $this->db->getServers(true);
        $colorOptions = $this->gameNoticeConfig['color_options'] ?? [
            0 => ['label' => 'Mặc định', 'value' => '#000000'],
        ];
        require_once __DIR__ . '/../../resources/views/game_notice/create.php';
    }

    public function store() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.create')) {
            return $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền thêm thông báo game']);
        }

        $serverIds = $_POST['server_ids'] ?? [];
        $type = $_POST['notice_type'] ?? 1;
        $content = $_POST['content'] ?? '';
        $link = $_POST['link'] ?? '';
        $color = $_POST['color'] ?? '#000000';
        $beginTime = $_POST['begin_send_time'] ?? '';
        $endTime = $_POST['end_send_time'] ?? '';
        $interval = $_POST['send_interval'] ?? 300;
        $disableOldNotices = isset($_POST['disable_old_notices']) ? true : false;

        $beginTimestamp = strtotime(str_replace('T', ' ', $beginTime));
        $endTimestamp = strtotime(str_replace('T', ' ', $endTime));

        if ($beginTimestamp === false || $beginTimestamp <= 0) {
            return $this->jsonResponse(['status' => false, 'message' => 'Thời gian bắt đầu không hợp lệ']);
        }
        if ($endTimestamp === false || $endTimestamp <= 0) {
            return $this->jsonResponse(['status' => false, 'message' => 'Thời gian kết thúc không hợp lệ']);
        }
        if ($endTimestamp <= $beginTimestamp) {
            return $this->jsonResponse(['status' => false, 'message' => 'Thời gian kết thúc phải sau thời gian bắt đầu']);
        }

        if (empty($serverIds)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng chọn ít nhất một máy chủ']);
        }

        if (empty($content)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập nội dung thông báo']);
        }

        if ($type == 2 && empty($link)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập link']);
        }

        try {
            // Kết nối tới game database
            $connectResults = $this->gameDatabaseManager->connectServers($serverIds);
            $this->checkGameDbConnections($connectResults);
            
            $results = [];
            foreach ($serverIds as $serverId) {
                // Tắt tất cả notice cũ của server này nếu được chọn
                if ($disableOldNotices) {
                    $disableSql = $this->gameSqlBuilder->buildUpdate('publicnotice', 
                        ['is_forbid' => 1], 
                        'is_forbid = 0'
                    );
                    $disableResult = $this->gameDatabaseManager->executeOnServer($serverId, $disableSql['sql'], $disableSql['params']);
                    
                    if (!$disableResult['success']) {
                        $results[] = [
                            'server_id' => $serverId,
                            'status' => false,
                            'message' => 'Không thể tắt thông báo cũ',
                            'error' => $disableResult['error'] ?? null
                        ];
                        continue;
                    }
                }

                // Xử lý link như code cũ
                if ($type == 2 && !empty($link)) {
                    $str = str_replace('http://', '', $link);
                    if (!empty($str)) {
                        $linkHtml = "<a href='{$link}' target='_blank'><font color='#FF0000'>【Chi tiết】</font></a>";
                        $content = stripslashes($content . $linkHtml);
                    } else {
                        $content = stripslashes($content);
                    }
                } else {
                    $content = stripslashes($content);
                }

                // Chuyển đổi mã màu hex sang decimal
                $colorDecimal = hexdec(ltrim($color, '#'));

                $mailData = [
                    'type' => $type,
                    'creator' => "GameNoticeController",
                    'createtime' => time(),
                    'content' => $content,
                    'color' => $colorDecimal,
                    'begin_send_time' => $beginTimestamp,
                    'end_send_time' => $endTimestamp,
                    'send_interval' => $interval,
                    'is_forbid' => 0,
                    'is_urgent' => 0
                ];
                
                $insertSql = $this->gameSqlBuilder->buildInsert('publicnotice', $mailData);
                $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);

                $results[] = [
                    'server_id' => $serverId,
                    'status' => $result['success'],
                    'message' => $result['success'] ? 'Thêm thông báo thành công' : 'Thêm thông báo thất bại',
                    'error' => $result['error'] ?? null
                ];
            }

            // Log action
            $this->db->log('game_notice_create', 'success', [
                'server_ids' => $serverIds,
                'type' => $type,
                'content' => $content,
                'results' => $results
            ]);

            return $this->jsonResponse([
                'status' => true,
                'message' => 'Thêm thông báo thành công',
                'data' => ['results' => $results]
            ]);
        } catch (\Exception $e) {
            $this->db->log('game_notice_create', 'error', [
                'error' => $e->getMessage(),
                'server_ids' => $serverIds,
                'type' => $type,
                'content' => $content
            ]);
            return $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            $this->gameDatabaseManager->closeAll();
        }
    }

    public function edit() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.edit')) {
            header('Location: /public/?route=gamenotice');
            exit;
        }

        $serverId = $_GET['server_id'] ?? 0;
        $id = $_GET['id'] ?? 0;

        if (!$serverId || !$id) {
            header('Location: /public/?route=gamenotice');
            exit;
        }

        $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
        if (!$this->checkGameDbConnections($connectResults)) {
            header('Location: /public/?route=gamenotice');
            exit;
        }

        $sql = $this->gameSqlBuilder->buildSelect('publicnotice', 'idpublicnotice = ?', [$id]);
        $result = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);

        if (!$result['success'] || empty($result['data'])) {
            header('Location: /public/?route=gamenotice');
            exit;
        }

        $notice = $result['data'][0];
        $servers = $this->db->getServers();
        $colorOptions = $this->gameNoticeConfig['color_options'] ?? [
            0 => ['label' => 'Mặc định', 'value' => '#000000'],
        ];
        require_once __DIR__ . '/../../resources/views/game_notice/edit.php';
    }

    public function update() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.edit')) {
            return $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền sửa thông báo game']);
        }

        $id = $_POST['id'] ?? 0;
        $serverIds = $_POST['server_ids'] ?? [];
        $type = $_POST['notice_type'] ?? 1;
        $content = $_POST['content'] ?? '';
        $link = $_POST['link'] ?? '';
        $color = $_POST['color'] ?? '#000000';
        $beginTime = $_POST['begin_send_time'] ?? '';
        $endTime = $_POST['end_send_time'] ?? '';
        $interval = $_POST['send_interval'] ?? 300;
        $disableOldNotices = isset($_POST['disable_old_notices']) ? true : false;

        $beginTimestamp = strtotime(str_replace('T', ' ', $beginTime));
        $endTimestamp = strtotime(str_replace('T', ' ', $endTime));
        if ($beginTimestamp === false || $beginTimestamp <= 0) {
            return $this->jsonResponse(['status' => false, 'message' => 'Thời gian bắt đầu không hợp lệ']);
        }
        if ($endTimestamp === false || $endTimestamp <= 0) {
            return $this->jsonResponse(['status' => false, 'message' => 'Thời gian kết thúc không hợp lệ']);
        }
        if ($endTimestamp <= $beginTimestamp) {
            return $this->jsonResponse(['status' => false, 'message' => 'Thời gian kết thúc phải sau thời gian bắt đầu']);
        }

        if (empty($serverIds)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng chọn ít nhất một máy chủ']);
        }

        if (empty($content)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập nội dung thông báo']);
        }

        // Xử lý link như code cũ
        if ($type == 2 && !empty($link)) {
            $str = str_replace('http://', '', $link);
            if (!empty($str)) {
                $linkHtml = "<a href='{$link}' target='_blank'><font color='#FF0000'>【Chi tiết】</font></a>";
                $content = stripslashes($content . $linkHtml);
            } else {
                $content = stripslashes($content);
            }
        } else {
            $content = stripslashes($content);
        }

        try {
            $connectResults = $this->gameDatabaseManager->connectServers($serverIds);
            $this->checkGameDbConnections($connectResults);
            
            $colorDecimal = hexdec(ltrim($color, '#'));
            
            $results = [];
            foreach ($serverIds as $serverId) {
                // Tắt tất cả notice cũ của server này nếu được chọn
                if ($disableOldNotices) {
                    $disableSql = $this->gameSqlBuilder->buildUpdate('publicnotice', 
                        ['is_forbid' => 1], 
                        'is_forbid = 0'
                    );
                    $disableResult = $this->gameDatabaseManager->executeOnServer($serverId, $disableSql['sql'], $disableSql['params']);
                    
                    if (!$disableResult['success']) {
                        $results[] = [
                            'server_id' => $serverId,
                            'status' => false,
                            'message' => 'Không thể tắt thông báo cũ',
                            'error' => $disableResult['error'] ?? null
                        ];
                        continue;
                    }
                }

                $updateData = [
                    'type' => $type,
                    'creator' => "GameNoticeController",
                    'createtime' => time(),
                    'content' => $content,
                    'color' => $colorDecimal,
                    'begin_send_time' => $beginTimestamp,
                    'end_send_time' => $endTimestamp,
                    'send_interval' => $interval
                ];
                
                $updateSql = $this->gameSqlBuilder->buildUpdate('publicnotice', $updateData, 'idpublicnotice = ?', [$id]);
                $result = $this->gameDatabaseManager->executeOnServer($serverId, $updateSql['sql'], $updateSql['params']);

                $results[] = [
                    'server_id' => $serverId,
                    'status' => $result['success'],
                    'message' => $result['success'] ? 'Cập nhật thông báo thành công' : 'Cập nhật thông báo thất bại',
                    'error' => $result['error'] ?? null
                ];
            }

            // Log action
            $this->db->log('game_notice_update', 'success', [
                'notice_id' => $id,
                'server_ids' => $serverIds,
                'type' => $type,
                'content' => $content,
                'results' => $results
            ]);

            return $this->jsonResponse([
                'status' => true,
                'message' => 'Cập nhật thông báo thành công',
                'data' => ['results' => $results]
            ]);
        } catch (\Exception $e) {
            $this->db->log('game_notice_update', 'error', [
                'error' => $e->getMessage(),
                'notice_id' => $id,
                'server_ids' => $serverIds
            ]);
            return $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            $this->gameDatabaseManager->closeAll();
        }
    }

    public function delete() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.delete')) {
            $_SESSION['error'] = 'Bạn không có quyền xóa thông báo game';
            return $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền xóa thông báo game']);
        }

        $serverId = $_POST['server_id'] ?? 0;
        $id = $_POST['id'] ?? 0;

        if (!$serverId || !$id) {
            $_SESSION['error'] = 'Thiếu thông tin server hoặc thông báo';
            return $this->jsonResponse(['status' => false, 'message' => 'Thiếu thông tin server hoặc thông báo']);
        }

        try {
            $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
            if (!$this->checkGameDbConnections($connectResults)) {
                throw new \Exception("Không thể kết nối đến server $serverId");
            }

            $sql = $this->gameSqlBuilder->buildDelete('publicnotice', 'idpublicnotice = ?', [$id]);
            $result = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);

            if ($result['success']) {
                // Log action
                $this->db->log('game_notice_delete', 'success', [
                    'notice_id' => $id,
                    'server_id' => $serverId
                ]);

                $_SESSION['success'] = 'Xóa thông báo thành công';
                return $this->jsonResponse([
                    'status' => true,
                    'message' => 'Xóa thông báo thành công'
                ]);
            }

            throw new \Exception("Không thể xóa thông báo");
        } catch (\Exception $e) {
            $this->db->log('game_notice_delete', 'error', [
                'error' => $e->getMessage(),
                'notice_id' => $id,
                'server_id' => $serverId
            ]);
            $_SESSION['error'] = 'Có lỗi xảy ra: ' . $e->getMessage();
            return $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            $this->gameDatabaseManager->closeAll();
        }
    }

    public function toggleStatus() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.edit')) {
            $_SESSION['error'] = 'Bạn không có quyền thay đổi trạng thái thông báo';
            return $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền thay đổi trạng thái thông báo']);
        }

        $serverId = $_POST['server_id'] ?? 0;
        $id = $_POST['id'] ?? 0;
        $type = $_POST['field'] ?? '';
        $value = $_POST['value'] ?? 0;

        if (!$serverId || !$id || !in_array($type, ['is_forbid', 'is_urgent'])) {
            $_SESSION['error'] = 'Thiếu thông tin hoặc thông tin không hợp lệ';
            return $this->jsonResponse(['status' => false, 'message' => 'Thiếu thông tin hoặc thông tin không hợp lệ']);
        }

        try {
            $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
            if (!$this->checkGameDbConnections($connectResults)) {
                throw new \Exception("Không thể kết nối đến server $serverId");
            }

            $updateData = [$type => $value];
            $updateSql = $this->gameSqlBuilder->buildUpdate('publicnotice', $updateData, 'idpublicnotice = ?', [$id]);
            $result = $this->gameDatabaseManager->executeOnServer($serverId, $updateSql['sql'], $updateSql['params']);

            if ($result['success']) {
                // Log action
                $this->db->log('game_notice_toggle_status', 'success', [
                    'notice_id' => $id,
                    'server_id' => $serverId,
                    'type' => $type,
                    'value' => $value
                ]);

                $_SESSION['success'] = 'Thay đổi trạng thái thành công';
                return $this->jsonResponse([
                    'status' => true,
                    'message' => 'Thay đổi trạng thái thành công'
                ]);
            }

            throw new \Exception("Không thể thay đổi trạng thái thông báo");
        } catch (\Exception $e) {
            $this->db->log('game_notice_toggle_status', 'error', [
                'error' => $e->getMessage(),
                'notice_id' => $id,
                'server_id' => $serverId
            ]);
            $_SESSION['error'] = 'Có lỗi xảy ra: ' . $e->getMessage();
            return $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            $this->gameDatabaseManager->closeAll();
        }
    }

    private function getNotices($serverId) {
        try {
            $connectResults = $this->gameDatabaseManager->connectServers([$serverId]);
            if (!$this->checkGameDbConnections($connectResults)) {
                throw new \Exception("Không thể kết nối đến server $serverId");
            }

            $sql = $this->gameSqlBuilder->buildSelect('publicnotice', '', [], ['*'], 'idpublicnotice DESC');
            $result = $this->gameDatabaseManager->executeOnServer($serverId, $sql['sql'], $sql['params']);
            
            if ($result['success']) {
                return $result['data'];
            }
            
            return [];
        } catch (\Exception $e) {
            $this->db->log('game_notice_get', 'error', [
                'error' => $e->getMessage(),
                'server_id' => $serverId
            ]);
            return [];
        }
    }

    public function directNotice() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.direct_notice')) {
            $this->redirectToFirstAccessiblePage();
        }

        $servers = $this->db->getServers(true);
        require_once __DIR__ . '/../../resources/views/game_notice/direct_notice.php';
    }

    public function sendDirectNotice() {
        $this->requireLogin();
        
        if (!$this->hasPermission('gamenotice.direct_notice')) {
            return $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền gửi thông báo trực tiếp']);
        }

        $serverIds = $_POST['server_ids'] ?? [];
        $content = $_POST['content'] ?? '';
        $disconnect = $_POST['disconnect'] ?? 'false';

        if (empty($serverIds)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng chọn máy chủ']);
        }

        try {
            $connectResults = $this->gameDatabaseManager->connectServers($serverIds);
            if (!$this->checkGameDbConnections($connectResults)) {
                throw new \Exception("Không thể kết nối đến các máy chủ đã chọn");
            }

            // Encode content to base64
            $encodedContent = !empty($content) ? base64_encode($content) : 'none';

            $command = [
                'creator' => 'GameNoticeController',
                'createtime' => time(),
                'type' => 2,
                'cmd' => "maintenance {$disconnect} {$encodedContent}"
            ];

            $results = [];
            foreach ($serverIds as $serverId) {
                $insertSql = $this->gameSqlBuilder->buildInsert('command', $command);
                $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);

                $results[] = [
                    'server_id' => $serverId,
                    'status' => $result['success'],
                    'message' => $result['success'] ? 'Gửi thông báo thành công' : 'Gửi thông báo thất bại',
                    'error' => $result['error'] ?? null
                ];
            }

            // Log action
            $this->db->log('game_notice_direct', 'success', [
                'server_ids' => $serverIds,
                'content' => $content,
                'disconnect' => $disconnect
            ]);

            return $this->jsonResponse([
                'status' => true,
                'message' => 'Gửi thông báo thành công',
                'results' => $results
            ]);

        } catch (\Exception $e) {
            $this->db->log('game_notice_direct', 'error', [
                'error' => $e->getMessage(),
                'server_ids' => $serverIds,
                'content' => $content
            ]);
            return $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            $this->gameDatabaseManager->closeAll();
        }
    }
}