<?php

namespace App\Controllers;

use App\Core\Controller;
use Exception;

class NoticeController extends Controller
{
    private $noticeFile;
    private $policyFile;
    private $termsFile;
    private $isWritable;

    public function __construct($db, $config)
    {
        parent::__construct($db, $config);
        $this->noticeFile = __DIR__ . '/../../data/notice.json';
        $this->policyFile = __DIR__ . '/../../data/chinhsach.txt';
        $this->termsFile = __DIR__ . '/../../data/dieukhoan.txt';

        // Kiểm tra quyền ghi file
        $this->isWritable = is_writable(dirname($this->noticeFile)) && 
                           is_writable(dirname($this->policyFile)) && 
                           is_writable(dirname($this->termsFile));

        if (!$this->isWritable) {
            $_SESSION['error'] = '<PERSON><PERSON> thống không có quyền ghi file. Vui lòng kiểm tra quyền thư mục data/';
        }
    }

    private function checkFilePermission()
    {
        if (!$this->isWritable) {
            throw new Exception('<PERSON><PERSON> thống không có quyền ghi file. Vui lòng kiểm tra quyền thư mục data/');
        }
    }

    public function index()
    {
        $this->requireLogin();
        if (!$this->hasPermission('notice.view')) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này.';
            $this->redirectToFirstAccessiblePage();
        }

        $notices = [];
        if (file_exists($this->noticeFile)) {
            $notices = json_decode(file_get_contents($this->noticeFile), true)['data'] ?? [];
        }

        require __DIR__ . '/../../resources/views/notice/index.php';
    }

    public function create()
    {
        $this->requireLogin();
        if (!$this->hasPermission('notice.create')) {
            $_SESSION['error'] = 'Bạn không có quyền tạo thông báo.';
            $this->redirectToFirstAccessiblePage();
        }

        try {
            $this->checkFilePermission();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: ?route=notice');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $title = trim($_POST['title'] ?? '');
            $content = trim($_POST['content'] ?? '');
            $type = trim($_POST['type'] ?? 'game');

            if (empty($title) || empty($content)) {
                $_SESSION['error'] = 'Vui lòng nhập đầy đủ thông tin!';
                header('Location: ?route=notice&action=create');
                exit;
            }

            $notices = [];
            if (file_exists($this->noticeFile)) {
                $notices = json_decode(file_get_contents($this->noticeFile), true)['data'] ?? [];
            }

            $newNotice = [
                'title' => $title,
                'content' => $content,
                'type' => $type,
                'add_time' => date('Y-m-d H:i:s')
            ];

            array_unshift($notices, $newNotice);

            $data = [
                'ret' => 0,
                'data' => $notices
            ];

            if (file_put_contents($this->noticeFile, json_encode($data, JSON_PRETTY_PRINT))) {
                // Lưu log
                $this->db->log('notice.create', 'success', [
                    'title' => $title,
                    'type' => $type
                ]);

                $_SESSION['success'] = 'Thêm thông báo thành công!';
                header('Location: ?route=notice');
                exit;
            } else {
                $_SESSION['error'] = 'Có lỗi xảy ra khi thêm thông báo!';
            }
        }

        require __DIR__ . '/../../resources/views/notice/create.php';
    }

    public function edit()
    {
        $this->requireLogin();
        if (!$this->hasPermission('notice.edit')) {
            $_SESSION['error'] = 'Bạn không có quyền sửa thông báo.';
            $this->redirectToFirstAccessiblePage();
        }

        try {
            $this->checkFilePermission();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: ?route=notice');
            exit;
        }

        $index = (int)($_GET['index'] ?? -1);
        if ($index < 0) {
            $_SESSION['error'] = 'Thông báo không tồn tại!';
            header('Location: ?route=notice');
            exit;
        }

        $notices = [];
        if (file_exists($this->noticeFile)) {
            $notices = json_decode(file_get_contents($this->noticeFile), true)['data'] ?? [];
        }

        if (!isset($notices[$index])) {
            $_SESSION['error'] = 'Thông báo không tồn tại!';
            header('Location: ?route=notice');
            exit;
        }

        $notice = $notices[$index];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $title = trim($_POST['title'] ?? '');
            $content = trim($_POST['content'] ?? '');
            $type = trim($_POST['type'] ?? 'game');

            if (empty($title) || empty($content)) {
                $_SESSION['error'] = 'Vui lòng nhập đầy đủ thông tin!';
                header("Location: ?route=notice&action=edit&index=$index");
                exit;
            }

            $notices[$index] = [
                'title' => $title,
                'content' => $content,
                'type' => $type,
                'add_time' => $notice['add_time']
            ];

            $data = [
                'ret' => 0,
                'data' => $notices
            ];

            if (file_put_contents($this->noticeFile, json_encode($data, JSON_PRETTY_PRINT))) {
                // Lưu log
                $this->db->log('notice.edit', 'success', [
                    'index' => $index,
                    'old_title' => $notice['title'],
                    'new_title' => $title,
                    'type' => $type
                ]);

                $_SESSION['success'] = 'Sửa thông báo thành công!';
                header('Location: ?route=notice');
                exit;
            } else {
                $_SESSION['error'] = 'Có lỗi xảy ra khi sửa thông báo!';
            }
        }

        require __DIR__ . '/../../resources/views/notice/edit.php';
    }

    public function delete()
    {
        $this->requireLogin();
        if (!$this->hasPermission('notice.delete')) {
            $_SESSION['error'] = 'Bạn không có quyền xóa thông báo.';
            $this->redirectToFirstAccessiblePage();
        }

        try {
            $this->checkFilePermission();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: ?route=notice');
            exit;
        }

        $index = (int)($_GET['index'] ?? -1);
        if ($index < 0) {
            $_SESSION['error'] = 'Thông báo không tồn tại!';
            header('Location: ?route=notice');
            exit;
        }

        $notices = [];
        if (file_exists($this->noticeFile)) {
            $notices = json_decode(file_get_contents($this->noticeFile), true)['data'] ?? [];
        }


        if (!isset($notices[$index])) {
            $_SESSION['error'] = 'Thông báo không tồn tại!';
            header('Location: ?route=notice');
            exit;
        }

        $notice = $notices[$index];
        
        $title = $notice['title'];
        if ($title == 'Thông báo game' || $title == 'Thông báo cập nhật') {
            $_SESSION['error'] = 'Không thể xóa thông báo game hoặc cập nhật!';
            header('Location: ?route=notice');
            exit;
        }
        unset($notices[$index]);
        $notices = array_values($notices);

        $data = [
            'ret' => 0,
            'data' => $notices
        ];

        if (file_put_contents($this->noticeFile, json_encode($data, JSON_PRETTY_PRINT))) {
            // Lưu log
            $this->db->log('notice.delete', 'success', [
                'index' => $index,
                'title' => $notice['title'],
                'type' => $notice['type']
            ]);

            $_SESSION['success'] = 'Xóa thông báo thành công!';
        } else {
            $_SESSION['error'] = 'Có lỗi xảy ra khi xóa thông báo!';
        }

        header('Location: ?route=notice');
        exit;
    }

    public function policy()
    {
        $this->requireLogin();
        if (!$this->hasPermission('notice.policy')) {
            $_SESSION['error'] = 'Bạn không có quyền chỉnh sửa chính sách.';
            $this->redirectToFirstAccessiblePage();
        }

        try {
            $this->checkFilePermission();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: ?route=notice');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $content = trim($_POST['content'] ?? '');

            if (file_put_contents($this->policyFile, $content)) {
                // Lưu log
                $this->db->log('notice.policy', 'success', [
                    'content_length' => strlen($content)
                ]);

                $_SESSION['success'] = 'Cập nhật chính sách thành công!';
                header('Location: ?route=notice&action=policy');
                exit;
            } else {
                $_SESSION['error'] = 'Có lỗi xảy ra khi cập nhật chính sách!';
            }
        }

        $content = '';
        if (file_exists($this->policyFile)) {
            $content = file_get_contents($this->policyFile);
        }

        require __DIR__ . '/../../resources/views/notice/policy.php';
    }

    public function terms()
    {
        $this->requireLogin();
        if (!$this->hasPermission('notice.terms')) {
            $_SESSION['error'] = 'Bạn không có quyền chỉnh sửa điều khoản.';
            $this->redirectToFirstAccessiblePage();
        }

        try {
            $this->checkFilePermission();
        } catch (Exception $e) {
            $_SESSION['error'] = $e->getMessage();
            header('Location: ?route=notice');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $content = trim($_POST['content'] ?? '');

            if (file_put_contents($this->termsFile, $content)) {
                // Lưu log
                $this->db->log('notice.terms', 'success', [
                    'content_length' => strlen($content)
                ]);

                $_SESSION['success'] = 'Cập nhật điều khoản thành công!';
                header('Location: ?route=notice&action=terms');
                exit;
            } else {
                $_SESSION['error'] = 'Có lỗi xảy ra khi cập nhật điều khoản!';
            }
        }

        $content = '';
        if (file_exists($this->termsFile)) {
            $content = file_get_contents($this->termsFile);
        }

        require __DIR__ . '/../../resources/views/notice/terms.php';
    }
} 