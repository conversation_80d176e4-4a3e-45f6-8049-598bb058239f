<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X<PERSON>c thực Email - <?= htmlspecialchars($app_name) ?></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
        }
        .content {
            margin-bottom: 30px;
        }
        .verification-code {
            background-color: #f8f9fa;
            border: 2px dashed #007bff;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 20px 0;
        }
        .verification-code .code {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            letter-spacing: 3px;
            font-family: 'Courier New', monospace;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .footer {
            border-top: 1px solid #eee;
            padding-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin: 10px 0;
        }
        .button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <h1><?= htmlspecialchars($app_name) ?></h1>
            <p>Xác thực địa chỉ email của bạn</p>
        </div>
        
        <div class="content">
            <h2>Xin chào <?= htmlspecialchars($username) ?>!</h2>
            
            <p>Cảm ơn bạn đã đăng ký tài khoản tại <strong><?= htmlspecialchars($app_name) ?></strong>. Để hoàn tất quá trình đăng ký, vui lòng xác thực địa chỉ email của bạn bằng mã xác thực bên dưới:</p>
            
            <div class="verification-code">
                <p><strong>Mã xác thực của bạn:</strong></p>
                <div class="code"><?= htmlspecialchars($verification_code) ?></div>
            </div>
            
            <p>Vui lòng nhập mã này vào ứng dụng để xác thực email của bạn.</p>
            
            <div class="warning">
                <strong>⚠️ Lưu ý quan trọng:</strong>
                <ul>
                    <li>Mã xác thực này có hiệu lực trong <strong>5 phút</strong></li>
                    <li>Không chia sẻ mã này với bất kỳ ai</li>
                    <li>Nếu bạn không yêu cầu xác thực này, vui lòng bỏ qua email</li>
                </ul>
            </div>
            
            <p>Nếu bạn gặp bất kỳ vấn đề gì, vui lòng liên hệ với chúng tôi để được hỗ trợ.</p>
        </div>
        
        <div class="footer">
            <p>Email này được gửi tự động từ hệ thống <strong><?= htmlspecialchars($app_name) ?></strong></p>
            <p>Vui lòng không trả lời email này.</p>
            <p><a href="<?= htmlspecialchars($app_url) ?>"><?= htmlspecialchars($app_url) ?></a></p>
        </div>
    </div>
</body>
</html>
