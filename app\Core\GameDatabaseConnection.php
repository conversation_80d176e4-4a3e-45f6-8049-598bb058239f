<?php
namespace App\Core;
use Exception;

class GameDatabaseConnection {
    private $pdo = null;

    public function __construct($serverId, $mainDb) {
        // Lấy thông tin server từ database quản lý (mainDb)
        $server = $mainDb->getServerById($serverId);
        if (!$server || empty($server['mysql_ip']) || empty($server['mysql_db'])) {
            throw new Exception("Không tìm thấy thông tin kết nối database game cho serverId: $serverId");
        }
        $host = $server['mysql_ip'];
        $dbname = $server['mysql_db'];
        $user = getenv('GAME_DB_USER');
        $pass = getenv('GAME_DB_PASS');
        $charset = 'utf8mb4';

        $dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
        $this->pdo = new \PDO($dsn, $user, $pass, [
            \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION,
            \PDO::ATTR_DEFAULT_FETCH_MODE => \PDO::FETCH_ASSOC
        ]);
    }

    public function query($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    }

    public function fetch($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }

    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }

    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');
        $sql = "INSERT INTO $table (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $this->query($sql, array_values($data));
        return $this->pdo->lastInsertId();
    }

    public function close() {
        $this->pdo = null;
    }
}
