<?php
require_once __DIR__ . '/../vendor/autoload.php';
$config = require __DIR__ . '/../config/api.php';

// Load .env configuration
$envPath = dirname(__DIR__);
$dotenv = Dotenv\Dotenv::createUnsafeImmutable($envPath);
$dotenv->load();



$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://gmtool_nginx/api/Cron/collectOnlineRoles');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    var_dump("Lỗi khi thu thập số role online: " . $response);
    exit(1);
}

$result = json_decode($response, true);
echo json_encode($result);