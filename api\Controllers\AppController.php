<?php

namespace Api\Controllers;

use App\Core\Controller;
use App\Services\AppSettingsService;
use Api\Core\ApiHandler;

class AppController extends Controller {
    use ApiHandler;
    
    private $orderConfig;
    private $validator;
    private $appSettingsService;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->orderConfig = require __DIR__ . '/../../config/order.php';
        $this->appSettingsService = AppSettingsService::getInstance($this->db);
        // Lazy loading - chỉ khởi tạo khi cần
    }

    /**
     * Get app information including login settings and package list
     */
    public function info() {
        return $this->apiEndpoint('app_info', function($validated) {

            // Extract app_id and app_secret
            $appId = $validated->input('app_id');
            $appSecret = $validated->input('app_secret');

            // Verify app_id and app_secret using service with cache
            $appSettings = $this->appSettingsService->validateApp($appId, $appSecret);

            if (!$appSettings) {
                throw new \Exception('Thông tin xác thực không hợp lệ hoặc ứng dụng đã bị vô hiệu hóa');
            }

            // Prepare response data
            $responseData = [
                'login_settings' => [
                    'google_login' => (bool)$appSettings['google_login'],
                    'facebook_login' => (bool)$appSettings['facebook_login'],
                    'apple_login' => (bool)$appSettings['apple_login'],
                    'guest_login' => (bool)$appSettings['guest_login']
                ],
                'app_settings' => [
                    'is_debug' => (bool)$appSettings['is_debug'],
                    'phone_sync' => (bool)($appSettings['phone_sync'] ?? 0)
                ],
                'packages' => []
            ];

            // Add packages from order.php
            foreach ($this->orderConfig['packages'] as $key => $package) {
                if ($package['enabled']) {
                    $responseData['packages'][] = [
                        'id' => $key,
                        'name' => $package['name'],
                        'amount' => $package['amount']
                    ];
                }
            }

            $this->successResponse($responseData, 'Thành công');
        });
    }
}
