<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- Cache Management Section -->
<?php if ($this->hasPermission('appsettings.edit')): ?>
<div class="bg-blue-50 border border-blue-200 rounded-lg mb-4">
    <div class="px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-database text-blue-600 mr-2"></i>
                <h4 class="text-sm font-medium text-blue-800">Quản lý Cache</h4>
            </div>
            <div class="flex space-x-2">
                <button id="refreshCacheStats" 
                        class="inline-flex items-center px-3 py-1 border border-blue-300 text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50">
                    <i class="fas fa-sync-alt mr-1"></i>Refresh Stats
                </button>
                <button id="clearCache" 
                        class="inline-flex items-center px-3 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50">
                    <i class="fas fa-trash mr-1"></i>Xóa Cache
                </button>
            </div>
        </div>
        
        <!-- Cache Stats Display -->
        <div id="cacheStats" class="mt-3 grid grid-cols-2 md:grid-cols-4 gap-3">
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Redis Status</div>
                <div id="redisStatus" class="text-sm font-medium">
                    <span class="text-gray-400">Đang tải...</span>
                </div>
            </div>
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Cached Keys</div>
                <div id="cachedKeys" class="text-sm font-medium">
                    <span class="text-gray-400">-</span>
                </div>
            </div>
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Cache TTL</div>
                <div id="cacheExpire" class="text-sm font-medium">
                    <span class="text-gray-400">-</span>
                </div>
            </div>
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Last Updated</div>
                <div id="lastUpdated" class="text-sm font-medium">
                    <span class="text-gray-400">-</span>
                </div>
            </div>
        </div>
        
        <!-- Cache Details (collapsible) -->
        <div id="cacheDetails" class="mt-3 hidden">
            <div class="bg-white border rounded-lg">
                <div class="px-3 py-2 border-b bg-gray-50 text-sm font-medium text-gray-700">
                    Chi tiết Cache Keys
                </div>
                <div id="cacheDetailsList" class="p-3 text-sm">
                    <!-- Cache details will be populated here -->
                </div>
            </div>
        </div>
        
        <!-- Toggle cache details button -->
        <div class="mt-2 text-right">
            <button id="toggleCacheDetails" 
                    class="text-xs text-blue-600 hover:text-blue-800 hidden">
                <i id="toggleIcon" class="fas fa-chevron-down mr-1"></i>
                <span id="toggleText">Hiển thị chi tiết</span>
            </button>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Danh sách Cài đặt App
        </h3>
        <?php if ($this->hasPermission('appsettings.edit')): ?>
        <a href="/public/?route=appsettings&action=create"
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700">
            <i class="fas fa-plus mr-2"></i>Thêm Cài đặt App
        </a>
        <?php endif; ?>
    </div>
    <div class="border-t border-gray-200">
        <div class="p-4">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">App ID</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">App Secret</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Google Login</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Facebook Login</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Apple Login</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Guest Login</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Kích hoạt</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Debug</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Đồng bộ Phone</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($appSettings)): ?>
                            <tr>
                                <td colspan="8" class="px-4 sm:px-6 py-4 text-center text-sm text-gray-500">
                                    Không có dữ liệu
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($appSettings as $app): ?>
                                <tr>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $app['id']; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo $app['app_id']; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo substr($app['app_secret'], 0, 10) . '...'; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['google_login']): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['facebook_login']): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['apple_login']): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['guest_login']): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['is_active'] ?? 1): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['is_debug'] ?? 0): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php if ($app['phone_sync'] ?? 0): ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                                Bật
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                Tắt
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <?php if ($this->hasPermission('appsettings.edit')): ?>
                                            <a href="/public/?route=appsettings&action=edit&id=<?php echo $app['id']; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                                                <i class="fas fa-edit"></i> Sửa
                                            </a>
                                            <a href="/public/?route=appsettings&action=delete&id=<?php echo $app['id']; ?>"
                                               class="text-red-600 hover:text-red-900"
                                               onclick="return confirm('Bạn có chắc chắn muốn xóa cài đặt này?');">
                                                <i class="fas fa-trash"></i> Xóa
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Cache Management JavaScript -->
<?php if ($this->hasPermission('appsettings.edit')): ?>
<script>
// Cache management functions
document.addEventListener('DOMContentLoaded', function() {
    
    // Load cache stats on page load
    loadCacheStats();
    
    // Refresh cache stats button
    document.getElementById('refreshCacheStats').addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Đang tải...';
        
        loadCacheStats().finally(() => {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-sync-alt mr-1"></i>Refresh Stats';
        });
    });
    
    // Clear cache button
    document.getElementById('clearCache').addEventListener('click', function() {
        if (confirm('Bạn có chắc chắn muốn xóa tất cả cache?')) {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Đang xóa...';
            
            clearCache().finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-trash mr-1"></i>Xóa Cache';
            });
        }
    });
    
    // Toggle cache details
    document.getElementById('toggleCacheDetails').addEventListener('click', function() {
        const details = document.getElementById('cacheDetails');
        const icon = document.getElementById('toggleIcon');
        const text = document.getElementById('toggleText');
        
        if (details.classList.contains('hidden')) {
            details.classList.remove('hidden');
            icon.className = 'fas fa-chevron-up mr-1';
            text.textContent = 'Ẩn chi tiết';
        } else {
            details.classList.add('hidden');
            icon.className = 'fas fa-chevron-down mr-1';
            text.textContent = 'Hiển thị chi tiết';
        }
    });
});

// Load cache statistics
function loadCacheStats() {
    return fetch('/public/?route=appsettings&action=cache&cache_action=stats')
        .then(response => response.json())
        .then(data => {
            updateCacheStats(data);
        })
        .catch(error => {
            console.error('Error loading cache stats:', error);
            showError('Lỗi khi tải thống kê cache');
        });
}

// Clear all cache
function clearCache() {
    return fetch('/public/?route=appsettings&action=cache&cache_action=clear')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(data.message || 'Cache đã được xóa thành công');
                // Reload stats after clearing
                setTimeout(loadCacheStats, 500);
            } else {
                showError(data.error || 'Lỗi khi xóa cache');
            }
        })
        .catch(error => {
            console.error('Error clearing cache:', error);
            showError('Lỗi khi xóa cache');
        });
}

// Update cache stats display
function updateCacheStats(data) {
    const redisStatus = document.getElementById('redisStatus');
    const cachedKeys = document.getElementById('cachedKeys');
    const cacheExpire = document.getElementById('cacheExpire');
    const lastUpdated = document.getElementById('lastUpdated');
    const toggleButton = document.getElementById('toggleCacheDetails');
    const cacheDetailsList = document.getElementById('cacheDetailsList');
    
    if (data.redis_available) {
        redisStatus.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Kết nối</span>';
        cachedKeys.innerHTML = '<span class="text-blue-600">' + (data.cached_keys || 0) + '</span>';
        cacheExpire.innerHTML = '<span class="text-gray-600">' + formatTTL(data.cache_expire || 3600) + '</span>';
        
        // Display real cache timestamp
        if (data.last_updated) {
            const cacheTime = new Date(data.last_updated * 1000);
            const now = new Date();
            const diffMinutes = Math.floor((now - cacheTime) / 60000);
            
            let timeDisplay;
            if (diffMinutes < 1) {
                timeDisplay = 'Vừa xong';
            } else if (diffMinutes < 60) {
                timeDisplay = diffMinutes + ' phút trước';
            } else if (diffMinutes < 1440) {
                timeDisplay = Math.floor(diffMinutes / 60) + ' giờ trước';
            } else {
                timeDisplay = cacheTime.toLocaleString('vi-VN');
            }
            
            lastUpdated.innerHTML = '<span class="text-gray-600" title="' + cacheTime.toLocaleString('vi-VN') + '">' + timeDisplay + '</span>';
        } else {
            lastUpdated.innerHTML = '<span class="text-gray-400">Chưa có cache</span>';
        }
        
        // Show/hide toggle button and populate cache details
        if (data.cached_keys > 0 && data.cache_details) {
            toggleButton.classList.remove('hidden');
            
            let detailsHTML = '';
            for (const [keyName, details] of Object.entries(data.cache_details)) {
                const cachedAt = new Date(details.cached_at * 1000);
                const expiresAt = details.expires_at ? new Date(details.expires_at * 1000) : null;
                const now = new Date();
                const diffMinutes = Math.floor((now - cachedAt) / 60000);
                
                let ageDisplay;
                if (diffMinutes < 1) {
                    ageDisplay = 'Vừa xong';
                } else if (diffMinutes < 60) {
                    ageDisplay = diffMinutes + ' phút trước';
                } else {
                    ageDisplay = Math.floor(diffMinutes / 60) + ' giờ trước';
                }
                
                let expiryDisplay = 'N/A';
                if (expiresAt) {
                    const ttlMinutes = Math.floor((expiresAt - now) / 60000);
                    if (ttlMinutes > 0) {
                        if (ttlMinutes < 60) {
                            expiryDisplay = ttlMinutes + ' phút';
                        } else {
                            expiryDisplay = Math.floor(ttlMinutes / 60) + ' giờ';
                        }
                    } else {
                        expiryDisplay = '<span class="text-red-500">Đã hết hạn</span>';
                    }
                }
                
                detailsHTML += `
                    <div class="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <div>
                            <div class="font-medium text-gray-900">
                                ${keyName === 'all' ? 'Tất cả apps' : 'App ID: ' + keyName}
                            </div>
                            <div class="text-xs text-gray-500">
                                Cached: ${ageDisplay} (${cachedAt.toLocaleString('vi-VN')})
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">
                                TTL: ${expiryDisplay}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            cacheDetailsList.innerHTML = detailsHTML || '<div class="text-gray-500">Không có chi tiết cache</div>';
        } else {
            toggleButton.classList.add('hidden');
            cacheDetailsList.innerHTML = '<div class="text-gray-500">Không có cache nào</div>';
        }
    } else {
        redisStatus.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>Ngắt kết nối</span>';
        cachedKeys.innerHTML = '<span class="text-gray-400">N/A</span>';
        cacheExpire.innerHTML = '<span class="text-gray-400">N/A</span>';
        lastUpdated.innerHTML = '<span class="text-gray-400">N/A</span>';
        toggleButton.classList.add('hidden');
        cacheDetailsList.innerHTML = '<div class="text-gray-500">Redis không khả dụng</div>';
    }
}

// Format TTL seconds to readable format
function formatTTL(seconds) {
    if (seconds >= 3600) {
        return Math.floor(seconds / 3600) + 'h';
    } else if (seconds >= 60) {
        return Math.floor(seconds / 60) + 'm';
    } else {
        return seconds + 's';
    }
}

// Show success message
function showSuccess(message) {
    const toast = createToast(message, 'success');
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}

// Show error message
function showError(message) {
    const toast = createToast(message, 'error');
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 5000);
}

// Create toast notification
function createToast(message, type) {
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'fa-check' : 'fa-exclamation-triangle';
    
    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center space-x-2`;
    toast.innerHTML = `
        <i class="fas ${icon}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    return toast;
}
</script>
<?php endif; ?>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
