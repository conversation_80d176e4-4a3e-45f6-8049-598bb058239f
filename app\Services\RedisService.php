<?php
namespace App\Services;

class RedisService 
{
    private static $instance = null;
    private $redis;
    private $connected = false;
    
    private function __construct() {
        // Private constructor để enforce singleton
        $this->connect();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Kết nối Redis với fallback graceful
     */
    private function connect(): void 
    {
        try {
            $this->redis = new \Redis();
            $this->redis->connect(
                getenv('REDIS_HOST') ?: '127.0.0.1',
                getenv('REDIS_PORT') ?: 6379,
                getenv('REDIS_TIMEOUT') ?: 2
            );
            
            if ($password = getenv('REDIS_PASSWORD')) {
                $this->redis->auth($password);
            }

            $pingResult = $this->redis->ping();
            if ($pingResult !== '+PONG' && $pingResult !== 'PONG' && $pingResult !== true) {
                throw new \Exception('Redis ping failed');
            }
            
            $this->connected = true;
        } catch (\Exception $e) {
            error_log("Redis connection failed: " . $e->getMessage());
            $this->connected = false;
        }
    }
    
    /**
     * Kiểm tra Redis có khả dụng không
     */
    public function isAvailable(): bool 
    {
        return $this->connected;
    }
    
    /**
     * Rate Limiting với sliding window
     */
    public function checkRateLimit(string $key, int $limit, int $window = 60): array 
    {
        if (!$this->connected) {
            return ['allowed' => true, 'remaining' => $limit]; // Fallback cho phép request
        }
        
        try {
            $current = time();
            $pipeline = $this->redis->multi(\Redis::PIPELINE);
            
            // Xóa các entries cũ hơn window
            $pipeline->zRemRangeByScore($key, 0, $current - $window);
            
            // Đếm số requests hiện tại
            $pipeline->zCard($key);
            
            $results = $pipeline->exec();
            $currentCount = $results[1] ?? 0;
            
            // Kiểm tra nếu đã vượt quá limit
            if ($currentCount >= $limit) {
                // Lấy thời gian của request đầu tiên để tính reset time
                $firstRequest = $this->redis->zRange($key, 0, 0, true);
                $resetTime = empty($firstRequest) ? $current : (int)reset($firstRequest) + $window;
                
                return [
                    'allowed' => false,
                    'remaining' => 0,
                    'reset_time' => $resetTime
                ];
            }
            
            // Chỉ thêm request mới nếu chưa vượt quá limit
            $this->redis->multi()
                ->zAdd($key, $current, $current . '_' . uniqid())
                ->expire($key, $window)
                ->exec();
            
            return [
                'allowed' => true,
                'remaining' => $limit - $currentCount - 1,
                'reset_time' => $current + $window
            ];
            
        } catch (\Exception $e) {
            error_log("Redis rate limit error: " . $e->getMessage());
            return ['allowed' => true, 'remaining' => $limit]; // Fallback
        }
    }
    
    /**
     * Get stats for monitoring
     */
    public function getStats(): array 
    {
        if (!$this->connected) {
            return ['connected' => false];
        }
        
        try {
            $info = $this->redis->info();
            return [
                'connected' => true,
                'memory_used' => $info['used_memory_human'] ?? 'N/A',
                'total_connections' => $info['total_connections_received'] ?? 'N/A',
                'keys' => $this->redis->dbSize()
            ];
        } catch (\Exception $e) {
            return ['connected' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get value by key
     */
    public function get(string $key) 
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->get($key);
        } catch (\Exception $e) {
            error_log("Redis get error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set value with expiration
     */
    public function setex(string $key, int $expire, string $value): bool 
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->setex($key, $expire, $value);
        } catch (\Exception $e) {
            error_log("Redis setex error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete keys
     */
    public function del($keys): int 
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            if (is_array($keys)) {
                return $this->redis->del($keys);
            } else {
                return $this->redis->del([$keys]);
            }
        } catch (\Exception $e) {
            error_log("Redis del error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get keys by pattern
     */
    public function keys(string $pattern): array 
    {
        if (!$this->connected) {
            return [];
        }
        
        try {
            $result = $this->redis->keys($pattern);
            return $result ?: [];
        } catch (\Exception $e) {
            error_log("Redis keys error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get hash field
     */
    public function hGet(string $key, string $field)
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->hGet($key, $field);
        } catch (\Exception $e) {
            error_log("Redis hGet error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get all hash fields
     */
    public function hGetAll(string $key): array
    {
        if (!$this->connected) {
            return [];
        }
        
        try {
            $result = $this->redis->hGetAll($key);
            return $result ?: [];
        } catch (\Exception $e) {
            error_log("Redis hGetAll error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Set hash field
     */
    public function hSet(string $key, string $field, string $value): bool
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->hSet($key, $field, $value);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Set multiple hash fields
     */
    public function hMSet(string $key, array $fields): bool
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->hMSet($key, $fields);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Delete hash fields
     */
    public function hDel(string $key, string $field): bool
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            return $this->redis->hDel($key, $field) > 0;
        } catch (\Exception $e) {
            error_log("Redis hDel error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Set hash with expiration
     */
    public function hSetex(string $key, array $data, int $ttl): bool
    {
        if (!$this->connected) {
            return false;
        }
        
        try {
            $pipe = $this->redis->multi(\Redis::PIPELINE);
            $pipe->hMSet($key, $data);
            $pipe->expire($key, $ttl);
            $pipe->exec();
            return true;
        } catch (\Exception $e) {
            error_log("Redis hSetex error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get hash length
     */
    public function hLen(string $key): int
    {
        if (!$this->connected) {
            return 0;
        }
        
        try {
            return $this->redis->hLen($key);
        } catch (\Exception $e) {
            error_log("Redis hLen error: " . $e->getMessage());
            return 0;
        }
    }
} 