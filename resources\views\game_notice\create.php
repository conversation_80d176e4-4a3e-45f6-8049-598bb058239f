<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        <!-- Flash Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-3 sm:mb-4 p-3 sm:p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                <?php 
                echo $_SESSION['success'];
                unset($_SESSION['success']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-3 sm:mb-4 p-3 sm:p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                <?php 
                echo $_SESSION['error'];
                unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>

        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <!-- Header -->
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                    <h2 class="text-xl sm:text-2xl font-bold text-gray-900">
                        Thêm thông báo game
                    </h2>
                    <a href="?route=gamenotice" class="text-blue-600 hover:text-blue-800 text-sm sm:text-base">
                        <i class="fas fa-arrow-left mr-1 sm:mr-2"></i>Quay lại
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="p-3 sm:p-6">
                <form id="noticeForm" class="space-y-4 sm:space-y-6">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                    <!-- Server Selection -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300">
                        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2">
                            <label class="block text-sm sm:text-base font-medium text-gray-700">Máy chủ <span class="text-red-600">*</span></label>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="select-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-blue-600 hover:text-blue-800 bg-blue-50 rounded-md border border-blue-200">Chọn tất cả</button>
                                <button type="button" id="deselect-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-red-600 hover:text-red-800 bg-red-50 rounded-md border border-red-200">Bỏ chọn tất cả</button>
                            </div>
                        </div>
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-3 sm:p-4 mb-3 sm:mb-4 rounded-md shadow-sm">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-yellow-400 text-lg sm:text-xl mt-1"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <div class="flex items-center">
                                        <input type="checkbox" name="disable_old_notices" id="disable_old_notices" checked 
                                               class="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors">
                                        <label for="disable_old_notices" class="ml-2 block text-sm sm:text-base font-medium text-gray-700">
                                            Tắt toàn bộ thông báo cũ của máy chủ được chọn
                                        </label>
                                    </div>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p class="font-semibold">Lưu ý:</p>
                                        <p class="mt-1">Khi option được chọn, tất cả thông báo cũ sẽ được tắt đi.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="relative mb-3 sm:mb-4">
                            <input type="text" id="server-search" placeholder="Tìm kiếm máy chủ..." 
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border-2 border-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute right-3 top-2.5">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="mt-3 sm:mt-4 max-h-96 overflow-y-auto border-2 border-gray-300 rounded-md">
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 p-2 sm:p-3">
                                <?php 
                                $dbServers = [];
                                foreach ($servers as $server) {
                                    if (!empty($server['mysql_db'])) {
                                        $dbServers[$server['mysql_db']][] = $server;
                                    }
                                }
                                foreach ($servers as $server): ?>
                                    <?php if (!$server['merged_into']): ?>
                                        <div class="server-item flex flex-col p-3 hover:bg-gray-100 rounded-md border border-gray-200">
                                            <div class="flex items-center">
                                                <input type="checkbox" name="server_ids[]" value="<?php echo $server['id']; ?>" 
                                                       class="h-4 w-4 sm:h-5 sm:w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400 rounded">
                                                <div class="ml-3">
                                                    <label class="block text-sm sm:text-base font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($server['name']); ?>
                                                    </label>
                                                    <span class="text-xs sm:text-sm text-gray-500">ID: <?php echo $server['id']; ?></span>
                                                </div>
                                            </div>
                                            <?php if (!empty($server['mysql_db']) && count($dbServers[$server['mysql_db']]) > 1): ?>
                                                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                                                    <div class="flex items-start">
                                                        <svg class="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                        </svg>
                                                        <div class="text-xs sm:text-sm text-yellow-700">
                                                            <p class="font-medium">Cảnh báo: Database trùng lặp</p>
                                                            <p class="mt-1">Database này cũng được sử dụng bởi:</p>
                                                            <ul class="list-disc list-inside mt-1">
                                                                <?php foreach ($dbServers[$server['mysql_db']] as $otherServer): ?>
                                                                    <?php if ($otherServer['id'] != $server['id']): ?>
                                                                        <li>Server <?php echo $otherServer['id']; ?>: <?php echo htmlspecialchars($otherServer['name']); ?></li>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Notice Type -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                            Loại thông báo <span class="text-red-600">*</span>
                        </label>
                        <select name="notice_type" class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="1">Thông thường</option>
                            <option value="2">Có link</option>
                        </select>
                    </div>

                    <!-- Content -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                            Nội dung <span class="text-red-600">*</span>
                        </label>
                        <textarea name="content" rows="4" required
                                  class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Nhập nội dung thông báo"></textarea>
                    </div>

                    <!-- Link (for type 2) -->
                    <div id="linkField" class="bg-gray-50 p-3 sm:p-4 rounded-lg hidden">
                        <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                            Link
                        </label>
                        <input type="text" name="link" 
                               class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="https://...">
                    </div>

                    <!-- Color -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                            Màu chữ
                        </label>
                        <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                            <select name="color" id="colorSelect" class="w-full sm:w-48 px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <?php foreach ($colorOptions as $key => $opt): ?>
                                    <option value="<?php echo $opt['value']; ?>" data-color="<?php echo $opt['value']; ?>">
                                        <?php echo $opt['label']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <span id="colorPreview" class="inline-block w-8 h-8 sm:w-10 sm:h-10 rounded border border-gray-300" style="background: <?php echo $colorOptions[0]['value']; ?>;"></span>
                        </div>
                    </div>

                    <!-- Time Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-6">
                        <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                            <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                                Thời gian bắt đầu <span class="text-red-600">*</span>
                            </label>
                            <input type="datetime-local" name="begin_send_time" required
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                            <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                                Thời gian kết thúc <span class="text-red-600">*</span>
                            </label>
                            <input type="datetime-local" name="end_send_time" required
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                    </div>

                    <!-- Interval -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm sm:text-base font-medium text-gray-700 mb-2">
                            Khoảng thời gian gửi (giây) <span class="text-red-600">*</span>
                        </label>
                        <input type="number" name="send_interval" min="1" value="300" required
                               class="w-full px-3 sm:px-4 py-2 sm:py-3 text-sm sm:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <button type="submit" id="submitBtn" 
                                class="bg-blue-600 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base">
                            <i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('noticeForm');
    const noticeType = form.querySelector('[name="notice_type"]');
    const linkField = document.getElementById('linkField');
    const selectAllBtn = document.getElementById('select-all');
    const deselectAllBtn = document.getElementById('deselect-all');
    const serverSearch = document.getElementById('server-search');
    const serverItems = document.querySelectorAll('.server-item');
    const submitBtn = document.getElementById('submitBtn');

    // Hiển thị/ẩn trường link theo loại thông báo
    noticeType.addEventListener('change', function() {
        linkField.classList.toggle('hidden', this.value !== '2');
    });

    // Chọn tất cả máy chủ
    selectAllBtn.addEventListener('click', function() {
        serverItems.forEach(item => {
            if (item.style.display !== 'none') {
                item.querySelector('input[type="checkbox"]').checked = true;
            }
        });
    });

    // Bỏ chọn tất cả máy chủ
    deselectAllBtn.addEventListener('click', function() {
        serverItems.forEach(item => {
            if (item.style.display !== 'none') {
                item.querySelector('input[type="checkbox"]').checked = false;
            }
        });
    });

    // Tìm kiếm server
    serverSearch.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        serverItems.forEach(item => {
            const label = item.querySelector('label').textContent.toLowerCase();
            const id = item.querySelector('span').textContent.toLowerCase();
            if (label.includes(searchTerm) || id.includes(searchTerm)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    });

    // Preview màu khi chọn
    const colorSelect = document.getElementById('colorSelect');
    const colorPreview = document.getElementById('colorPreview');
    const colorMap = <?php echo json_encode(array_column($colorOptions, 'value')); ?>;
    colorSelect.addEventListener('change', function() {
        colorPreview.style.background = this.value || '#000000';
    });

    // Xử lý submit form
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        submitBtn.disabled = true;
        submitBtn.innerHTML = `<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Đang lưu...`;

        // Validate
        const serverIds = Array.from(form.querySelectorAll('[name="server_ids[]"]:checked')).map(cb => cb.value);
        if (serverIds.length === 0) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Vui lòng chọn ít nhất một máy chủ',
                width: '90vw',
                maxWidth: '400px'
            });
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo';
            return;
        }
        const content = form.querySelector('[name="content"]').value.trim();
        if (!content) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Vui lòng nhập nội dung thông báo',
                width: '90vw',
                maxWidth: '400px'
            });
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo';
            return;
        }
        const beginTime = form.querySelector('[name="begin_send_time"]').value;
        const endTime = form.querySelector('[name="end_send_time"]').value;
        if (!beginTime || !endTime) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Vui lòng chọn thời gian bắt đầu và kết thúc',
                width: '90vw',
                maxWidth: '400px'
            });
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo';
            return;
        }
        if (new Date(beginTime) >= new Date(endTime)) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Thời gian kết thúc phải sau thời gian bắt đầu',
                width: '90vw',
                maxWidth: '400px'
            });
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo';
            return;
        }
        const interval = parseInt(form.querySelector('[name="send_interval"]').value);
        if (isNaN(interval) || interval < 1) {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Khoảng thời gian gửi không hợp lệ',
                width: '90vw',
                maxWidth: '400px'
            });
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo';
            return;
        }

        // Submit form
        const formData = new FormData(form);
        fetch('?route=gamenotice&action=store', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                let details = '<div class="space-y-2">';
                if (data.data && data.data.results) {
                    data.data.results.forEach(result => {
                        if (result.status) {
                            details += `<p class="text-green-600 font-medium">Server ${result.server_id}: ${result.message}</p>`;
                        } else {
                            details += `<p class="text-red-600 font-medium">Server ${result.server_id}: ${result.message}</p>`;
                            if (result.error) {
                                details += `<div class="pl-4 mt-1"><p class="text-sm text-red-500">- ${result.error}</p></div>`;
                            }
                        }
                    });
                }
                details += '</div>';
                Swal.fire({
                    icon: 'success',
                    title: 'Thành công',
                    html: 'Thêm thông báo thành công!' + details,
                    width: '90vw',
                    maxWidth: '600px'
                }).then(() => {
                    window.location.href = '?route=gamenotice';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: data.message || 'Có lỗi xảy ra',
                    width: '90vw',
                    maxWidth: '400px'
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: 'Có lỗi xảy ra khi lưu thông báo',
                width: '90vw',
                maxWidth: '400px'
            });
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-1 sm:mr-2"></i>Lưu thông báo';
        });
    });
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 