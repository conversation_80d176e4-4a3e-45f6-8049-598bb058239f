<?php require __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold"><PERSON><PERSON><PERSON>n lý thông báo</h1>
        <?php if ($this->hasPermission('notice.create')): ?>
        <a href="?route=notice&action=create" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
            <i class="fas fa-plus"></i> Thêm thông báo
        </a>
        <?php endif; ?>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
    </div>
    <?php endif; ?>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <?php if ($this->hasPermission('notice.policy')): ?>
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-file-alt text-blue-500 mr-2"></i>
                    Chính sách
                </h2>
                <a href="?route=notice&action=policy" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-edit"></i> Chỉnh sửa
                </a>
            </div>
            <div class="prose max-w-none">
                <?php
                if (file_exists($this->policyFile)) {
                    $content = file_get_contents($this->policyFile);
                    $lines = explode("\n", $content);
                    $preview = array_slice($lines, 0, 5);
                    echo nl2br(htmlspecialchars(implode("\n", $preview)));
                    if (count($lines) > 5) {
                        echo '<div class="mt-2 text-right">';
                        echo '<a href="?route=notice&action=policy" class="text-blue-600 hover:text-blue-800">';
                        echo '<i class="fas fa-arrow-right"></i> Xem thêm';
                        echo '</a>';
                        echo '</div>';
                    }
                } else {
                    echo '<p class="text-gray-500">Chưa có nội dung chính sách</p>';
                }
                ?>
            </div>
        </div>
        <?php endif; ?>

        <?php if ($this->hasPermission('notice.terms')): ?>
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-xl font-bold text-gray-800">
                    <i class="fas fa-file-contract text-blue-500 mr-2"></i>
                    Điều khoản
                </h2>
                <a href="?route=notice&action=terms" class="text-blue-600 hover:text-blue-800">
                    <i class="fas fa-edit"></i> Chỉnh sửa
                </a>
            </div>
            <div class="prose max-w-none">
                <?php
                if (file_exists($this->termsFile)) {
                    $content = file_get_contents($this->termsFile);
                    $lines = explode("\n", $content);
                    $preview = array_slice($lines, 0, 5);
                    echo nl2br(htmlspecialchars(implode("\n", $preview)));
                    if (count($lines) > 5) {
                        echo '<div class="mt-2 text-right">';
                        echo '<a href="?route=notice&action=terms" class="text-blue-600 hover:text-blue-800">';
                        echo '<i class="fas fa-arrow-right"></i> Xem thêm';
                        echo '</a>';
                        echo '</div>';
                    }
                } else {
                    echo '<p class="text-gray-500">Chưa có nội dung điều khoản</p>';
                }
                ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tiêu đề</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <?php if (empty($notices)): ?>
                <tr>
                    <td colspan="4" class="px-6 py-4 text-center text-gray-500">Không có thông báo nào</td>
                </tr>
                <?php else: ?>
                <?php foreach ($notices as $index => $notice): ?>
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($notice['title']); ?></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                            <?php echo $notice['type'] === 'game' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'; ?>">
                            <?php echo $notice['type'] === 'game' ? 'Game' : 'Hệ thống'; ?>
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <?php echo date('d/m/Y H:i', strtotime($notice['add_time'])); ?>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <?php if ($this->hasPermission('notice.edit')): ?>
                        <a href="?route=notice&action=edit&index=<?php echo $index; ?>" class="text-indigo-600 hover:text-indigo-900 mr-3">
                            <i class="fas fa-edit"></i> Sửa
                        </a>
                        <?php endif; ?>
                        <?php if ($this->hasPermission('notice.delete')): ?>
                        <a href="?route=notice&action=delete&index=<?php echo $index; ?>" 
                           class="text-red-600 hover:text-red-900"
                           onclick="return confirm('Bạn có chắc chắn muốn xóa thông báo này?')">
                            <i class="fas fa-trash"></i> Xóa
                        </a>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<?php require __DIR__ . '/../layouts/footer.php'; ?> 