<?php

namespace Api\Controllers;

use App\Core\Controller;
use Api\Core\ApiHandler;

class OrderController extends Controller {
    use ApiHandler;
    
    private $orderConfig;
    private $xml;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->orderConfig = require __DIR__ . '/../../config/order.php';
        $this->xml = simplexml_load_file(__DIR__ . '/../../data/chongzhireward_spid.xml');
        // Lazy loading - chỉ khởi tạo khi cần
    }

    private function getPackageIdByAmount($amount) {
        foreach ($this->orderConfig['packages'] as $key => $pkg) {
            if ($pkg['amount'] == $amount) {
                return $key;
            }
        }
        return null;
    }

    public function create() {
        return $this->apiEndpoint('create_order', function($validator) {
            // Validate required parameters
            $userId = $validator->input('user_id');
            $roleId = $validator->input('role_id');
            $serverId = $validator->input('server_id');
            $time = $validator->input('time');
            $source = $validator->input('source');
            $orderId = $validator->input('order_id');
            $roleLevel = $validator->input('role_level');
            $sign = $validator->input('sign');

            // Kiểm tra SIGN
            $userSign = $this->generateSign($userId);
            $expectedSign = md5(
                $userId . $roleId . $serverId . $time . $source . $orderId . $roleLevel . $userSign
            );
            if ($sign !== $expectedSign) {
                throw new \Exception('Invalid sign');
            }

            // Parse order_id
            $orderIdParts = explode('_', $orderId);
            $lastPart = end($orderIdParts); // "6-14-1"
            list($moneySeq, $seq, $mod) = explode('-', $lastPart);

            // Lấy currency_1 từ XML
            $currency1 = null;
            foreach ($this->xml->reward_0->data as $reward) {
                if ((string)$reward->seq === $seq) {
                    $moneySeqXml = (string)$reward->money_seq;
                    foreach ($this->xml->money_type->data as $moneyType) {
                        if ((string)$moneyType->seq === $moneySeqXml) {
                            $currency1 = (int)$moneyType->currency_1;
                            break 2;
                        }
                    }
                }
            }
            if ($currency1 === null) {
                throw new \Exception('Không tìm thấy đơn hàng phù hợp');
            }

            // Lấy package_id từ orderConfig
            $packageId = $this->getPackageIdByAmount($currency1);
            if ($packageId === null) {
                throw new \Exception('Không tìm thấy package phù hợp');
            }

            // Kiểm tra package có bị vô hiệu hóa không
            if (empty($this->orderConfig['packages'][$packageId]['enabled'])) {
                throw new \Exception('Package đã bị vô hiệu hóa');
            }

            // Kiểm tra thời gian tạo đơn hàng
            $lastOrder = $this->db->fetch(
                "SELECT UNIX_TIMESTAMP(created_at) as timestamp FROM payment_transactions 
                WHERE game_uid = ? 
                ORDER BY created_at DESC 
                LIMIT 1",
                [$roleId]
            );
            if ($lastOrder) {
                $lastOrderTime = (int)$lastOrder['timestamp'];
                $currentTime = time();
                $timeDiff = $currentTime - $lastOrderTime;
                $interval = $this->orderConfig['settings']['order_interval'] ?? 5;
                if ($timeDiff < $interval) {
                    throw new \Exception('Vui lòng chờ ' . ($interval - $timeDiff) . ' giây trước khi tạo đơn mới');
                }
            }

            // Lưu vào database
            $orderId = uniqid('ORDER_' . $serverId . '_', true);
            $orderData = [
                'order_id' => $orderId,
                'client_order_id' => $orderId,
                'user_id' => $userId,
                'game_uid' => $roleId, 
                'game_level' => $roleLevel,
                'package_id' => $packageId,
                'package_count' => 1,
                'server_id' => $serverId,
                'amount' => $currency1,
                'source' => $source,
                'status' => 'pending',
                'created_at' => date('Y-m-d H:i:s')
            ];
            $this->db->insert('payment_transactions', $orderData);

            $appDebug = getenv('APP_DEBUG') ?? 'false';

            if ($appDebug == 'true') {
                $connectResults = $this->gameDatabaseManager->connectServers([$serverId]); 
                $this->checkGameDbConnections($connectResults);
                
                $selectRoleNameMap = $this->gameSqlBuilder->buildSelectByArray('role_name_map', ['role_id' => $roleId]);
                $roleData = $this->gameDatabaseManager->executeOnServer($serverId,$selectRoleNameMap['sql'], $selectRoleNameMap['params']);
                if ($roleData['success']) {
                    $roleData = $roleData['data'];
                    $plat_user_name = $roleData[0]['plat_user_name'];

                    $selectAccountGold = $this->gameSqlBuilder->buildSelectByArray('accountgold', ['plat_user_name' => $plat_user_name]);
                    $accountGoldData = $this->gameDatabaseManager->executeOnServer($serverId,$selectAccountGold['sql'], $selectAccountGold['params']);
                    if ($accountGoldData['success']) {
                        $accountGoldData = $accountGoldData['data'];
                        if (empty($accountGoldData)) {
                            $insertAccountGold = [
                                'plat_user_name' => $plat_user_name,
                                'gold' => $currency1,
                                'gold_history' => $currency1,
                                'last_get_gold_time' => 0
                            ];
                            $insertSql = $this->gameSqlBuilder->buildInsert('accountgold', $insertAccountGold);
                            $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);
                        } else {
                            $updateAccountGold = [
                                'gold' => $accountGoldData[0]['gold'] + $currency1,
                                'gold_history' => $accountGoldData[0]['gold_history'] + $currency1
                            ];
                            $updateSql = $this->gameSqlBuilder->buildUpdate('accountgold', $updateAccountGold, 'plat_user_name = ?', [$plat_user_name]);
                            $this->gameDatabaseManager->executeOnServer($serverId, $updateSql['sql'], $updateSql['params']);
                        }
                    }
                }

                $pcommand = [
                    'creator' => 'OrderController',
                    'createtime' => $this->THIS_DATETIME,
                    'type' => 2,
                    'cmd' => "CmdBuyItem {$roleId} {$moneySeq} {$seq} {$mod} {$currency1} 0",
                ];

                $insertCommand = $this->gameSqlBuilder->buildInsert('command', $pcommand);

                $this->gameDatabaseManager->executeOnServer($serverId,$insertCommand['sql'], $insertCommand['params']);
                $this->gameDatabaseManager->closeAll();
                $this->db->update('payment_transactions', ['status' => 'success'], 'order_id = ?', [$orderId]);
            }

            $this->successResponse($orderData, 'Tạo đơn hàng thành công');
        });
    }
} 