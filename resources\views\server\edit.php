<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">S<PERSON>a thông tin máy chủ</h1>
            <a href="/public/?route=server" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>

        <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form method="POST" action="/public/?route=server&action=edit&id=<?php echo $server['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                
                <div class="mb-4">
                    <label for="name" class="block text-gray-700 text-sm font-bold mb-2">Tên máy chủ</label>
                    <input type="text" id="name" name="name" value="<?php echo isset($_POST['name']) ? htmlspecialchars($_POST['name']) : htmlspecialchars($server['name']); ?>" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>

                <div class="mb-4">
                    <label for="ip" class="block text-gray-700 text-sm font-bold mb-2">IP</label>
                    <input type="text" id="ip" name="ip" value="<?php echo isset($_POST['ip']) ? htmlspecialchars($_POST['ip']) : htmlspecialchars($server['ip']); ?>" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>

                <div class="mb-4">
                    <label for="port" class="block text-gray-700 text-sm font-bold mb-2">Port</label>
                    <input type="number" id="port" name="port" value="<?php echo isset($_POST['port']) ? (int)$_POST['port'] : $server['port']; ?>" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>

                <div class="mb-4">
                    <label for="opentime" class="block text-gray-700 text-sm font-bold mb-2">Thời gian mở</label>
                    <input type="datetime-local" id="opentime" name="opentime" 
                           value="<?php echo isset($_POST['opentime']) ? htmlspecialchars($_POST['opentime']) : date('Y-m-d\TH:i', strtotime($server['opentime'])); ?>" 
                           class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>

                <div class="mb-4">
                    <label for="status" class="block text-gray-700 text-sm font-bold mb-2">Trạng thái</label>
                    <select id="status" name="status" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <?php foreach ($serverStatus as $key => $label): ?>
                        <option value="<?php echo $key; ?>" <?php echo $server['status'] == $key ? 'selected' : ''; ?>><?php echo $label; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-4">
                    <label for="mysql_ip" class="block text-gray-700 text-sm font-bold mb-2">MySQL IP</label>
                    <select id="mysql_ip" name="mysql_ip" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                        <option value="">Chọn IP</option>
                        <?php foreach ($GAME_DB_IPS as $ip): ?>
                        <option value="<?php echo htmlspecialchars($ip); ?>" <?php echo (isset($_POST['mysql_ip']) ? $_POST['mysql_ip'] : ($server['mysql_ip'] ?? '')) === $ip ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($ip); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div class="mb-4">
                    <label for="mysql_db" class="block text-gray-700 text-sm font-bold mb-2">MySQL Database</label>
                    <input type="text" id="mysql_db" name="mysql_db" value="<?php echo isset($_POST['mysql_db']) ? htmlspecialchars($_POST['mysql_db']) : htmlspecialchars($server['mysql_db'] ?? ''); ?>" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                </div>

                <div class="flex items-center justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        <i class="fas fa-save mr-2"></i>Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 