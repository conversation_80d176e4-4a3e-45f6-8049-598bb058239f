<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Gộp máy chủ</h1>
            <a href="/public/?route=server" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
        </div>
        <?php endif; ?>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form method="POST" action="/public/?route=server&action=merge" id="mergeForm">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="merge_data" id="mergeData">
                
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">Kéo thả để gộp máy chủ</label>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div id="serverTree"></div>
                    </div>
                </div>

                <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                    <p class="font-bold">Hướng dẫn:</p>
                    <ul class="list-disc list-inside mt-2">
                        <li>Kéo các máy chủ nguồn vào máy chủ đích để gộp</li>
                        <li>Máy chủ được kéo vào sẽ hiển thị dưới máy chủ đích</li>
                        <li>Không thể hoàn tác thao tác gộp máy chủ</li>
                    </ul>
                </div>

                <div class="flex items-center justify-end">
                    <button type="submit" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        <i class="fas fa-compress-arrows-alt mr-2"></i>Gộp máy chủ
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- jsTree CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.12/themes/default/style.min.css" />
<!-- jsTree JS -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.12/jstree.min.js"></script>

<script>
$(document).ready(function() {
    // Chuẩn bị dữ liệu cho cây
    const treeData = <?php echo json_encode($servers); ?>;

    // Khởi tạo jsTree
    $('#serverTree').jstree({
        'core': {
            'data': treeData,
            'check_callback': function (operation, node, parent, position, more) {
                // Chỉ cho phép kéo node vào node gốc (parent là '#') hoặc node gốc (parent.depth == 1)
                if (operation === "move_node") {
                    // Không cho phép lồng quá 1 cấp
                    if (parent.parents && parent.parents.length > 1) {
                        return false;
                    }
                }
                return true;
            },
            'themes': {
                'name': 'default',
                'responsive': true
            }
        },
        'plugins': ['dnd', 'wholerow'],
        'dnd': {
            'copy': false,
            'check_while_dragging': true
        }
    }).on('move_node.jstree', function(e, data) {
        // Cập nhật dữ liệu khi di chuyển node
        updateMergeData();
    });

    // Hàm cập nhật dữ liệu gộp
    function updateMergeData() {
        const tree = $('#serverTree').jstree(true);
        const mergeData = [];

        // Lấy cấu trúc cây đầy đủ
        const nodes = tree.get_json('#', {flat: false});
        nodes.forEach(node => {
            if (node.children && node.children.length > 0) {
                mergeData.push({
                    target_id: parseInt(node.id),
                    source_ids: node.children.map(child => parseInt(child.id))
                });
            }
        });

        $('#mergeData').val(JSON.stringify(mergeData));
    }

    // Xử lý khi form được submit
    $('#mergeForm').on('submit', function(e) {
        e.preventDefault();

        // Luôn cập nhật dữ liệu trước khi submit
        updateMergeData();

        const mergeData = JSON.parse($('#mergeData').val() || '[]');

        if (mergeData.length === 0) {
            alert('Vui lòng kéo thả ít nhất một máy chủ để gộp');
            return;
        }

        this.submit();
    });
});
</script>

<style>
.jstree-default .jstree-node {
    margin-left: 24px;
}

.jstree-default .jstree-anchor {
    line-height: 24px;
    height: 24px;
}

.jstree-default .jstree-icon {
    line-height: 24px;
}

.jstree-default .jstree-wholerow {
    height: 24px;
}

.jstree-default .jstree-wholerow-hovered {
    background: #e6f7ff;
}

.jstree-default .jstree-wholerow-clicked {
    background: #bae7ff;
}
</style>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 