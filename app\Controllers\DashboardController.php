<?php
namespace App\Controllers;

use App\Core\Controller;

class DashboardController extends Controller {

    public function index() {
        $this->requireLogin();

        // Ki<PERSON><PERSON> tra quyền truy cập
        if (!$this->hasPermission('dashboard.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Xử lý tham số lọc theo tháng/năm
        $year = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));
        $month = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));

        // Đảm bảo tham số hợp lệ
        if ($year < 2000 || $year > 2100) $year = intval(date('Y'));
        if ($month < 1 || $month > 12) $month = intval(date('m'));

        // Tạo timestamp cho ngày đầu và cuối tháng đã chọn
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate)); // t là ngày cuối cùng của tháng


        // Tính tổng doanh thu tháng trước để so sánh
        $prevMonthStart = date('Y-m-01', strtotime('-1 month', strtotime($startDate)));
        $prevMonthEnd = date('Y-m-t', strtotime('-1 month', strtotime($startDate)));

        // Lấy danh sách các tháng có dữ liệu
        $availableMonths = $this->db->fetchAll("
            SELECT DISTINCT YEAR(created_at) as year, MONTH(created_at) as month
            FROM game_users
            ORDER BY year DESC, month DESC
            LIMIT 24
        ");

        // Truyền biến $db vào view
        $db = $this->db;

        // --- Tính toán số liệu tổng quan ---

        // Tổng số người dùng
        $totalUsers = $this->db->fetchValue("
            SELECT COUNT(*)
            FROM game_users
        ");

        // Số người dùng mới hôm nay
        $todayUsers = $this->db->fetchValue("
            SELECT COUNT(*)
            FROM game_users
            WHERE DATE(created_at) = CURDATE()
        ");

        // Tổng doanh thu
        $totalAllRevenue = $this->db->fetchValue("
            SELECT SUM(amount)
            FROM payment_transactions
            WHERE status = 'success'
        ");

        // Doanh thu hôm nay
        $todayRevenue = $this->db->fetchValue("
            SELECT SUM(amount)
            FROM payment_transactions
            WHERE status = 'success' AND DATE(created_at) = CURDATE()
        ");

        // Doanh thu hôm nay theo server
        $todayRevenueByServer = $this->db->fetchAll("
            SELECT
                server_id,
                SUM(amount) as total_amount,
                COUNT(*) as transaction_count,
                COUNT(DISTINCT user_id) as unique_payers
            FROM payment_transactions
            WHERE status = 'success' AND DATE(created_at) = CURDATE()
            GROUP BY server_id
            ORDER BY total_amount DESC
        ");

        // Get all server IDs for server selection
        $allServers = $this->db->fetchAll("
            SELECT DISTINCT server_id, COUNT(*) as transaction_count
            FROM payment_transactions
            WHERE status = 'success'
            GROUP BY server_id
            ORDER BY server_id
        ");

        // Prepare data for revenue by server chart
        $revenueByServerChartData = [
            'labels' => [], // Days
            'datasets' => [] // One dataset per server
        ];

        // Get the last 30 days for the chart
        $thirtyDaysAgo = date('Y-m-d', strtotime('-30 days'));
        $today = date('Y-m-d');

        // Generate date labels for the last 30 days
        $currentDate = new \DateTime($thirtyDaysAgo);
        $endDate2 = new \DateTime($today);

        while ($currentDate <= $endDate2) {
            $revenueByServerChartData['labels'][] = $currentDate->format('Y-m-d');
            $currentDate->modify('+1 day');
        }

        // Get revenue data for top 10 servers
        $topServers = $this->db->fetchAll("
            SELECT
                server_id,
                SUM(amount) as total_amount
            FROM payment_transactions
            WHERE status = 'success' AND created_at >= ?
            GROUP BY server_id
            ORDER BY total_amount DESC
            LIMIT 10
        ", [$thirtyDaysAgo]);

        // Generate a color palette for servers
        $serverColors = [
            'rgba(99, 102, 241, 0.8)', // Indigo
            'rgba(239, 68, 68, 0.8)',  // Red
            'rgba(16, 185, 129, 0.8)', // Emerald
            'rgba(245, 158, 11, 0.8)', // Amber
            'rgba(139, 92, 246, 0.8)', // Purple
            'rgba(14, 165, 233, 0.8)', // Sky
            'rgba(249, 115, 22, 0.8)', // Orange
            'rgba(236, 72, 153, 0.8)', // Pink
            'rgba(20, 184, 166, 0.8)', // Teal
            'rgba(168, 85, 247, 0.8)'  // Violet
        ];

        // For each top server, get daily revenue data
        foreach ($topServers as $index => $server) {
            $serverId = $server['server_id'];
            $serverColor = $serverColors[$index % count($serverColors)];

            $dailyRevenue = $this->db->fetchAll("
                SELECT
                    DATE(created_at) as date,
                    SUM(amount) as daily_amount
                FROM payment_transactions
                WHERE status = 'success' AND server_id = ? AND created_at >= ?
                GROUP BY DATE(created_at)
                ORDER BY date
            ", [$serverId, $thirtyDaysAgo]);

            // Create a dataset for this server
            $dataset = [
                'label' => 'Server ' . $serverId,
                'data' => [],
                'backgroundColor' => $serverColor,
                'borderColor' => str_replace('0.8', '1', $serverColor),
                'borderWidth' => 1
            ];

            // Initialize with zeros
            $dailyAmounts = [];
            foreach ($revenueByServerChartData['labels'] as $date) {
                $dailyAmounts[$date] = 0;
            }

            // Fill in actual values
            foreach ($dailyRevenue as $row) {
                if (isset($dailyAmounts[$row['date']])) {
                    $dailyAmounts[$row['date']] = (int)$row['daily_amount'];
                }
            }

            // Add the data to the dataset
            foreach ($dailyAmounts as $amount) {
                $dataset['data'][] = $amount;
            }

            $revenueByServerChartData['datasets'][] = $dataset;
        }

        // Doanh thu tháng này
        $thisMonthStart = date('Y-m-01');
        $thisMonthEnd = date('Y-m-t');
        $thisMonthRevenue = $this->db->fetchValue("
            SELECT SUM(amount)
            FROM payment_transactions
            WHERE status = 'success' AND created_at >= ? AND created_at <= ?
        ", [$thisMonthStart . ' 00:00:00', $thisMonthEnd . ' 23:59:59']);

        // Tổng số giao dịch thành công
        $totalSuccessTransactions = $this->db->fetchValue("
            SELECT COUNT(*)
            FROM payment_transactions
            WHERE status = 'success'
        ");

        // ARPU tổng (doanh thu trung bình trên mỗi người dùng)
        $totalARPU = $totalUsers > 0 ? $totalAllRevenue / $totalUsers : 0;

        // ARPPU tổng (doanh thu trung bình trên mỗi người dùng thanh toán)
        $totalARPPU = 0;

        // Rate Pay tổng (tỉ lệ người dùng đã thanh toán)
        $totalPayingUsers = $this->db->fetchValue("
            SELECT COUNT(DISTINCT user_id)
            FROM payment_transactions
            WHERE status = 'success'
        ");
        $totalPayingRate = $totalUsers > 0 ? ($totalPayingUsers / $totalUsers) * 100 : 0;

        // Tính ARPPU
        $totalARPPU = $totalPayingUsers > 0 ? $totalAllRevenue / $totalPayingUsers : 0;

        // Daily Active Users (DAU)
        $dau = $this->db->fetchValue("
            SELECT COUNT(DISTINCT user_id)
            FROM game_users
            WHERE DATE(last_login) = CURDATE()
        ");

        // Monthly Active Users (MAU)
        $mau = $this->db->fetchValue("
            SELECT COUNT(DISTINCT user_id)
            FROM game_users
            WHERE last_login >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");

        // DAU/MAU Ratio (Stickiness)
        $dauMauRatio = $mau > 0 ? ($dau / $mau) * 100 : 0;

        // Retention Rate calculations
        // D1 Retention (1-day retention)
        $d1Retention = $this->db->fetchValue("
            SELECT
                (COUNT(DISTINCT CASE WHEN DATEDIFF(last_login, created_at) >= 1 THEN user_id END) /
                COUNT(DISTINCT user_id)) * 100 as retention_rate
            FROM game_users
            WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        ");

        // D7 Retention (7-day retention)
        $d7Retention = $this->db->fetchValue("
            SELECT
                (COUNT(DISTINCT CASE WHEN DATEDIFF(last_login, created_at) >= 7 THEN user_id END) /
                COUNT(DISTINCT user_id)) * 100 as retention_rate
            FROM game_users
            WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 7 DAY)
        ");

        // D30 Retention (30-day retention)
        $d30Retention = $this->db->fetchValue("
            SELECT
                (COUNT(DISTINCT CASE WHEN DATEDIFF(last_login, created_at) >= 30 THEN user_id END) /
                COUNT(DISTINCT user_id)) * 100 as retention_rate
            FROM game_users
            WHERE DATE(created_at) = DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        ");

        // Comprehensive Retention Rate Table (12 months x 31 days)
        $retentionRateTable = [];

        // Get current year
        $currentYear = date('Y');

        // For each month in the current year
        for ($month = 1; $month <= 12; $month++) {
            $monthData = [];
            $monthName = date('F', mktime(0, 0, 0, $month, 1, $currentYear));
            $monthData['month_name'] = $monthName;
            $monthData['month_number'] = $month;

            // Calculate the first day of the month
            $firstDayOfMonth = sprintf('%04d-%02d-01', $currentYear, $month);

            // Calculate the last day of the month
            $lastDayOfMonth = date('Y-m-t', strtotime($firstDayOfMonth));

            // Skip future months
            if (strtotime($firstDayOfMonth) > time()) {
                $retentionRateTable[$month] = $monthData;
                continue;
            }

            // Get all users who registered in this month
            $registeredUsers = $this->db->fetchValue("
                SELECT COUNT(DISTINCT user_id)
                FROM game_users
                WHERE created_at >= ? AND created_at <= ?
            ", [$firstDayOfMonth, $lastDayOfMonth]);

            $monthData['registered_users'] = $registeredUsers;

            // Calculate retention for each day (D1 to D31)
            $retentionRates = [];
            for ($day = 1; $day <= 31; $day++) {
                // Skip calculation for future days
                if (strtotime($firstDayOfMonth) + ($day * 86400) > time()) {
                    $retentionRates[$day] = null;
                    continue;
                }

                $retentionRate = $this->db->fetchValue("
                    SELECT
                        (COUNT(DISTINCT CASE WHEN DATEDIFF(last_login, created_at) >= ? THEN user_id END) /
                        COUNT(DISTINCT user_id)) * 100 as retention_rate
                    FROM game_users
                    WHERE created_at >= ? AND created_at <= ?
                ", [$day, $firstDayOfMonth, $lastDayOfMonth]);

                $retentionRates[$day] = $retentionRate;
            }

            $monthData['retention_rates'] = $retentionRates;
            $retentionRateTable[$month] = $monthData;
        }

        // Lấy dữ liệu role online theo giờ hôm nay
        $todayOnlineStats = $this->db->fetchAll("
            SELECT
                HOUR(created_at) as hour,
                MAX(total_online) as max_online,
                MAX(server_stats) as server_stats
            FROM online_role_stats
            WHERE DATE(created_at) = CURDATE()
            GROUP BY HOUR(created_at)
            ORDER BY hour
        ");

        // Lấy số người chơi đồng thời cao nhất trong ngày
        $peakConcurrentUsers = $this->db->fetchValue("
            SELECT MAX(total_online)
            FROM online_role_stats
            WHERE DATE(created_at) = CURDATE()
        ");

        // Lấy số người chơi đồng thời cao nhất trong tháng
        $peakConcurrentUsersMonth = $this->db->fetchValue("
            SELECT MAX(total_online)
            FROM online_role_stats
            WHERE created_at >= ? AND created_at <= ?
        ", [$startDate, $endDate]);

        // Phân tích phân bố người chơi theo server
        $serverDistribution = [];
        // Comprehensive Monthly Revenue Table (12 months x 31 days)
        $monthlyRevenueTable = [];

        // Get current year
        $currentYear = date('Y');

        // For each month in the current year
        for ($month = 1; $month <= 12; $month++) {
            $monthData = [];
            $monthName = date('F', mktime(0, 0, 0, $month, 1, $currentYear));
            $monthData['month_name'] = $monthName;
            $monthData['month_number'] = $month;

            // Calculate the first day of the month
            $firstDayOfMonth = sprintf('%04d-%02d-01', $currentYear, $month);

            // Calculate the last day of the month
            $lastDayOfMonth = date('Y-m-t', strtotime($firstDayOfMonth));

            // Skip future months
            if (strtotime($firstDayOfMonth) > time()) {
                $monthlyRevenueTable[$month] = $monthData;
                continue;
            }

            // Get total revenue for the month
            $totalMonthRevenue = $this->db->fetchValue("
                SELECT SUM(amount) as total_revenue
                FROM payment_transactions
                WHERE status = 'success' AND created_at >= ? AND created_at <= ?
            ", [$firstDayOfMonth . ' 00:00:00', $lastDayOfMonth . ' 23:59:59']);

            $monthData['total_revenue'] = $totalMonthRevenue ?? 0;

            // Get transaction count for the month
            $transactionCount = $this->db->fetchValue("
                SELECT COUNT(*) as transaction_count
                FROM payment_transactions
                WHERE status = 'success' AND created_at >= ? AND created_at <= ?
            ", [$firstDayOfMonth . ' 00:00:00', $lastDayOfMonth . ' 23:59:59']);

            $monthData['transaction_count'] = $transactionCount ?? 0;

            // Calculate daily revenue for each day (1 to 31)
            $dailyRevenues = [];
            $daysInMonth = date('t', strtotime($firstDayOfMonth));

            for ($day = 1; $day <= 31; $day++) {
                // Skip days that don't exist in this month
                if ($day > $daysInMonth) {
                    $dailyRevenues[$day] = null;
                    continue;
                }

                // Skip calculation for future days
                $currentDayDate = sprintf('%04d-%02d-%02d', $currentYear, $month, $day);
                if (strtotime($currentDayDate) > time()) {
                    $dailyRevenues[$day] = null;
                    continue;
                }

                $dailyRevenue = $this->db->fetchValue("
                    SELECT SUM(amount) as daily_revenue
                    FROM payment_transactions
                    WHERE status = 'success'
                    AND DATE(created_at) = ?
                ", [$currentDayDate]);

                $dailyRevenues[$day] = $dailyRevenue;
            }

            $monthData['daily_revenues'] = $dailyRevenues;
            $monthlyRevenueTable[$month] = $monthData;
        }

        $latestServerStats = $this->db->fetch("
            SELECT server_stats
            FROM online_role_stats
            WHERE server_stats IS NOT NULL
            ORDER BY created_at DESC
            LIMIT 1
        ");

        if ($latestServerStats && !empty($latestServerStats['server_stats'])) {
            $serverDistribution = json_decode($latestServerStats['server_stats'], true) ?: [];
        }

        // Chuẩn bị dữ liệu cho biểu đồ
        $onlineChartData = [
            'labels' => [],
            'datasets' => [
                [
                    'label' => 'Tổng số role online',
                    'data' => [],
                    'backgroundColor' => 'rgba(99, 102, 241, 0.8)',
                    'borderColor' => 'rgb(99, 102, 241)',
                    'borderWidth' => 1
                ]
            ]
        ];

        // Điền dữ liệu vào mảng
        foreach ($todayOnlineStats as $stat) {
            $onlineChartData['labels'][] = sprintf('%02d:00', $stat['hour']);
            $onlineChartData['datasets'][0]['data'][] = $stat['max_online'];
        }

        // --- Tính toán số liệu theo tháng đã chọn ---

        // NRU (New Registered Users) - Tính người dùng mới đăng ký theo ngày trong tháng đã chọn
        $nruData = $db->fetchAll("
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM game_users
            WHERE created_at >= ? AND created_at <= ?
            GROUP BY DATE(created_at)
            ORDER BY date
        ", [$startDate, $endDate]);

        // Tính tổng số người đăng ký trong tháng
        $totalRegistrations = $db->fetch("
            SELECT COUNT(*) as count
            FROM game_users
            WHERE created_at >= ? AND created_at <= ?
        ", [$startDate, $endDate]);

        // Tính tổng số người đăng ký tháng trước để so sánh
        $prevMonthRegistrations = $db->fetch("
            SELECT COUNT(*) as count
            FROM game_users
            WHERE created_at >= ? AND created_at <= ?
        ", [$prevMonthStart, $prevMonthEnd]);

        // Tính tỷ lệ tăng trưởng người dùng so với tháng trước
        $userGrowth = 0;
        if (($prevMonthRegistrations['count'] ?? 0) > 0) {
            $userGrowth = ((($totalRegistrations['count'] ?? 0) - ($prevMonthRegistrations['count'] ?? 0)) / ($prevMonthRegistrations['count'] ?? 0)) * 100;
        }

        // Tính số người dùng active trong tháng
        $activeUsersInMonth = $db->fetch("
            SELECT COUNT(DISTINCT user_id) as count
            FROM game_users
            WHERE last_login >= ? AND last_login <= ?
        ", [$startDate, $endDate]);

        // Tính retention theo ngày trong tháng
        $retentionData = $db->fetchAll("
            SELECT
                DATE(gu.created_at) as date,
                COUNT(DISTINCT gu.id) as total_users,
                COUNT(DISTINCT CASE WHEN gu.last_login IS NOT NULL AND DATEDIFF(gu.last_login, gu.created_at) >= 1 THEN gu.id END) as d1_retained,
                COUNT(DISTINCT CASE WHEN gu.last_login IS NOT NULL AND DATEDIFF(gu.last_login, gu.created_at) >= 7 THEN gu.id END) as d7_retained
            FROM game_users gu
            WHERE gu.created_at >= ? AND gu.created_at <= ?
            GROUP BY DATE(gu.created_at)
        ", [$startDate, $endDate]);

        // Tính doanh thu theo ngày trong tháng
        $revenueData = $db->fetchAll("
            SELECT
                DATE(created_at) as date,
                COUNT(*) as transactions,
                SUM(amount) as total
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ? AND status = 'success'
            GROUP BY DATE(created_at)
            ORDER BY date
        ", [$startDate, $endDate]);

        // Tính tổng doanh thu trong tháng
        $totalRevenue = $db->fetch("
            SELECT SUM(amount) as total
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ? AND status = 'success'
        ", [$startDate, $endDate]);

        $prevMonthRevenue = $db->fetch("
            SELECT SUM(amount) as total
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ? AND status = 'success'
        ", [$prevMonthStart, $prevMonthEnd]);

        // Tính tỷ lệ tăng trưởng doanh thu so với tháng trước
        $revenueGrowth = 0;
        if (($prevMonthRevenue['total'] ?? 0) > 0) {
            $revenueGrowth = ((($totalRevenue['total'] ?? 0) - ($prevMonthRevenue['total'] ?? 0)) / ($prevMonthRevenue['total'] ?? 0)) * 100;
        }

        // Tính số lượng giao dịch thành công trong tháng
        $successTransactions = $db->fetch("
            SELECT COUNT(*) as count
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ? AND status = 'success'
        ", [$startDate, $endDate]);

        // Tỉ lệ người chơi thanh toán trong tháng (Rate pay)
        $payingRate = $db->fetch("
            SELECT
                (SELECT COUNT(DISTINCT user_id) FROM payment_transactions
                 WHERE status = 'success' AND created_at >= ? AND created_at <= ?) as paying_users,
                (SELECT COUNT(*) FROM game_users
                 WHERE created_at >= ? AND created_at <= ?) as total_users
        ", [$startDate, $endDate, $startDate, $endDate]);

        $payingRatePercent = 0;
        if (($payingRate['total_users'] ?? 0) > 0) {
            $payingRatePercent = round(($payingRate['paying_users'] / $payingRate['total_users']) * 100, 2);
        }

        // Phân tích doanh thu theo nguồn
        $revenueBySource = $db->fetchAll("
            SELECT
                source,
                COUNT(*) as count,
                SUM(amount) as total
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ? AND status = 'success'
            GROUP BY source
            ORDER BY total DESC
        ", [$startDate, $endDate]);

        // Phân tích hành vi chi trả theo cấp độ
        $paymentByLevel = $db->fetchAll("
            SELECT
                CASE
                    WHEN game_level <= 10 THEN '1-10'
                    WHEN game_level <= 20 THEN '11-20'
                    WHEN game_level <= 30 THEN '21-30'
                    WHEN game_level <= 40 THEN '31-40'
                    WHEN game_level <= 50 THEN '41-50'
                    WHEN game_level <= 60 THEN '51-60'
                    WHEN game_level <= 70 THEN '61-70'
                    WHEN game_level <= 80 THEN '71-80'
                    WHEN game_level <= 90 THEN '81-90'
                    ELSE '91+'
                END as level_range,
                COUNT(*) as transaction_count,
                SUM(amount) as total_amount,
                COUNT(DISTINCT user_id) as unique_payers,
                AVG(amount) as avg_amount
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ? AND status = 'success' AND game_level > 0
            GROUP BY level_range
            ORDER BY MIN(game_level)
        ", [$startDate, $endDate]);

        // Cấp độ trung bình khi người chơi bắt đầu chi tiền
        $avgFirstPaymentLevel = $db->fetch("
            SELECT
                AVG(first_payment_level) as avg_level,
                MIN(first_payment_level) as min_level,
                MAX(first_payment_level) as max_level
            FROM (
                SELECT
                    user_id,
                    MIN(game_level) as first_payment_level
                FROM payment_transactions
                WHERE status = 'success' AND game_level > 0
                GROUP BY user_id
            ) as first_payments
        ");

        // Trạng thái thanh toán
        $paymentStatus = $db->fetchAll("
            SELECT
                status,
                COUNT(*) as count,
                SUM(amount) as total
            FROM payment_transactions
            WHERE created_at >= ? AND created_at <= ?
            GROUP BY status
        ", [$startDate, $endDate]);

        // Tính ARPU (Average Revenue Per User)
        $arpu = 0;
        if (($totalRegistrations['count'] ?? 0) > 0) {
            $arpu = ($totalRevenue['total'] ?? 0) / $totalRegistrations['count'];
        }


        $roleOnlineCount = 0;
        $servers = $this->db->getServers(true, true);
        $connectResults = $this->gameDatabaseManager->connectServers($servers);
        $status = $this->checkGameDbConnections($connectResults, false);
        if ($status) {
            $sql = $this->gameSqlBuilder->buildSelect('role', 'is_online = ?', [1]);
            $results = $this->gameDatabaseManager->executeOnAll($sql['sql'], $sql['params']);
            foreach ($results as $serverId => $result) {
                if ($result['success']) {
                    $roleOnlineCount += count($result['data']);
                }
            }
        }

        // Truyền các biến vào view
        $stats = [
            'year' => $year,
            'month' => $month,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'availableMonths' => $availableMonths,
            'nruData' => $nruData,
            'retentionData' => $retentionData,
            'revenueData' => $revenueData,
            'totalRegistrations' => $totalRegistrations['count'] ?? 0,
            'totalRevenue' => $totalRevenue['total'] ?? 0,
            'successTransactions' => $successTransactions['count'] ?? 0,
            'payingUsers' => $payingRate['paying_users'] ?? 0,
            'totalUsers' => $payingRate['total_users'] ?? 0,
            'payingRate' => $payingRatePercent,
            'revenueBySource' => $revenueBySource,
            'paymentStatus' => $paymentStatus,
            'arpu' => $arpu,
            'roleOnlineCount' => $roleOnlineCount,
            'onlineChartData' => $onlineChartData,
            // New metrics
            'arppu' => $totalARPPU,
            'dau' => $dau,
            'mau' => $mau,
            'dauMauRatio' => $dauMauRatio,
            'peakConcurrentUsers' => $peakConcurrentUsers,
            'peakConcurrentUsersMonth' => $peakConcurrentUsersMonth,
            'serverDistribution' => $serverDistribution,
            'revenueGrowth' => $revenueGrowth,
            'userGrowth' => $userGrowth,
            'activeUsersInMonth' => $activeUsersInMonth['count'] ?? 0,
            'prevMonthRevenue' => $prevMonthRevenue['total'] ?? 0,
            'prevMonthRegistrations' => $prevMonthRegistrations['count'] ?? 0,
            // Additional new metrics
            'd1Retention' => $d1Retention ?? 0,
            'd7Retention' => $d7Retention ?? 0,
            'd30Retention' => $d30Retention ?? 0,
            'todayRevenueByServer' => $todayRevenueByServer ?? [],
            'paymentByLevel' => $paymentByLevel ?? [],
            'avgFirstPaymentLevel' => $avgFirstPaymentLevel['avg_level'] ?? 0,
            'minFirstPaymentLevel' => $avgFirstPaymentLevel['min_level'] ?? 0,
            'maxFirstPaymentLevel' => $avgFirstPaymentLevel['max_level'] ?? 0,
            // New comprehensive retention and revenue data
            'retentionRateTable' => $retentionRateTable ?? [],
            'monthlyRevenueTable' => $monthlyRevenueTable ?? [],
            'allServers' => $allServers ?? [],
            'revenueByServerChartData' => $revenueByServerChartData ?? []
        ];

        // Đảm bảo biến có giá trị an toàn
        if (!isset($totalUsers)) $totalUsers = 0;
        if (!isset($todayUsers)) $todayUsers = 0;
        if (!isset($totalAllRevenue)) $totalAllRevenue = 0;
        if (!isset($todayRevenue)) $todayRevenue = 0;
        if (!isset($thisMonthRevenue)) $thisMonthRevenue = 0;
        if (!isset($totalSuccessTransactions)) $totalSuccessTransactions = 0;
        if (!isset($totalARPU)) $totalARPU = 0;
        if (!isset($totalARPPU)) $totalARPPU = 0;
        if (!isset($totalPayingRate)) $totalPayingRate = 0;
        if (!isset($dau)) $dau = 0;
        if (!isset($mau)) $mau = 0;
        if (!isset($dauMauRatio)) $dauMauRatio = 0;
        if (!isset($peakConcurrentUsers)) $peakConcurrentUsers = 0;
        if (!isset($peakConcurrentUsersMonth)) $peakConcurrentUsersMonth = 0;
        if (!isset($revenueGrowth)) $revenueGrowth = 0;
        if (!isset($userGrowth)) $userGrowth = 0;
        if (!isset($d1Retention)) $d1Retention = 0;
        if (!isset($d7Retention)) $d7Retention = 0;
        if (!isset($d30Retention)) $d30Retention = 0;
        if (!isset($todayRevenueByServer)) $todayRevenueByServer = [];
        if (!isset($paymentByLevel)) $paymentByLevel = [];
        if (!isset($avgFirstPaymentLevel)) $avgFirstPaymentLevel = ['avg_level' => 0, 'min_level' => 0, 'max_level' => 0];
        if (!isset($retentionRateTable)) $retentionRateTable = [];
        if (!isset($monthlyRevenueTable)) $monthlyRevenueTable = [];
        if (!isset($allServers)) $allServers = [];
        if (!isset($revenueByServerChartData)) $revenueByServerChartData = ['labels' => [], 'datasets' => []];

        require_once __DIR__ . '/../../resources/views/dashboard/index.php';
    }

    // Xuất dữ liệu ra Excel
    public function export_excel() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('dashboard.view')) {
            header('Location: /public/?route=auth&action=login');
            exit;
        }

        // Xử lý tham số lọc theo tháng/năm
        $year = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));
        $month = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
        $type = isset($_GET['type']) ? $_GET['type'] : 'users';

        // Đảm bảo tham số hợp lệ
        if ($year < 2000 || $year > 2100) $year = intval(date('Y'));
        if ($month < 1 || $month > 12) $month = intval(date('m'));

        // Tạo timestamp cho ngày đầu và cuối tháng đã chọn
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));

        // Tên file Excel
        $filename = sprintf('dashboard_%s_%04d_%02d.csv', $type, $year, $month);

        // Header cho file CSV
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');

        // Tạo file handle
        $output = fopen('php://output', 'w');

        // BOM (Byte Order Mark) để Excel hiển thị đúng tiếng Việt
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Xuất dữ liệu tùy theo loại báo cáo
        switch ($type) {
            case 'users':
                // Xuất dữ liệu người dùng mới theo ngày
                fputcsv($output, ['Ngày', 'Số người đăng ký mới', 'Tỉ lệ quay lại (1 ngày)', 'Tỉ lệ quay lại (7 ngày)']);

                $userData = $this->db->fetchAll("
                    SELECT
                        DATE(gu.created_at) as date,
                        COUNT(DISTINCT gu.id) as total_users,
                        COUNT(DISTINCT CASE WHEN gu.last_login IS NOT NULL AND DATEDIFF(gu.last_login, gu.created_at) >= 1 THEN gu.id END) as d1_retained,
                        COUNT(DISTINCT CASE WHEN gu.last_login IS NOT NULL AND DATEDIFF(gu.last_login, gu.created_at) >= 7 THEN gu.id END) as d7_retained
                    FROM game_users gu
                    WHERE gu.created_at >= ? AND gu.created_at <= ?
                    GROUP BY DATE(gu.created_at)
                    ORDER BY date
                ", [$startDate, $endDate]);

                foreach ($userData as $row) {
                    $d1_retention = $row['total_users'] > 0 ? round(($row['d1_retained'] / $row['total_users']) * 100, 2) : 0;
                    $d7_retention = $row['total_users'] > 0 ? round(($row['d7_retained'] / $row['total_users']) * 100, 2) : 0;

                    fputcsv($output, [
                        $row['date'],
                        $row['total_users'],
                        $d1_retention . '%',
                        $d7_retention . '%'
                    ]);
                }
                break;

            case 'revenue':
                // Xuất dữ liệu doanh thu theo ngày
                fputcsv($output, ['Ngày', 'Số giao dịch', 'Doanh thu (VNĐ)', 'Doanh thu trung bình (VNĐ)']);

                $revenueData = $this->db->fetchAll("
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as transactions,
                        SUM(amount) as total
                    FROM payment_transactions
                    WHERE created_at >= ? AND created_at <= ? AND status = 'success'
                    GROUP BY DATE(created_at)
                    ORDER BY date
                ", [$startDate, $endDate]);

                foreach ($revenueData as $row) {
                    $avgRevenue = $row['transactions'] > 0 ? round($row['total'] / $row['transactions']) : 0;

                    fputcsv($output, [
                        $row['date'],
                        $row['transactions'],
                        $row['total'],
                        $avgRevenue
                    ]);
                }
                break;

            case 'sources':
                // Xuất dữ liệu theo nguồn đăng ký
                fputcsv($output, ['Nguồn', 'Số người dùng', 'Tỉ lệ (%)']);

                $sourceData = $this->db->fetchAll("
                    SELECT register_source, COUNT(*) as count
                    FROM game_users
                    WHERE register_source IS NOT NULL AND created_at >= ? AND created_at <= ?
                    GROUP BY register_source
                    ORDER BY count DESC
                ", [$startDate, $endDate]);

                $totalUsers = 0;
                foreach ($sourceData as $row) {
                    $totalUsers += $row['count'];
                }

                foreach ($sourceData as $row) {
                    $percent = $totalUsers > 0 ? round(($row['count'] / $totalUsers) * 100, 2) : 0;
                    $sourceName = $row['register_source'] ?: 'Không xác định';

                    fputcsv($output, [
                        $sourceName,
                        $row['count'],
                        $percent . '%'
                    ]);
                }
                break;

            case 'payment_sources':
                // Xuất dữ liệu theo nguồn thanh toán
                fputcsv($output, ['Nguồn', 'Số giao dịch', 'Doanh thu (VNĐ)', 'Tỉ lệ (%)']);

                $paymentSourceData = $this->db->fetchAll("
                    SELECT
                        source,
                        COUNT(*) as count,
                        SUM(amount) as total
                    FROM payment_transactions
                    WHERE created_at >= ? AND created_at <= ? AND status = 'success'
                    GROUP BY source
                    ORDER BY total DESC
                ", [$startDate, $endDate]);

                $totalAmount = 0;
                foreach ($paymentSourceData as $row) {
                    $totalAmount += $row['total'];
                }

                foreach ($paymentSourceData as $row) {
                    $percent = $totalAmount > 0 ? round(($row['total'] / $totalAmount) * 100, 2) : 0;

                    fputcsv($output, [
                        $row['source'],
                        $row['count'],
                        $row['total'],
                        $percent . '%'
                    ]);
                }
                break;

            default:
                fputcsv($output, ['Loại báo cáo không hợp lệ']);
                break;
        }

        fclose($output);
        exit;
    }

    // API lấy dữ liệu cho biểu đồ
    public function get_chart_data() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('dashboard.view')) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Không có quyền truy cập']);
            exit;
        }

        // Xử lý tham số lọc theo tháng/năm
        $year = isset($_GET['year']) ? intval($_GET['year']) : intval(date('Y'));
        $month = isset($_GET['month']) ? intval($_GET['month']) : intval(date('m'));
        $type = isset($_GET['type']) ? $_GET['type'] : 'users';

        // Đảm bảo tham số hợp lệ
        if ($year < 2000 || $year > 2100) $year = intval(date('Y'));
        if ($month < 1 || $month > 12) $month = intval(date('m'));

        // Tạo timestamp cho ngày đầu và cuối tháng đã chọn
        $startDate = sprintf('%04d-%02d-01', $year, $month);
        $endDate = date('Y-m-t', strtotime($startDate));

        // Tạo mảng chứa tất cả các ngày trong tháng
        $allDates = [];
        $currentDate = new \DateTime($startDate);
        $lastDate = new \DateTime($endDate);

        while ($currentDate <= $lastDate) {
            $allDates[] = $currentDate->format('Y-m-d');
            $currentDate->modify('+1 day');
        }

        // Kết quả mặc định
        $result = [
            'labels' => $allDates,
            'datasets' => []
        ];

        // Lấy dữ liệu tùy theo loại biểu đồ
        switch ($type) {
            case 'users':
                // Người dùng mới và tỉ lệ quay lại
                $userData = $this->db->fetchAll("
                    SELECT
                        DATE(gu.created_at) as date,
                        COUNT(DISTINCT gu.id) as total_users,
                        COUNT(DISTINCT CASE WHEN gu.last_login IS NOT NULL AND DATEDIFF(gu.last_login, gu.created_at) >= 1 THEN gu.id END) as d1_retained,
                        COUNT(DISTINCT CASE WHEN gu.last_login IS NOT NULL AND DATEDIFF(gu.last_login, gu.created_at) >= 7 THEN gu.id END) as d7_retained
                    FROM game_users gu
                    WHERE gu.created_at >= ? AND gu.created_at <= ?
                    GROUP BY DATE(gu.created_at)
                    ORDER BY date
                ", [$startDate, $endDate]);

                // Chuyển đổi kết quả vào mảng tương ứng với từng ngày
                $usersByDate = [];
                $d1RetentionByDate = [];
                $d7RetentionByDate = [];

                foreach ($userData as $row) {
                    $date = $row['date'];
                    $usersByDate[$date] = (int)$row['total_users'];

                    $d1Retention = $row['total_users'] > 0 ? round(($row['d1_retained'] / $row['total_users']) * 100, 2) : 0;
                    $d7Retention = $row['total_users'] > 0 ? round(($row['d7_retained'] / $row['total_users']) * 100, 2) : 0;

                    $d1RetentionByDate[$date] = $d1Retention;
                    $d7RetentionByDate[$date] = $d7Retention;
                }

                // Tạo các mảng dữ liệu cho tất cả các ngày
                $usersData = [];
                $d1RetentionData = [];
                $d7RetentionData = [];

                foreach ($allDates as $date) {
                    $usersData[] = $usersByDate[$date] ?? 0;
                    $d1RetentionData[] = $d1RetentionByDate[$date] ?? 0;
                    $d7RetentionData[] = $d7RetentionByDate[$date] ?? 0;
                }

                $result['datasets'] = [
                    [
                        'label' => 'Người dùng mới',
                        'data' => $usersData,
                        'backgroundColor' => 'rgba(99, 102, 241, 0.8)',
                        'borderColor' => 'rgb(99, 102, 241)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ],
                    [
                        'label' => 'Tỉ lệ quay lại (1 ngày)',
                        'data' => $d1RetentionData,
                        'backgroundColor' => 'rgba(0, 0, 0, 0)',
                        'borderColor' => 'rgb(234, 88, 12)',
                        'borderWidth' => 2,
                        'type' => 'line',
                        'yAxisID' => 'y1'
                    ],
                    [
                        'label' => 'Tỉ lệ quay lại (7 ngày)',
                        'data' => $d7RetentionData,
                        'backgroundColor' => 'rgba(0, 0, 0, 0)',
                        'borderColor' => 'rgb(16, 185, 129)',
                        'borderWidth' => 2,
                        'type' => 'line',
                        'yAxisID' => 'y1'
                    ]
                ];
                break;

            case 'revenue':
                // Doanh thu theo ngày
                $revenueData = $this->db->fetchAll("
                    SELECT
                        DATE(created_at) as date,
                        COUNT(*) as transactions,
                        SUM(amount) as total
                    FROM payment_transactions
                    WHERE created_at >= ? AND created_at <= ? AND status = 'success'
                    GROUP BY DATE(created_at)
                    ORDER BY date
                ", [$startDate, $endDate]);

                // Chuyển đổi kết quả vào mảng tương ứng với từng ngày
                $revenueByDate = [];
                $transactionsByDate = [];

                foreach ($revenueData as $row) {
                    $date = $row['date'];
                    $revenueByDate[$date] = (float)$row['total'];
                    $transactionsByDate[$date] = (int)$row['transactions'];
                }

                // Tạo các mảng dữ liệu cho tất cả các ngày
                $revenueData = [];
                $transactionsData = [];

                foreach ($allDates as $date) {
                    $revenueData[] = $revenueByDate[$date] ?? 0;
                    $transactionsData[] = $transactionsByDate[$date] ?? 0;
                }

                $result['datasets'] = [
                    [
                        'label' => 'Doanh thu (VNĐ)',
                        'data' => $revenueData,
                        'backgroundColor' => 'rgba(245, 158, 11, 0.8)',
                        'borderColor' => 'rgb(245, 158, 11)',
                        'borderWidth' => 1,
                        'yAxisID' => 'y'
                    ],
                    [
                        'label' => 'Số giao dịch',
                        'data' => $transactionsData,
                        'backgroundColor' => 'rgba(0, 0, 0, 0)',
                        'borderColor' => 'rgb(59, 130, 246)',
                        'borderWidth' => 2,
                        'type' => 'line',
                        'yAxisID' => 'y1'
                    ]
                ];
                break;
        }

        // Trả về dữ liệu dưới dạng JSON
        header('Content-Type: application/json');
        echo json_encode($result);
        exit;
    }
}