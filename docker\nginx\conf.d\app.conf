server {
    listen 80;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /var/www;

    # Cấu hình CORS
    add_header 'Access-Control-Allow-Origin' '*' always;
    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
    add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
    add_header 'Access-Control-Max-Age' '86400' always;

    # Chặn truy cập vào các file và thư mục nhạy cảm
    location ~ /\. {
        deny all;
    }
	
	# location /tt09_dev2_s1/fightdata {
    #     alias "/etc/nginx/game/logs";
    #     autoindex on;
    #     add_header Access-Control-Allow-Origin "*";
    # }

    location ~ ^/(vendor|composer\.(lock|json)|\.env) {
        deny all;
    }

    # Chặn truy cập trực tiếp vào các thư mục nhạy cảm
    location ~ ^/(app|config|vendor|\.git) {
        deny all;
    }

    # X<PERSON> lý API requests
    location /api/ {
        try_files $uri $uri/ /api/index.php?$query_string;
    }

    # X<PERSON> lý public requests
    location /public/ {
        try_files $uri $uri/ /public/index.php?$query_string;
    }

    # Xử lý client requests
    location /client/ {
		alias /var/www/client/;  # Thư mục chứa các tệp tĩnh
		try_files $uri =404;  # Trả về lỗi 404 nếu không tìm thấy tệp
	}

    # Chặn truy cập vào các thư mục khác
    location / {
        deny all;
    }

    # Xử lý PHP files
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # Tắt directory listing
    autoindex off;
}