<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Services\UserService;

class UserController extends Controller {
    private $userConfig;
    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->userConfig = require __DIR__ . '/../../config/user.php';
    }

    public function index() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('user.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy tham số tìm kiếm và phân trang
        $search = $_GET['search'] ?? '';
        $page = max(1, intval($_GET['page'] ?? 1));
        $perPage = $this->userConfig['pagination']['per_page'];
        $offset = ($page - 1) * $perPage;

        // Xây dựng query tìm kiếm
        $where = [];
        $params = [];

        if (!empty($search) && strlen($search) >= $this->userConfig['search']['min_length']) {
            $searchFields = $this->userConfig['search']['fields'];
            $searchConditions = [];
            
            foreach ($searchFields as $field) {
                $searchConditions[] = "$field LIKE ?";
                $params[] = "%$search%";
            }
            
            $where[] = '(' . implode(' OR ', $searchConditions) . ')';
        }

        // Xây dựng query đếm tổng số bản ghi
        $countQuery = "SELECT COUNT(*) as total FROM game_users";
        if (!empty($where)) {
            $countQuery .= " WHERE " . implode(' AND ', $where);
        }
        $total = (int)$this->db->fetch($countQuery, $params)['total'];

        // Xây dựng query lấy dữ liệu
        $query = "SELECT * FROM game_users";
        if (!empty($where)) {
            $query .= " WHERE " . implode(' AND ', $where);
        }
        $query .= " ORDER BY id DESC";
        
        // Thêm LIMIT và OFFSET chỉ khi có dữ liệu
        if ($total > 0) {
            $query .= " LIMIT " . (int)$perPage . " OFFSET " . (int)$offset;
        }
        
        // Lấy danh sách người dùng using UserService
        $users = $this->getUserService()->getList($query, $params, $page, $perPage);
    
        // Format dữ liệu
        foreach ($users as &$user) {
            $user['register_source'] = $this->userConfig['register_sources'][$user['register_source']] ?? $user['register_source'];
            $user['last_login'] = $user['last_login'] ? date($this->userConfig['display']['date_format'], strtotime($user['last_login'])) : 'Chưa đăng nhập';            
        }
        unset($user); // Giải phóng tham chiếu

        // Tính toán phân trang
        $totalPages = max(1, ceil($total / $perPage));
        $page = min($page, $totalPages); // Đảm bảo page không vượt quá tổng số trang
        
        $pagination = [
            'current' => $page,
            'total' => $totalPages,
            'per_page' => $perPage,
            'total_items' => $total,
            'max_links' => $this->userConfig['pagination']['max_links']
        ];

        require_once __DIR__ . '/../../resources/views/user/index.php';
    }

    public function edit() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('user.view.edit')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy ID từ URL
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /public/?route=user');
            exit;
        }

        // Lấy thông tin người dùng using UserService
        $user = $this->getUserService()->getById($id);

        if (!$user) {
            header('Location: /public/?route=user');
            exit;
        }

        // Format dữ liệu
        $user['register_source'] = $this->userConfig['register_sources'][$user['register_source']] ?? $user['register_source'];
        $user['last_login'] = $user['last_login'] ? date($this->userConfig['display']['date_format'], strtotime($user['last_login'])) : 'Chưa đăng nhập';
        $user['created_at'] = date($this->userConfig['display']['date_format'], strtotime($user['created_at']));
        $user['updated_at'] = date($this->userConfig['display']['date_format'], strtotime($user['updated_at']));

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $data = [
                'username' => $_POST['username'] ?? '',
                'email' => $_POST['email'] ?? '',
                'is_gm' => isset($_POST['is_gm']) ? 1 : 0,
                'status' => isset($_POST['status']) ? 1 : 0,
                'notes' => $_POST['notes'] ?? ''
            ];

            $this->getUserService()->update($id, $data);
            
            // Ghi log
            $this->db->log('user_edit', 'success', [
                'user_id' => $id,
                'changes' => $data
            ]);
            
            header('Location: /public/?route=user');
            exit;
        }

        require_once __DIR__ . '/../../resources/views/user/edit.php';
    }

    public function block() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('user.view.block')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy ID từ URL
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /public/?route=user');
            exit;
        }

        $this->getUserService()->update($id, ['status' => 0]);
        
        // Ghi log
        $this->db->log('user_block', 'success', ['user_id' => $id]);
        
        header('Location: /public/?route=user');
        exit;
    }

    public function unblock() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('user.view.block')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy ID từ URL
        $id = $_GET['id'] ?? null;
        if (!$id) {
            header('Location: /public/?route=user');
            exit;
        }

        $this->getUserService()->update($id, ['status' => 1]);
        
        // Ghi log
        $this->db->log('user_unblock', 'success', ['user_id' => $id]);
        
        header('Location: /public/?route=user');
        exit;
    }

    /**
     * Cache management endpoint
     */
    public function cache() {
        $this->requireLogin();

        // Check permission
        if (!$this->hasPermission('user.view.edit')) {
            $this->redirectToFirstAccessiblePage();
        }

        $action = $_GET['cache_action'] ?? '';

        if ($action === 'clear') {
            $this->getUserService()->clearAllCache();
            echo json_encode(['success' => true, 'message' => 'Cache người dùng đã được xóa']);
            exit;
        }

        if ($action === 'stats') {
            $stats = $this->getUserService()->getCacheStats();
            echo json_encode($stats);
            exit;
        }

        echo json_encode(['error' => 'Action không hợp lệ']);
        exit;
    }
} 