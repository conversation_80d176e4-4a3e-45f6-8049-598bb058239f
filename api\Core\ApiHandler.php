<?php
namespace Api\Core;

use Api\Core\Auth;
use Api\Exceptions\ValidationException;
use Api\Core\Validator;

trait ApiHandler
{
    use Auth;

    // Context để tracking logging info
    private $_apiLoggingContext = null;
    
    // Cached Validator instance
    private $_validatorInstance = null;
    
    // Cached endpoint config
    private $_endpointConfig = null;
    private $_currentEndpointName = null;

    /**
     * Get Validator instance (lazy loading)
     */
    private function getValidatorInstance($config = null): Validator
    {
        if ($this->_validatorInstance === null) {
            $this->_validatorInstance = Validator::getInstance($config);
        }
        return $this->_validatorInstance;
    }

    /**
     * Get endpoint config (cached)
     */
    private function getEndpointConfig(string $endpointName): ?array
    {
        // If we're asking for the same endpoint, return cached config
        if ($this->_currentEndpointName === $endpointName && $this->_endpointConfig !== null) {
            return $this->_endpointConfig;
        }
        
        // Load new config and cache it
        $this->_currentEndpointName = $endpointName;
        $this->_endpointConfig = $this->config['api']['endpoints'][$endpointName] ?? null;
        
        return $this->_endpointConfig;
    }

    /**
     * Get API path from config instead of hardcoded mapping
     */
    private function getApiPath(string $endpointName): string
    {
        // Get path from config endpoints
        $endpointConfig = $this->getEndpointConfig($endpointName);
        
        if ($endpointConfig && isset($endpointConfig['path'])) {
            return $endpointConfig['path'];
        }
        
        // Fallback for unknown endpoints
        return "/api/Unknown/{$endpointName}";
    }

    /**
     * Get request data for logging
     */
    private function getRequestData(): array
    {
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        
        if ($method === 'POST') {
            $input = file_get_contents('php://input');
            $data = json_decode($input, true);
            return $data ?: [];
        } else {
            return $_GET ?: [];
        }
    }

    /**
     * Execute một method với tự động xử lý exception
     */
    protected function handleExceptions(callable $callback, array $options = [])
    {
        try {
            return $callback();
        } catch (ValidationException $e) {
            $this->handleValidationException($e, $options);
        } catch (\Exception $e) {
            $this->handleGeneralException($e, $options);
        }
    }

    /**
     * Xử lý ValidationException
     */
    protected function handleValidationException(ValidationException $e, array $options = []): void
    {
        $statusCode = $options['validation_status_code'] ?? 200;
        $response = [
            'status' => false,
            'message' => $e->getFirstError(),
            'errors' => $options['include_all_errors'] ?? false ? $e->getErrors() : null
        ];

        // Auto log error response
        if ($this->_apiLoggingContext && ($options['auto_log'] ?? true)) {
            $this->db->logApiRequest(
                $this->_apiLoggingContext['api_path'],
                $this->_apiLoggingContext['method'],
                $this->_apiLoggingContext['request_data'],
                $response,
                $statusCode
            );
        }

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * Xử lý Exception chung
     */
    protected function handleGeneralException(\Exception $e, array $options = []): void
    {
        $statusCode = $options['status_code'] ?? 200;
        $message = $options['custom_message'] ?? $e->getMessage();
        
        // Log error if needed
        if ($options['log_error'] ?? true) {
            error_log("API Error: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
        }

        $response = [
            'status' => false,
            'message' => $message
        ];

        // Auto log error response
        if ($this->_apiLoggingContext && ($options['auto_log'] ?? true)) {
            $this->db->logApiRequest(
                $this->_apiLoggingContext['api_path'],
                $this->_apiLoggingContext['method'],
                $this->_apiLoggingContext['request_data'],
                $response,
                $statusCode
            );
        }

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * Wrapper cho API endpoints với automatic exception handling, authentication và logging
     */
    protected function apiEndpoint(string $endpointName, callable $callback, array $options = [])
    {
        // Check if API endpoint is enabled
        $endpointConfig = $this->getEndpointConfig($endpointName);
        if (!($endpointConfig['enabled'] ?? true)) {
            $response = [
                'status' => false,
                'message' => 'API is disabled'
            ];
            
            $this->jsonResponse($response, 200);
            return;
        }

        // Setup logging context
        $apiPath = $this->getApiPath($endpointName);
        $requestData = $this->getRequestData();
        $method = $_SERVER['REQUEST_METHOD'] ?? 'POST';

        if(isset($options['auto_log']) && $options['auto_log']) {
            $this->_apiLoggingContext = [
                'api_path' => $apiPath,
                'method' => $method,
                'request_data' => $requestData,
                'auto_log' => true
            ];
        }else{
            $this->_apiLoggingContext = null;
        }

        return $this->handleExceptions(function() use ($endpointName, $callback, $options) {
            // Tự động xác minh authentication dựa trên cấu hình
            $this->autoVerifyToken($endpointName);
            
            // Get validator instance để pass vào callback
            $validator = $this->getValidatorInstance($this->_endpointConfig);
            
            // Tự động validate parameters dựa trên config (nếu không bị disable)
            if (!($options['skip_param_validation'] ?? false)) {
                $validator->validateEndpointParams($endpointName); // Chỉ validate, không return data
            }
            
            // Execute callback với validator instance
            // Callback tự gọi validator.input() khi cần data
            $reflection = new \ReflectionFunction($callback);
            $paramCount = $reflection->getNumberOfParameters();
            
            if ($paramCount >= 1) {
                // Callback nhận validator instance
                return $callback($validator);
            } else {
                // Callback không nhận parameter nào
                return $callback();
            }
        }, $options);
    }

    /**
     * Quick response methods với auto-logging
     */
    protected function successResponse($data = null, string $message = 'Success'): void
    {
        $response = [
            'status' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        // Auto log successful response
        if ($this->_apiLoggingContext && $this->_apiLoggingContext['auto_log']) {
            $this->db->logApiRequest(
                $this->_apiLoggingContext['api_path'],
                $this->_apiLoggingContext['method'],
                $this->_apiLoggingContext['request_data'],
                $response,
                200
            );
        }
        
        $this->jsonResponse($response);
    }

    protected function errorResponse(string $message, int $statusCode = 200): void
    {
        $response = [
            'status' => false,
            'message' => $message
        ];

        // Auto log error response
        if ($this->_apiLoggingContext && $this->_apiLoggingContext['auto_log']) {
            $this->db->logApiRequest(
                $this->_apiLoggingContext['api_path'],
                $this->_apiLoggingContext['method'],
                $this->_apiLoggingContext['request_data'],
                $response,
                $statusCode
            );
        }

        $this->jsonResponse($response, $statusCode);
    }

    /**
     * Manual logging method (for special cases)
     */
    protected function logApiRequest($response, int $statusCode = 200): void
    {
        if ($this->_apiLoggingContext) {
            $this->db->logApiRequest(
                $this->_apiLoggingContext['api_path'],
                $this->_apiLoggingContext['method'],
                $this->_apiLoggingContext['request_data'],
                $response,
                $statusCode
            );
        }
    }

    /**
     * Validate với auto error handling
     */
    protected function validateRequestSafe(array $rules, array $messages = []): array
    {
        try {
            $validator = $this->getValidatorInstance();
            if (!empty($messages)) {
                return $validator->validateWithMessages($rules, $messages);
            }
            return $validator->validate($rules);
        } catch (ValidationException $e) {
            // Throw để được caught bởi handleExceptions
            throw $e;
        }
    }
} 