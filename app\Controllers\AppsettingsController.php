<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Services\AppSettingsService;
use Exception;

class AppsettingsController extends Controller {

    private $appSettingsService;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->appSettingsService = AppSettingsService::getInstance($this->db);
    }

    public function view() {
        $this->requireLogin();

        // Check permission
        if (!$this->hasPermission('appsettings.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Get app settings using service
        $appSettings = $this->appSettingsService->getAll();

        // Load order config for packages
        $orderConfig = require __DIR__ . '/../../config/order.php';

        require_once __DIR__ . '/../../resources/views/appsettings/view.php';
    }

    public function edit() {
        $this->requireLogin();

        // Check permission
        if (!$this->hasPermission('appsettings.edit')) {
            $this->redirectToFirstAccessiblePage();
        }

        $id = $_GET['id'] ?? 0;

        // Get app settings
        $appSettings = $this->db->fetch("SELECT * FROM app_settings WHERE id = ?", [$id]);

        if (!$appSettings) {
            header('Location: /public/?route=appsettings&action=view');
            exit;
        }

        $errors = [];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get form data
            $appId = $_POST['app_id'] ?? '';
            $appSecret = $_POST['app_secret'] ?? '';
            $googleLogin = isset($_POST['google_login']) ? 1 : 0;
            $facebookLogin = isset($_POST['facebook_login']) ? 1 : 0;
            $appleLogin = isset($_POST['apple_login']) ? 1 : 0;
            $guestLogin = isset($_POST['guest_login']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            $isDebug = isset($_POST['is_debug']) ? 1 : 0;
            $phoneSync = isset($_POST['phone_sync']) ? 1 : 0;

            // Validate
            if (empty($appId) || !is_numeric($appId)) {
                $errors[] = "App ID không hợp lệ";
            }

            if (empty($appSecret)) {
                $errors[] = "App Secret không được để trống";
            }

            // Check if app_id already exists (except for current record)
            $existingApp = $this->db->fetch(
                "SELECT COUNT(*) as count FROM app_settings WHERE app_id = ? AND id != ?",
                [$appId, $id]
            );

            if ($existingApp['count'] > 0) {
                $errors[] = "App ID đã tồn tại";
            }

            if (empty($errors)) {
                try {
                    // Update app settings using service
                    $updateData = [
                        'app_id' => $appId,
                        'app_secret' => $appSecret,
                        'google_login' => $googleLogin,
                        'facebook_login' => $facebookLogin,
                        'apple_login' => $appleLogin,
                        'guest_login' => $guestLogin,
                        'is_active' => $isActive,
                        'is_debug' => $isDebug,
                        'phone_sync' => $phoneSync
                    ];

                    $this->appSettingsService->update($id, $updateData);

                    // Log
                    $this->db->log('update_app_settings', 'success', [
                        'id' => $id,
                        'app_id' => $appId,
                        'google_login' => $googleLogin,
                        'facebook_login' => $facebookLogin,
                        'apple_login' => $appleLogin,
                        'guest_login' => $guestLogin,
                        'is_active' => $isActive,
                        'is_debug' => $isDebug,
                        'phone_sync' => $phoneSync
                    ]);

                    header('Location: /public/?route=appsettings&action=view');
                    exit;
                } catch (Exception $e) {
                    $errors[] = "Lỗi khi cập nhật: " . $e->getMessage();
                }
            }
        }

        require_once __DIR__ . '/../../resources/views/appsettings/edit.php';
    }

    public function create() {
        $this->requireLogin();

        // Check permission
        if (!$this->hasPermission('appsettings.edit')) {
            $this->redirectToFirstAccessiblePage();
        }

        $errors = [];

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // Get form data
            $appId = $_POST['app_id'] ?? '';
            $appSecret = $_POST['app_secret'] ?? '';
            $googleLogin = isset($_POST['google_login']) ? 1 : 0;
            $facebookLogin = isset($_POST['facebook_login']) ? 1 : 0;
            $appleLogin = isset($_POST['apple_login']) ? 1 : 0;
            $guestLogin = isset($_POST['guest_login']) ? 1 : 0;
            $isActive = isset($_POST['is_active']) ? 1 : 0;
            $isDebug = isset($_POST['is_debug']) ? 1 : 0;
            $phoneSync = isset($_POST['phone_sync']) ? 1 : 0;

            // Validate
            if (empty($appId) || !is_numeric($appId)) {
                $errors[] = "App ID không hợp lệ";
            }

            if (empty($appSecret)) {
                $errors[] = "App Secret không được để trống";
            }

            // Check if app_id already exists
            $existingApp = $this->db->fetch(
                "SELECT COUNT(*) as count FROM app_settings WHERE app_id = ?",
                [$appId]
            );

            if ($existingApp['count'] > 0) {
                $errors[] = "App ID đã tồn tại";
            }

            if (empty($errors)) {
                try {
                    // Insert new app settings using service
                    $createData = [
                        'app_id' => $appId,
                        'app_secret' => $appSecret,
                        'google_login' => $googleLogin,
                        'facebook_login' => $facebookLogin,
                        'apple_login' => $appleLogin,
                        'guest_login' => $guestLogin,
                        'is_active' => $isActive,
                        'is_debug' => $isDebug,
                        'phone_sync' => $phoneSync
                    ];

                    $id = $this->appSettingsService->create($createData);

                    // Log
                    $this->db->log('create_app_settings', 'success', [
                        'id' => $id,
                        'app_id' => $appId,
                        'google_login' => $googleLogin,
                        'facebook_login' => $facebookLogin,
                        'apple_login' => $appleLogin,
                        'guest_login' => $guestLogin,
                        'is_active' => $isActive,
                        'is_debug' => $isDebug,
                        'phone_sync' => $phoneSync
                    ]);

                    header('Location: /public/?route=appsettings&action=view');
                    exit;
                } catch (Exception $e) {
                    $errors[] = "Lỗi khi tạo mới: " . $e->getMessage();
                }
            }
        }

        require_once __DIR__ . '/../../resources/views/appsettings/create.php';
    }

    public function delete() {
        $this->requireLogin();

        // Check permission
        if (!$this->hasPermission('appsettings.edit')) {
            $this->redirectToFirstAccessiblePage();
        }

        $id = $_GET['id'] ?? 0;

        try {
            // Get app settings before deletion
            $appSettings = $this->db->fetch("SELECT * FROM app_settings WHERE id = ?", [$id]);

            if (!$appSettings) {
                header('Location: /public/?route=appsettings&action=view');
                exit;
            }

            // Delete app settings using service
            $this->appSettingsService->delete($id);

            // Log
            $this->db->log('delete_app_settings', 'success', [
                'id' => $id,
                'app_id' => $appSettings['app_id']
            ]);

            header('Location: /public/?route=appsettings&action=view');
            exit;
        } catch (Exception $e) {
            die("Lỗi khi xóa cài đặt: " . $e->getMessage());
        }
    }

    /**
     * Cache management endpoint
     */
    public function cache() {
        $this->requireLogin();

        // Check permission
        if (!$this->hasPermission('appsettings.edit')) {
            $this->redirectToFirstAccessiblePage();
        }

        $action = $_GET['cache_action'] ?? '';

        if ($action === 'clear') {
            $this->appSettingsService->clearAllCache();
            echo json_encode(['success' => true, 'message' => 'Cache đã được xóa']);
            exit;
        }

        if ($action === 'stats') {
            $stats = $this->appSettingsService->getCacheStats();
            echo json_encode($stats);
            exit;
        }

        echo json_encode(['error' => 'Action không hợp lệ']);
        exit;
    }
}
