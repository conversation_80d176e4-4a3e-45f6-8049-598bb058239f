; PHP Configuration
memory_limit = 512M
upload_max_filesize = 40M
post_max_size = 40M
max_execution_time = 600
max_input_time = 600
max_input_vars = 3000
date.timezone = Asia/Ho_Chi_Minh

; Error Reporting
error_reporting = E_ALL
display_errors = On
display_startup_errors = On
log_errors = On
error_log = /var/log/php/error.log

; Session
session.gc_maxlifetime = 1440
session.save_path = "/tmp"

; OpCache
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1
opcache.enable_cli = 1

; Xdebug (uncomment if needed)
; zend_extension=xdebug.so
; xdebug.mode=debug
; xdebug.start_with_request=yes
; xdebug.client_host=host.docker.internal
; xdebug.client_port=9003 