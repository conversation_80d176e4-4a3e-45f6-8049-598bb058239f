<?php
namespace App\Controllers;

use App\Core\Controller;
use Exception;

class ConfigController extends Controller {
    private $typeOptions = [
        'text' => 'Text',
        'boolean' => 'Boolean',
        'number' => 'Number',
        'json' => 'JSON',
    ];

    public function index() {
        $this->requireLogin();
        if (!$this->hasPermission('config.view')) {
            $this->redirectToFirstAccessiblePage();
        }
        $configs = $this->db->fetchAll("SELECT * FROM config ORDER BY id DESC");
        $typeOptions = $this->typeOptions;
        require_once __DIR__ . '/../../resources/views/config/index.php';
    }

    public function create() {
        $this->requireLogin();
        if (!$this->hasPermission('config.create')) {
            $this->redirectToFirstAccessiblePage();
        }
        $errors = [];
        $typeOptions = $this->typeOptions;
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $config_key = trim($_POST['config_key'] ?? '');
            $config_value = trim($_POST['config_value'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $type = $_POST['type'] ?? 'text';
            if (empty($config_key)) $errors[] = 'Key không được để trống';
            if (!isset($_POST['config_value']) || $_POST['config_value'] === '') $errors[] = 'Value không được để trống';
            if (empty($type)) $errors[] = 'Type không được để trống';
            if (empty($errors)) {
                try {
                    $insertId = $this->db->insert('config', [
                        'config_key' => $config_key,
                        'config_value' => $config_value,
                        'description' => $description,
                        'type' => $type
                    ]);
                    $this->db->log('config_create', 'success', [
                        'config_id' => $insertId,
                        'config_key' => $config_key,
                        'config_value' => $config_value,
                        'type' => $type
                    ]);
                    $_SESSION['success'] = 'Thêm cấu hình thành công';
                    header('Location: /public/?route=config');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
                }
            }
        }
        require_once __DIR__ . '/../../resources/views/config/create.php';
    }

    public function edit() {
        $this->requireLogin();
        if (!$this->hasPermission('config.edit')) {
            $this->redirectToFirstAccessiblePage();
        }
        $id = $_GET['id'] ?? 0;
        $config = $this->db->fetch("SELECT * FROM config WHERE id = ?", [$id]);
        if (!$config) {
            $_SESSION['error'] = 'Cấu hình không tồn tại';
            header('Location: /public/?route=config');
            exit;
        }
        $errors = [];
        $typeOptions = $this->typeOptions;
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $config_key = trim($_POST['config_key'] ?? '');
            $config_value = trim($_POST['config_value'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $type = $_POST['type'] ?? 'text';
            if (empty($config_key)) $errors[] = 'Key không được để trống';
            if (!isset($_POST['config_value']) || $_POST['config_value'] === '') $errors[] = 'Value không được để trống';
            if (empty($type)) $errors[] = 'Type không được để trống';
            if (empty($errors)) {
                try {
                    $this->db->update('config', [
                        'config_key' => $config_key,
                        'config_value' => $config_value,
                        'description' => $description,
                        'type' => $type
                    ], 'id = ?', [$id]);
                    $this->db->log('config_edit', 'success', [
                        'config_id' => $id,
                        'config_key' => $config_key,
                        'config_value' => $config_value,
                        'type' => $type
                    ]);
                    $_SESSION['success'] = 'Cập nhật cấu hình thành công';
                    header('Location: /public/?route=config');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
                }
            }
        }
        require_once __DIR__ . '/../../resources/views/config/edit.php';
    }

    public function delete() {
        $this->requireLogin();
        if (!$this->hasPermission('config.delete')) {
            $this->redirectToFirstAccessiblePage();
        }
        $id = $_GET['id'] ?? 0;
        $config = $this->db->fetch("SELECT * FROM config WHERE id = ?", [$id]);
        if (!$config) {
            $_SESSION['error'] = 'Cấu hình không tồn tại';
            header('Location: /public/?route=config');
            exit;
        }
        $this->db->delete('config', 'id = ?', [$id]);
        $this->db->log('config_delete', 'success', [
            'config_id' => $id,
            'config_key' => $config['config_key'],
            'config_value' => $config['config_value'],
            'type' => $config['type']
        ]);
        $_SESSION['success'] = 'Xóa cấu hình thành công';
        header('Location: /public/?route=config');
        exit;
    }

    public function sendServerStatus() {
        $this->requireLogin();
        
        if (!$this->hasPermission('config.edit')) {
            return $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền thực hiện thao tác này']);
        }

        $content = $_POST['content'] ?? '';
        if (empty($content)) {
            return $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập nội dung thông báo']);
        }

        // Kiểm tra độ dài base64
        $encodedContent = base64_encode($content);
        if (strlen($encodedContent) > 256) {
            return $this->jsonResponse(['status' => false, 'message' => 'Nội dung sau khi mã hóa vượt quá 256 ký tự']);
        }

        try {
            // Lấy giá trị disconnect từ config
            $config = $this->db->fetch("SELECT * FROM config WHERE config_key = 'server_status'");
            if (!$config) {
                throw new Exception("Không tìm thấy cấu hình server_status");
            }

            // Chuyển đổi value thành boolean
            $disconnect = $config['config_value'] == '1' ? 'true' : 'false';

            // Lấy danh sách tất cả server
            $servers = $this->db->getServers(true);
            $serverIds = array_column($servers, 'id');

            // Kết nối đến tất cả server
            $connectResults = $this->gameDatabaseManager->connectServers($serverIds);
            if (!$this->checkGameDbConnections($connectResults)) {
                throw new Exception("Không thể kết nối đến các máy chủ");
            }

            $results = [];
            foreach ($serverIds as $serverId) {
                $command = [
                    'creator' => 'ConfigController',
                    'createtime' => time(),
                    'type' => 2,
                    'cmd' => "maintenance {$disconnect} {$encodedContent}"
                ];

                $insertSql = $this->gameSqlBuilder->buildInsert('command', $command);
                $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertSql['sql'], $insertSql['params']);

                $results[] = [
                    'server_id' => $serverId,
                    'status' => $result['success'],
                    'message' => $result['success'] ? 'Gửi tình trạng thành công' : 'Gửi tình trạng thất bại',
                    'error' => $result['error'] ?? null
                ];
            }

            // Log action
            $this->db->log('config_send_server_status', 'success', [
                'content' => $content,
                'disconnect' => $disconnect,
                'results' => $results
            ]);

            return $this->jsonResponse([
                'status' => true,
                'message' => 'Gửi tình trạng thành công',
                'results' => $results
            ]);

        } catch (Exception $e) {
            $this->db->log('config_send_server_status', 'error', [
                'error' => $e->getMessage()
            ]);
            return $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            $this->gameDatabaseManager->closeAll();
        }
    }
} 