<?php

namespace App\Services;

class TelegramService {
    private $botToken;
    private $chatId;

    private $telegramOn;

    private $init = false;

    public function __construct() {
        $this->botToken = getenv('TELEGRAM_BOT_TOKEN');
        $this->chatId = getenv('TELEGRAM_CHAT_ID');
        $this->telegramOn = getenv('TELEGRAM_ON');
        
        // Kiểm tra nếu bot không được bật hoặc thiếu thông tin cấu hình
        if ($this->telegramOn !== 'true' || empty($this->botToken) || empty($this->chatId)) {
            $this->init = false;
            return;
        }

        $this->init = true;
    }

    public function sendMessage($message) {
        if (!$this->init) {
            return false;
        }
        $url = "https://api.telegram.org/bot{$this->botToken}/sendMessage";
        
        $appName = getenv('APP_NAME') ?: 'GMTool';
        $message = "<b>🔔 {$appName}</b>\n\n" . $message;
        
        $data = [
            'chat_id' => $this->chatId,
            'text' => $message,
            'parse_mode' => 'HTML'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            return false;
        }

        return true;
    }
} 