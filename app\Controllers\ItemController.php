<?php

namespace App\Controllers;

use App\Core\Controller;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ItemController extends Controller
{
    private $itemConfig;
    protected $allItems = [];
    protected $categories = [];

    public function __construct($db, $config)
    {
        parent::__construct($db, $config);
        $this->itemConfig = require __DIR__ . '/../../config/item.php';
        $this->initItems();
    }


    public function index()
    {
        $this->requireLogin();
        if (!$this->hasPermission('item.view')) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này.';
            $this->redirectToFirstAccessiblePage();
        }

        // Nếu có action export thì xuất excel
        if (isset($_GET['action']) && $_GET['action'] === 'export') {
            $this->exportExcel();
            exit;
        }

        $search = trim($_GET['search'] ?? '');
        $category = trim($_GET['category'] ?? '');
        $page = (int)($_GET['page'] ?? 1);
        $perPage = $this->itemConfig['pagination']['per_page'];
        $offset = ($page - 1) * $perPage;

        // Lọc dữ liệu theo category
        $filteredItems = $this->allItems;
        if ($category && isset($this->categories[$category])) {
            $filteredItems = array_filter($filteredItems, function ($item) use ($category) {
                return $item['category_key'] === $category;
            });
        }

        // Lọc dữ liệu theo tìm kiếm
        if (!empty($search) && strlen($search) >= $this->itemConfig['search']['min_length']) {
            $filteredItems = array_filter($filteredItems, function ($item) use ($search) {
                $searchLower = mb_strtolower($search);
                $nameLower = mb_strtolower($item['name'] ?? '');
                $idMatch = strpos((string)$item['id'], $search) !== false;
                $nameMatch = strpos($nameLower, $searchLower) !== false;
                return $idMatch || $nameMatch;
            });
        }

        $total = count($filteredItems);
        $items = array_slice($filteredItems, $offset, $perPage);
        $totalPages = ceil($total / $perPage);

        $pagination = [
            'current' => $page,
            'total' => $totalPages,
            'perPage' => $perPage,
            'max_links' => $this->itemConfig['pagination']['max_links'],
            'total_items' => $total,
            'offset' => $offset
        ];
        
        $appClientDomain = getenv('APP_CLIENT_DOMAIN') ?: '';
        $itemConfig = $this->itemConfig;
        $categories = $this->categories;

        require_once __DIR__ . '/../../resources/views/item/index.php';
    }

    public function utility()
    {
        $this->requireLogin();
        if (!$this->hasPermission('item.utility')) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này.';
            $this->redirectToFirstAccessiblePage();
        }

        $allItems = $this->allItems;
        require_once __DIR__ . '/../../resources/views/item/utility.php';
    }

    // Thêm hàm xuất excel
    private function exportExcel()
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // Header
        $sheet->fromArray([
            ['ID', 'Tên', 'Loại', 'Mô tả', 'Icon ID', 'Resource']
        ], NULL, 'A1');
        $row = 2;
        foreach ($this->allItems as $item) {
            $sheet->setCellValue('A' . $row, $item['id'] ?? '')
                  ->setCellValue('B' . $row, $item['name'] ?? '')
                  ->setCellValue('C' . $row, $item['category_name'] ?? '')
                  ->setCellValue('D' . $row, $item['description'] ?? '')
                  ->setCellValue('E' . $row, $item['icon_id'] ?? '')
                  ->setCellValue('F' . $row, $item['res'] ?? '');
            $row++;
        }
        // Xuất file
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="items.xlsx"');
        header('Cache-Control: max-age=0');
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    public function apiGetByIds()
    {
        $this->requireLogin();
        header('Content-Type: application/json');

        // Lấy danh sách id từ POST
        $ids = $_POST['ids'] ?? '';
        if (empty($ids)) {
            echo json_encode([
                'status' => false,
                'message' => 'Vui lòng nhập danh sách ID hợp lệ'
            ]);
            exit;
        }

        $result = [];
        $notFoundIds = [];
        
        // Xử lý từng cặp id:số lượng
        foreach (explode(',', $ids) as $pair) {
            $pair = trim($pair);
            if (empty($pair)) continue;
            
            list($id, $amount) = explode(':', $pair) + [null, null];
            $id = trim($id);
            $amount = (int)trim($amount);
            
            if (!is_numeric($id) || $amount <= 0) {
                echo json_encode([
                    'status' => false,
                    'message' => 'Định dạng không hợp lệ: ' . $pair
                ]);
                exit;
            }
            
            if (isset($this->allItems[$id])) {
                $itemData = $this->allItems[$id];
                $itemData['amount'] = $amount;
                $result[] = $itemData;
            } else {
                $notFoundIds[] = $id;
            }
        }

        if (!empty($notFoundIds)) {
            echo json_encode([
                'status' => false,
                'message' => 'Không tìm thấy các item có ID: ' . implode(', ', $notFoundIds)
            ]);
            exit;
        }

        echo json_encode([
            'status' => true,
            'ids' => $ids,
            'data' => $result
        ]);
        exit;
    }

    public function apiConvertIdsToNames()
    {
        $this->requireLogin();
        header('Content-Type: application/json');

        $input = $_POST['input'] ?? '';
        if (empty($input)) {
            echo json_encode([
                'status' => false,
                'message' => 'Vui lòng nhập dữ liệu'
            ]);
            exit;
        }

        $lines = explode("\n", $input);
        $result = [];

        foreach ($lines as $line) {
            if (trim($line) === '') {
                $result[] = '';
                continue;
            }

            $items = explode(',', $line);
            $lineResult = [];

            foreach ($items as $item) {
                list($id, $amount) = explode(':', $item) + [null, null];
                $id = trim($id);
                $amount = trim($amount);

                if (isset($this->allItems[$id])) {
                    $lineResult[] = $this->allItems[$id]['name'] . ' *' . $amount;
                } else {
                    $lineResult[] = '[Không tìm thấy item ID: ' . $id . '] *' . $amount;
                }
            }

            $result[] = implode('|', $lineResult);
        }

        echo json_encode([
            'status' => true,
            'result' => implode("\n", $result)
        ]);
        exit;
    }

    public function apiConvertNamesToIds()
    {
        $this->requireLogin();
        header('Content-Type: application/json');

        $input = $_POST['input'] ?? '';
        if (empty($input)) {
            echo json_encode([
                'status' => false,
                'message' => 'Vui lòng nhập dữ liệu'
            ]);
            exit;
        }

        $lines = explode("\n", $input);
        $result = [];

        foreach ($lines as $line) {
            if (trim($line) === '') {
                $result[] = '';
                continue;
            }

            $items = explode('|', $line);
            $lineResult = [];

            foreach ($items as $item) {
                list($name, $amount) = explode('*', $item) + [null, null];
                $name = trim($name);
                $amount = trim($amount);

                $foundId = null;
                foreach ($this->allItems as $id => $itemData) {
                    if ($itemData['name'] === $name) {
                        $foundId = $id;
                        break;
                    }
                }

                if ($foundId !== null) {
                    $lineResult[] = $foundId . ':' . $amount;
                } else {
                    $lineResult[] = '[Không tìm thấy item: ' . $name . ']:' . $amount;
                }
            }

            $result[] = implode(',', $lineResult);
        }

        echo json_encode([
            'status' => true,
            'result' => implode("\n", $result)
        ]);
        exit;
    }

    public function apiMergeItemIds()
    {
        $this->requireLogin();
        header('Content-Type: application/json');

        $input = $_POST['input'] ?? '';
        if (empty($input)) {
            echo json_encode([
                'status' => false,
                'message' => 'Vui lòng nhập dữ liệu'
            ]);
            exit;
        }

        $lines = explode("\n", $input);
        $mergedItems = [];

        foreach ($lines as $line) {
            if (trim($line) === '') continue;
            
            // Kiểm tra định dạng input
            if (strpos($line, '|') !== false) {
                // Định dạng: Tên *Số lượng|Tên *Số lượng
                $items = explode('|', $line);
                foreach ($items as $item) {
                    list($name, $amount) = explode('*', $item) + [null, null];
                    $name = trim($name);
                    $amount = (int)trim($amount);

                    if (!isset($mergedItems[$name])) {
                        $mergedItems[$name] = 0;
                    }
                    $mergedItems[$name] += $amount;
                }
            } else {
                // Định dạng: Tên:Số lượng,Tên:Số lượng
                $items = explode(',', $line);
                foreach ($items as $item) {
                    list($name, $amount) = explode(':', $item) + [null, null];
                    $name = trim($name);
                    $amount = (int)trim($amount);

                    if (!isset($mergedItems[$name])) {
                        $mergedItems[$name] = 0;
                    }
                    $mergedItems[$name] += $amount;
                }
            }
        }

        // Sắp xếp theo tên
        ksort($mergedItems);

        // Tạo kết quả
        $result = [];
        foreach ($mergedItems as $name => $amount) {
            $result[] = $name . ':' . $amount;
        }

        echo json_encode([
            'status' => true,
            'result' => implode(',', $result)
        ]);
        exit;
    }

    public function apiCalculateItemIds()
    {
        $this->requireLogin();
        header('Content-Type: application/json');

        $itemsToChange = $_POST['items_to_change'] ?? '';
        $operator = $_POST['operator'] ?? '';
        $value = $_POST['value'] ?? '';
        $listItems = $_POST['list_items'] ?? '';

        if (empty($itemsToChange) || empty($operator) || empty($value) || empty($listItems)) {
            echo json_encode([
                'status' => false,
                'message' => 'Vui lòng nhập đầy đủ thông tin'
            ]);
            exit;
        }

        // Xử lý danh sách items
        $items = [];
        $lines = explode("\n", $listItems);
        foreach ($lines as $line) {
            if (trim($line) === '') continue;
            
            $pairs = explode(',', $line);
            foreach ($pairs as $pair) {
                list($id, $amount) = explode(':', $pair) + [null, null];
                $id = trim($id);
                $amount = (int)trim($amount);

                if (!isset($items[$id])) {
                    $items[$id] = 0;
                }
                $items[$id] += $amount;
            }
        }

        // Xử lý items cần thay đổi
        $itemsToChange = trim($itemsToChange);
        $value = (int)$value;
        $result = [];

        foreach ($items as $id => $amount) {
            if ($itemsToChange === '*' || $itemsToChange === $id) {
                switch ($operator) {
                    case '+':
                        $amount += $value;
                        break;
                    case '-':
                        $amount -= $value;
                        break;
                    case '*':
                        $amount *= $value;
                        break;
                    case '/':
                        if ($value != 0) {
                            $amount = (int)($amount / $value);
                        }
                        break;
                }
            }
            $result[] = $id . ':' . $amount;
        }

        // Sắp xếp theo ID
        sort($result);

        echo json_encode([
            'status' => true,
            'result' => implode(',', $result)
        ]);
        exit;
    }

} 