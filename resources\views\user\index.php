<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<!-- User Cache Management Section -->
<?php if ($this->hasPermission('user.view.edit')): ?>
<div class="bg-green-50 border border-green-200 rounded-lg mb-4">
    <div class="px-4 py-3">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-users text-green-600 mr-2"></i>
                <h4 class="text-sm font-medium text-green-800">Quản lý <PERSON>ache Người dùng</h4>
            </div>
            <div class="flex space-x-2">
                <button id="refreshUserCacheStats" 
                        class="inline-flex items-center px-3 py-1 border border-green-300 text-xs font-medium rounded text-green-700 bg-white hover:bg-green-50">
                    <i class="fas fa-sync-alt mr-1"></i>Refresh Stats
                </button>
                <button id="clearUserCache" 
                        class="inline-flex items-center px-3 py-1 border border-red-300 text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50">
                    <i class="fas fa-trash mr-1"></i>Xóa Cache
                </button>
            </div>
        </div>
        
        <!-- User Cache Stats Display -->
        <div id="userCacheStats" class="mt-3 grid grid-cols-2 md:grid-cols-5 gap-3">
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Redis Status</div>
                <div id="userRedisStatus" class="text-sm font-medium">
                    <span class="text-gray-400">Đang tải...</span>
                </div>
            </div>
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">User Data</div>
                <div id="userCachedKeys" class="text-sm font-medium">
                    <span class="text-gray-400">-</span>
                </div>
            </div>
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Token Keys</div>
                <div id="userTokenKeys" class="text-sm font-medium">
                    <span class="text-gray-400">-</span>
                </div>
            </div>
            <div class="bg-white rounded p-2 border">
                <div class="text-xs text-gray-500">Last Updated</div>
                <div id="userLastUpdated" class="text-sm font-medium">
                    <span class="text-gray-400">-</span>
                </div>
            </div>
        </div>
        
        <!-- User Cache Details -->
        <div id="userCacheDetails" class="mt-3 hidden">
            <div class="bg-white border rounded-lg">
                <div class="px-3 py-2 border-b bg-gray-50 text-sm font-medium text-gray-700">
                    Chi tiết Cache Keys Người dùng
                </div>
                <div class="p-3 text-sm">
                    <div class="space-y-3" id="cacheDetailItems"></div>
                </div>
            </div>
        </div>
        
        <!-- Toggle buttons -->
        <div class="mt-2 text-right space-x-2">
            <button id="toggleUserCacheDetails" 
                    class="text-xs text-green-600 hover:text-green-800 hidden">
                <i id="toggleUserIcon" class="fas fa-chevron-down mr-1"></i>
                <span id="toggleUserText">Chi tiết Cache</span>
            </button>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Danh sách người dùng
            </h3>
            <form method="GET" action="/public/?route=user" class="flex items-center space-x-2">
                <input type="hidden" name="route" value="user">
                <div class="relative">
                    <input type="text" 
                           name="search" 
                           value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>" 
                           placeholder="Tìm kiếm..." 
                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                           minlength="<?php echo $this->userConfig['search']['min_length']; ?>">
                </div>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-search mr-2"></i>Tìm kiếm
                </button>
            </form>
        </div>
    </div>
    <div class="border-t border-gray-200">
        <div class="p-4">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User ID</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tên người dùng</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">GM</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Nguồn đăng ký</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Đăng nhập cuối</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Trạng thái</th>
                            <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="9" class="px-4 sm:px-6 py-4 text-center text-sm text-gray-500">
                                Không tìm thấy người dùng nào
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $user['id']; ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($user['user_id']); ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($user['username']); ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo htmlspecialchars($user['email']); ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php if ($user['is_gm']): ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            GM
                                        </span>
                                    <?php else: ?>
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                            User
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo htmlspecialchars($user['register_source']); ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'Chưa đăng nhập'; ?>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $this->userConfig['display']['status'][$user['status']]['class']; ?>">
                                        <?=$this->userConfig['display']['status'][$user['status']]['text']; ?>
                                    </span>
                                </td>
                                <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex space-x-2">
                                        <?php if ($this->hasPermission('user.view.edit')): ?>
                                            <a href="/public/?route=user&action=edit&id=<?php echo $user['id']; ?>" 
                                               class="text-indigo-600 hover:text-indigo-900">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ($this->hasPermission('user.view.block')): ?>
                                            <?php if ($user['status']): ?>
                                                <form method="POST" action="/public/?route=user&action=block&id=<?php echo $user['id']; ?>" class="inline">
                                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                    <button type="submit" 
                                                            class="text-yellow-600 hover:text-yellow-900"
                                                            onclick="return confirm('Bạn có chắc chắn muốn khóa người dùng này?')">
                                                        <i class="fas fa-lock"></i>
                                                    </button>
                                                </form>
                                            <?php else: ?>
                                                <form method="POST" action="/public/?route=user&action=unblock&id=<?php echo $user['id']; ?>" class="inline">
                                                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                                                    <button type="submit" 
                                                            class="text-green-600 hover:text-green-900"
                                                            onclick="return confirm('Bạn có chắc chắn muốn mở khóa người dùng này?')">
                                                        <i class="fas fa-unlock"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <?php 
                                        if ($this->hasPermission('user.view.game')) {
                                            $sign = $this->generateSign($user['user_id']);
                                            $path = getenv('APP_CLIENT_DOMAIN') . "/index.php?user_id=" . $user['user_id'] . "&sign=" . $sign . "&type=web&debug=true&api=". getenv('API_URL');
                                        ?>
                                            <a href="<?php echo $path; ?>" 
                                               class="text-blue-600 hover:text-blue-900"
                                               target="_blank">
                                                <i class="fas fa-gamepad"></i>
                                            </a>
                                        <?php } ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($pagination['total'] > 1): ?>
            <div class="mt-4 flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Hiển thị <span class="font-medium"><?php echo ($offset + 1); ?></span> đến 
                    <span class="font-medium"><?php echo min($offset + $perPage, $total); ?></span> của 
                    <span class="font-medium"><?php echo $total; ?></span> kết quả
                </div>
                <div class="flex space-x-2">
                    <?php if ($pagination['current'] > 1): ?>
                        <a href="?route=user&page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                        <a href="?route=user&page=<?php echo ($pagination['current'] - 1); ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $pagination['current'] - floor($pagination['max_links'] / 2));
                    $end = min($pagination['total'], $start + $pagination['max_links'] - 1);
                    $start = max(1, $end - $pagination['max_links'] + 1);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <a href="?route=user&page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md <?php echo $i == $pagination['current'] ? 'bg-indigo-600 text-white' : 'text-gray-700 bg-white hover:bg-gray-50'; ?>">
                            <?php echo $i; ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($pagination['current'] < $pagination['total']): ?>
                        <a href="?route=user&page=<?php echo ($pagination['current'] + 1); ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="?route=user&page=<?php echo $pagination['total']; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- User Cache Management JavaScript -->
<?php if ($this->hasPermission('user.view.edit')): ?>
<script>
// User cache management functions
document.addEventListener('DOMContentLoaded', function() {
    
    // Load user cache stats on page load
    loadUserCacheStats();
    
    // Refresh user cache stats button
    document.getElementById('refreshUserCacheStats').addEventListener('click', function() {
        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Đang tải...';
        
        loadUserCacheStats().finally(() => {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-sync-alt mr-1"></i>Refresh Stats';
        });
    });
    
    // Clear user cache button
    document.getElementById('clearUserCache').addEventListener('click', function() {
        if (confirm('Bạn có chắc chắn muốn xóa tất cả cache người dùng?')) {
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Đang xóa...';
            
            clearUserCache().finally(() => {
                this.disabled = false;
                this.innerHTML = '<i class="fas fa-trash mr-1"></i>Xóa Cache';
            });
        }
    });
    
    // Toggle user cache details
    document.getElementById('toggleUserCacheDetails').addEventListener('click', function() {
        const details = document.getElementById('userCacheDetails');
        const icon = document.getElementById('toggleUserIcon');
        const text = document.getElementById('toggleUserText');
        
        if (details.classList.contains('hidden')) {
            details.classList.remove('hidden');
            icon.className = 'fas fa-chevron-up mr-1';
            text.textContent = 'Ẩn chi tiết';
        } else {
            details.classList.add('hidden');
            icon.className = 'fas fa-chevron-down mr-1';
            text.textContent = 'Hiển thị chi tiết';
        }
    });
});

// Load user cache statistics
function loadUserCacheStats() {
    return fetch('/public/?route=user&action=cache&cache_action=stats')
        .then(response => response.json())
        .then(data => {
            updateUserCacheStats(data);
        })
        .catch(error => {
            console.error('Error loading user cache stats:', error);
            showUserError('Lỗi khi tải thống kê cache người dùng');
        });
}

// Clear all user cache
function clearUserCache() {
    return fetch('/public/?route=user&action=cache&cache_action=clear')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showUserSuccess(data.message || 'Cache người dùng đã được xóa thành công');
                // Reload stats after clearing
                setTimeout(loadUserCacheStats, 500);
            } else {
                showUserError(data.error || 'Lỗi khi xóa cache người dùng');
            }
        })
        .catch(error => {
            console.error('Error clearing user cache:', error);
            showUserError('Lỗi khi xóa cache người dùng');
        });
}

// Update user cache stats display
function updateUserCacheStats(data) {
    const redisStatus = document.getElementById('userRedisStatus');
    const cachedKeys = document.getElementById('userCachedKeys');
    const tokenKeys = document.getElementById('userTokenKeys');
    const lastUpdated = document.getElementById('userLastUpdated');
    const toggleCacheBtn = document.getElementById('toggleUserCacheDetails');
    
    if (data.redis_available) {
        redisStatus.innerHTML = '<span class="text-green-600"><i class="fas fa-check-circle mr-1"></i>Kết nối</span>';
        
        // Hiển thị số lượng user cache
        const userCount = data.user_keys || 0;
        cachedKeys.innerHTML = `<span class="text-green-600">${userCount} users</span>`;
        
        // Hiển thị token keys
        tokenKeys.innerHTML = `<span class="text-green-600">${data.token_keys || 0} tokens</span>`;
        
        // Hiển thị thời gian cập nhật
        if (data.last_updated) {
            const cacheTime = new Date(data.last_updated * 1000);
            lastUpdated.innerHTML = `<span class="text-gray-600">${cacheTime.toLocaleString('vi-VN')}</span>`;
        }
        
        // Hiển thị chi tiết cache nếu có
        if (data.cache_details && Object.keys(data.cache_details).length > 0) {
            toggleCacheBtn.classList.remove('hidden');
            let detailsHTML = '';
            
            for (const [key, details] of Object.entries(data.cache_details)) {
                const cachedAt = new Date(details.cached_at * 1000);
                const expiresAt = new Date(details.expires_at * 1000);
                const now = new Date();
                const ttlSeconds = Math.floor((expiresAt - now) / 1000);
                
                let ttlDisplay = 'Đã hết hạn';
                if (ttlSeconds > 0) {
                    if (ttlSeconds < 60) {
                        ttlDisplay = `${ttlSeconds}s`;
                    } else if (ttlSeconds < 3600) {
                        ttlDisplay = `${Math.floor(ttlSeconds / 60)}m`;
                    } else {
                        ttlDisplay = `${Math.floor(ttlSeconds / 3600)}h ${Math.floor((ttlSeconds % 3600) / 60)}m`;
                    }
                }
                
                const userIdMatch = key.match(/user:(guest|account|google|facebook|apple)_([^:]+)/);
                const userId = userIdMatch ? userIdMatch[1] + '_' + userIdMatch[2] : key;
                
                // Get user mappings
                const userMappings = [];
                const mappingTypes = ['username', 'email', 'phone', 'id'];
                
                for (const type of mappingTypes) {
                    const mappings = data[`${type}_mappings`] || [];
                    for (const mapping of mappings) {
                        if (mapping.value === userId || mapping.value === userIdMatch?.[2]) {
                            userMappings.push({
                                type: type,
                                key: mapping.key
                            });
                        }
                    }
                }
                
                detailsHTML += `
                    <div class="bg-gray-50 rounded-lg overflow-hidden">
                        <div class="flex items-center justify-between p-3 border-b border-gray-200">
                            <div>
                                <div class="font-medium">${userId}</div>
                                <div class="text-xs text-gray-500">Cache: ${cachedAt.toLocaleString('vi-VN')}</div>
                            </div>
                            <div class="text-right">
                                <div class="text-sm ${ttlSeconds > 0 ? 'text-green-600' : 'text-red-600'}">${ttlDisplay}</div>
                                <div class="text-xs text-gray-500">Hết hạn: ${expiresAt.toLocaleString('vi-VN')}</div>
                            </div>
                        </div>`;
                
                // Add mappings if exist
                if (userMappings.length > 0) {
                    detailsHTML += `
                        <div class="p-2 bg-gray-100">
                            <div class="text-xs font-medium text-gray-500 mb-1">Mapping Keys:</div>
                            <div class="grid grid-cols-2 gap-2">`;
                    
                    for (const mapping of userMappings) {
                        detailsHTML += `
                                <div class="bg-white rounded p-1.5 text-xs">
                                    <span class="font-medium">${mapping.type}:</span>
                                    <span class="text-gray-600">${mapping.key}</span>
                                </div>`;
                    }
                    
                    detailsHTML += `
                            </div>
                        </div>`;
                }
                
                detailsHTML += `</div>`;
            }
            
            document.getElementById('cacheDetailItems').innerHTML = detailsHTML;
        } else {
            toggleCacheBtn.classList.add('hidden');
            document.getElementById('userCacheDetails').classList.add('hidden');
        }
    } else {
        redisStatus.innerHTML = '<span class="text-red-600"><i class="fas fa-times-circle mr-1"></i>Không kết nối</span>';
        cachedKeys.innerHTML = '<span class="text-gray-400">-</span>';
        tokenKeys.innerHTML = '<span class="text-gray-400">-</span>';
        lastUpdated.innerHTML = '<span class="text-gray-400">-</span>';
        toggleCacheBtn.classList.add('hidden');
        document.getElementById('userCacheDetails').classList.add('hidden');
    }
}

// Show success message
function showUserSuccess(message) {
    const toast = createUserToast(message, 'success');
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 3000);
}

// Show error message
function showUserError(message) {
    const toast = createUserToast(message, 'error');
    document.body.appendChild(toast);
    setTimeout(() => toast.remove(), 5000);
}

// Create toast notification
function createUserToast(message, type) {
    const toast = document.createElement('div');
    const bgColor = type === 'success' ? 'bg-green-500' : 'bg-red-500';
    const icon = type === 'success' ? 'fa-check' : 'fa-exclamation-triangle';
    
    toast.className = `fixed top-4 right-4 ${bgColor} text-white px-4 py-2 rounded-lg shadow-lg z-50 flex items-center space-x-2`;
    toast.innerHTML = `
        <i class="fas ${icon}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" class="ml-2 text-white hover:text-gray-200">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    return toast;
}
</script>
<?php endif; ?>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 