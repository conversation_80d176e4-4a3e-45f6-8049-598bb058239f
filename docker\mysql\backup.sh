#!/bin/bash

# L<PERSON><PERSON> thông tin từ biến môi trường
MYSQL_HOST="${MYSQL_HOST:-db}"
MYSQL_USER="${MYSQL_USER}"
MYSQL_PASSWORD="${MYSQL_PASSWORD}"
MYSQL_DATABASE="${MYSQL_DATABASE}"
BACKUP_DIR="/backup"
RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-7}"

# Tạo thư mục backup nếu chưa tồn tại
mkdir -p "${BACKUP_DIR}"

# Chỉ chạy backup vào 0h mỗi ngày
CURRENT_HOUR=$(date +%H)
if [ "$CURRENT_HOUR" != "00" ]; then
    echo "Not backup time. Current hour: $CURRENT_HOUR"
    exit 0
fi

# Tạo tên file backup
BACKUP_FILE="${BACKUP_DIR}/${MYSQL_DATABASE}_$(date +%Y%m%d_%H%M%S).sql.gz"

# Đợi MySQL sẵn sàng
while ! mysqladmin ping -h "${MYSQL_HOST}" -u "${MYSQL_USER}" -p"${MYSQL_PASSWORD}" --silent; do
    echo "Waiting for MySQL to be ready..."
    sleep 5
done

# Thực hiện backup
echo "Starting backup..."
mysqldump -h "${MYSQL_HOST}" -u "${MYSQL_USER}" -p"${MYSQL_PASSWORD}" "${MYSQL_DATABASE}" | gzip > "${BACKUP_FILE}"

# Kiểm tra kích thước file backup
if [ -s "${BACKUP_FILE}" ]; then
    echo "Backup completed successfully: ${BACKUP_FILE}"
else
    echo "Backup failed: Empty file"
    rm -f "${BACKUP_FILE}"
fi

# Xóa các file backup cũ
find "${BACKUP_DIR}" -name "${MYSQL_DATABASE}_*.sql.gz" -type f -mtime +${RETENTION_DAYS} -delete

# Log kết quả
echo "$(date): Backup completed - ${BACKUP_FILE}" >> "${BACKUP_DIR}/backup.log" 