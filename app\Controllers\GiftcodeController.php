<?php

namespace App\Controllers;

use App\Core\Controller;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Exception;

class GiftcodeController extends Controller
{
    private $giftcodeConfig;

    public function __construct($db, $config)
    {
        parent::__construct($db, $config);
        $this->giftcodeConfig = require __DIR__ . '/../../config/giftcode.php';
    }

    // Danh sách nhóm code
    public function index()
    {
        $this->requireLogin();
        if (!$this->hasPermission('giftcode.view')) {
            $_SESSION['error'] = 'Bạn không có quyền truy cập trang này.';
            $this->redirectToFirstAccessiblePage();
        }

        $search = trim($_GET['search'] ?? '');
        $page = (int)($_GET['page'] ?? 1);
        $perPage = $this->giftcodeConfig['pagination']['per_page'];
        $offset = ($page - 1) * $perPage;

        // Tìm kiếm
        $where = '1';
        $params = [];
        if ($search && strlen($search) >= $this->giftcodeConfig['search']['min_length']) {
            // Tìm theo code trong bảng gift_codes
            $codeSearch = $this->db->fetchAll(
                "SELECT DISTINCT group_id FROM gift_codes WHERE code LIKE ?",
                ['%' . $search . '%']
            );
            
            if (!empty($codeSearch)) {
                $groupIds = array_column($codeSearch, 'group_id');
                $where .= " AND (id IN (" . implode(',', array_fill(0, count($groupIds), '?')) . ")";
                $params = array_merge($params, $groupIds);
            } else {
                $where .= " AND (1=1";
            }
            
            // Tìm theo tên nhóm và code prefix
            $where .= " OR group_name LIKE ? OR code_prefix LIKE ? OR code_value LIKE ?)";
            $params[] = '%' . $search . '%';
            $params[] = '%' . $search . '%';
            $params[] = '%' . $search . '%';
        }

        // Đếm tổng
        $total = $this->db->fetchValue("SELECT COUNT(*) FROM gift_code_groups WHERE $where", $params);

        // Lấy danh sách nhóm code
        $groups = $this->db->fetchAll("SELECT * FROM gift_code_groups WHERE $where ORDER BY id ASC LIMIT $offset, $perPage", $params);

        // Lấy số code và đã dùng cho từng nhóm
        foreach ($groups as $key => $group) {
            if ($group['type'] === 'single') {
                $groups[$key]['total_code'] = $this->db->fetchValue("SELECT COUNT(*) FROM gift_codes WHERE group_id = ?", [$group['id']]);
                $groups[$key]['used_code'] = $this->db->fetchValue("SELECT COUNT(*) FROM gift_codes WHERE group_id = ? AND is_used = 1", [$group['id']]);
            } else {
                $groups[$key]['total_code'] = 1;
                $groups[$key]['used_code'] = $this->db->fetchValue("SELECT COUNT(*) FROM gift_code_claims WHERE group_id = ?", [$group['id']]);
            }
        }
        

        $totalPages = ceil($total / $perPage);
        $pagination = [
            'current' => $page,
            'total' => $totalPages,
            'perPage' => $perPage,
            'max_links' => $this->giftcodeConfig['pagination']['max_links'],
            'total_items' => $total,
            'offset' => $offset
        ];

        require __DIR__ . '/../../resources/views/giftcode/index.php';
    }

    // Tạo nhóm code
    public function create()
    {
        $this->requireLogin();
        if (!$this->hasPermission('giftcode.create')) {
            $_SESSION['error'] = 'Bạn không có quyền tạo nhóm code.';
            $this->redirectToFirstAccessiblePage();
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $group_name = trim($_POST['group_name'] ?? '');
            $type = $_POST['type'] ?? 'single';
            $items = trim($_POST['items'] ?? '');
            $created_by = $_SESSION['user_id'] ?? 1;

            if (!$this->isValidItemList($items)) {
                $_SESSION['error'] = 'Danh sách item không hợp lệ!';
                header('Location: ?route=giftcode&action=create');
                exit;
            }

            // Xử lý thời hạn sử dụng
            $expired_at = null;
            if ($_POST['expired_type'] === 'limited' && !empty($_POST['expired_at'])) {
                $expired_at = date('Y-m-d H:i:s', strtotime($_POST['expired_at']));
            }

            $data = [
                'group_name' => $group_name,
                'type' => $type,
                'items' => $items,
                'created_by' => $created_by,
                'expired_at' => $expired_at
            ];

            if ($type === 'single') {
                $amount = max(1, (int)($_POST['amount'] ?? 1));
                $code_prefix = trim($_POST['code_prefix'] ?? '');
                $data['code_prefix'] = $code_prefix;
                $group_id = $this->db->insert('gift_code_groups', $data);

                // Tối ưu tạo code
                $batchSize = 1000; // Số lượng code mỗi lần insert
                $totalBatches = ceil($amount / $batchSize);
                $usedCodes = []; // Lưu trữ các code đã tạo

                for ($batch = 0; $batch < $totalBatches; $batch++) {
                    $currentBatchSize = min($batchSize, $amount - ($batch * $batchSize));
                    $batchCodes = [];
                    
                    // Tạo code nhanh hơn và đảm bảo không trùng
                    for ($i = 0; $i < $currentBatchSize; $i++) {
                        do {
                            $code = strtoupper($code_prefix . substr(bin2hex(random_bytes(5)), 0, 10));
                        } while (isset($usedCodes[$code]));
                        
                        $usedCodes[$code] = true;
                        $batchCodes[] = [
                            'group_id' => $group_id,
                            'code' => $code,
                        ];
                    }

                    // Batch insert
                    if (!empty($batchCodes)) {
                        $this->db->batchInsert('gift_codes', $batchCodes);
                    }
                }
            } else { // common
                $code_value = trim($_POST['code_value'] ?? '');
                $max_use = max(1, (int)($_POST['max_use'] ?? 1));
                $data['code_value'] = $code_value;
                $data['max_use'] = $max_use;
                $group_id = $this->db->insert('gift_code_groups', $data);
            }

            // Lưu log
            $this->db->log('giftcode.create', 'success', [
                'group_id' => $group_id,
                'data' => $data,
                'created_by' => $created_by
            ]);

            $_SESSION['success'] = 'Tạo nhóm code thành công!';
            header('Location: ?route=giftcode');
            exit;
        }
        $appClientDomain = getenv('APP_CLIENT_DOMAIN') ?: '';
        require_once __DIR__ . '/../../resources/views/giftcode/create.php';
    }

    // Xuất code ra excel
    public function export()
    {
        $this->requireLogin();
        if (!$this->hasPermission('giftcode.export')) {
            $_SESSION['error'] = 'Bạn không có quyền xuất code.';
            $this->redirectToFirstAccessiblePage();
        }

        $group_id = (int)($_GET['id'] ?? 0);
        $group = $this->db->fetch("SELECT * FROM gift_code_groups WHERE id = ?", [$group_id]);
        if (!$group) {
            $_SESSION['error'] = 'Không tìm thấy nhóm code!';
            header('Location: ?route=giftcode');
            exit;
        }

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        if ($group['type'] === 'single') {
            $sheet->fromArray(['Code', 'Đã dùng', 'User ID', 'Thời gian nhập'], NULL, 'A1');
            $codes = $this->db->fetchAll("SELECT * FROM gift_codes WHERE group_id = ?", [$group_id]);

            $row = 2;
            foreach ($codes as $c) {
                $sheet->setCellValue('A' . $row, $c['code']);
                $sheet->setCellValue('B' . $row, $c['is_used'] ? 'Đã dùng' : 'Chưa');
                $sheet->setCellValue('C' . $row, $c['used_by'] ?? '');
                $sheet->setCellValue('F' . $row, $c['used_at'] ?? '');
                $row++;
            }
        } else {
            $sheet->fromArray(['Code chung', 'Số lần sử dụng tối đa', 'Số lần đã dùng', 'User ID', 'Role ID', 'Server ID', 'Thời gian nhập'], NULL, 'A1');
            $sheet->setCellValue('A2', $group['code_value']);
            $sheet->setCellValue('B2', $group['max_use']);
            $used = $this->db->fetchValue("SELECT COUNT(*) FROM gift_code_claims WHERE group_id = ?", [$group_id]);
            $sheet->setCellValue('C2', $used);

            // Lấy danh sách claims
            $claims = $this->db->fetchAll(
                "SELECT * FROM gift_code_claims WHERE group_id = ? ORDER BY claimed_at DESC",
                [$group_id]
            );

            $row = 2;
            foreach ($claims as $claim) {
                $sheet->setCellValue('D' . $row, $claim['user_id']);
                $sheet->setCellValue('E' . $row, $claim['role_id']);
                $sheet->setCellValue('F' . $row, $claim['server_id']);
                $sheet->setCellValue('G' . $row, $claim['claimed_at']);
                $row++;
            }
        }

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="giftcodes_ID' . $group_id . '.xlsx"');
        header('Cache-Control: max-age=0');
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }

    // Chỉnh sửa nhóm code
    public function edit()
    {
        $this->requireLogin();
        if (!$this->hasPermission('giftcode.edit')) {
            $_SESSION['error'] = 'Bạn không có quyền chỉnh sửa nhóm code.';
            $this->redirectToFirstAccessiblePage();
        }

        $id = (int)($_GET['id'] ?? 0);
        $group = $this->db->fetch("SELECT * FROM gift_code_groups WHERE id = ?", [$id]);
        if (!$group) {
            $_SESSION['error'] = 'Không tìm thấy nhóm code!';
            header('Location: ?route=giftcode');
            exit;
        }

        require __DIR__ . '/../../resources/views/giftcode/edit.php';
    }

    // Cập nhật nhóm code
    public function update()
    {
        $this->requireLogin();
        if (!$this->hasPermission('giftcode.edit')) {
            $_SESSION['error'] = 'Bạn không có quyền chỉnh sửa nhóm code.';
            $this->redirectToFirstAccessiblePage();
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = (int)($_POST['id'] ?? 0);
            $group = $this->db->fetch("SELECT * FROM gift_code_groups WHERE id = ?", [$id]);
            if (!$group) {
                $_SESSION['error'] = 'Không tìm thấy nhóm code!';
                header('Location: ?route=giftcode');
                exit;
            }

            $group_name = trim($_POST['group_name'] ?? '');
            $items = trim($_POST['items'] ?? '');
            $updated_by = $_SESSION['user_id'] ?? 1;
            $is_disabled = (int)($_POST['is_disabled'] ?? 0);

            if (!$this->isValidItemList($items)) {
                $_SESSION['error'] = 'Danh sách item không hợp lệ!';
                header("Location: ?route=giftcode&action=edit&id=$id");
                exit;
            }

            // Xử lý thời hạn sử dụng
            $expired_at = null;
            if ($_POST['expired_type'] === 'limited' && !empty($_POST['expired_at'])) {
                $expired_at = date('Y-m-d H:i:s', strtotime($_POST['expired_at']));
            }

            $data = [
                'group_name' => $group_name,
                'items' => $items,
                'expired_at' => $expired_at,
                'is_disabled' => $is_disabled,
                'updated_at' => date('Y-m-d H:i:s')
            ];

            if ($group['type'] === 'single') {
                $code_prefix = trim($_POST['code_prefix'] ?? '');
                $data['code_prefix'] = $code_prefix;
            } else {
                $code_value = trim($_POST['code_value'] ?? '');
                $max_use = max(1, (int)($_POST['max_use'] ?? 1));
                $data['code_value'] = $code_value;
                $data['max_use'] = $max_use;
            }

            try {
                $this->db->beginTransaction();
                
                // Cập nhật nhóm code
                $this->db->update('gift_code_groups', $data, 'id = ?', [$id]);
                
                // Lưu log
                $this->db->log('giftcode.update', 'success', [
                    'group_id' => $id,
                    'old_data' => $group,
                    'new_data' => $data,
                    'updated_by' => $updated_by
                ]);
                
                $this->db->commit();
                $_SESSION['success'] = 'Cập nhật nhóm code thành công!';
            } catch (Exception $e) {
                $this->db->rollBack();
                $_SESSION['error'] = 'Có lỗi xảy ra khi cập nhật nhóm code!';
                error_log("Error updating gift code group: " . $e->getMessage());
            }

            header("Location: ?route=giftcode&action=edit&id=$id");
            exit;
        }

        header('Location: ?route=giftcode');
        exit;
    }
} 