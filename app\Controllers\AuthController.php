<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Middleware\SecurityMiddleware;

class AuthController extends Controller {
    private $security;
    
    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->security = new SecurityMiddleware();
    }
    
    public function generateCaptcha() {
        // Tạo chuỗi ngẫu nhiên cho CAPTCHA
        $chars = '0123456789';
        $captcha = '';
        for ($i = 0; $i < 5; $i++) {
            $captcha .= $chars[rand(0, strlen($chars) - 1)];
        }
        
        // Lưu CAPTCHA vào session
        $_SESSION['captcha'] = $captcha;
        
        // Tạo hình ảnh CAPTCHA
        $width = 200;
        $height = 50;
        $image = imagecreatetruecolor($width, $height);
        
        // Màu nền
        $bg_color = imagecolorallocate($image, 255, 255, 255);
        imagefill($image, 0, 0, $bg_color);
        
        // Thêm nhiễu
        for ($i = 0; $i < 1000; $i++) {
            $x = rand(0, $width);
            $y = rand(0, $height);
            $color = imagecolorallocate($image, rand(200, 255), rand(200, 255), rand(200, 255));
            imagesetpixel($image, $x, $y, $color);
        }
        
        // Vẽ chữ
        $text_color = imagecolorallocate($image, 0, 0, 0);
        $font = __DIR__ . '/../../resources/fonts/arial.ttf';
        
        // Nếu không có font, sử dụng font mặc định
        if (!file_exists($font)) {
            imagestring($image, 5, 50, 15, $captcha, $text_color);
        } else {
            imagettftext($image, 20, rand(-10, 10), 50, 35, $text_color, $font, $captcha);
        }
        
        // Xuất hình ảnh
        header('Content-Type: image/png');
        imagepng($image);
        imagedestroy($image);
        exit;
    }
    
    public function login() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';
            $captcha = $_POST['captcha'] ?? '';
            
            // Kiểm tra CAPTCHA
            if (!isset($_SESSION['captcha']) || strtolower($captcha) !== strtolower($_SESSION['captcha'])) {
                $_SESSION['error'] = 'Mã CAPTCHA không đúng. Vui lòng thử lại.';
                header('Location: /public/?route=auth&action=login');
                exit;
            }
            
            // Check brute force
            if (!$this->security->checkBruteForce($username)) {
                $_SESSION['error'] = 'Quá nhiều lần đăng nhập sai. Vui lòng thử lại sau 5 phút.';
                header('Location: /public/?route=auth&action=login');
                exit;
            }
            
            // Validate CSRF
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                $_SESSION['error'] = 'Lỗi xác thực. Vui lòng thử lại.';
                header('Location: /public/?route=auth&action=login');
                exit;
            }
            
            try {
                // Validate credentials
                $user = $this->db->query(
                    'SELECT id, username, password, role, email, status 
                     FROM gm_users 
                     WHERE username = ? AND status = 1',
                    [$username]
                )->fetch();
                
                if ($user && password_verify($password, $user['password'])) {
                    // Success
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['email'] = $user['email'];
                    
                    // Update last login
                    $this->db->query(
                        'UPDATE gm_users SET last_login = NOW() WHERE id = ?',
                        [$user['id']]
                    );
                    
                    // Log success
                    $this->security->logLoginAttempt($username, true);
                    $this->logAction('login', 'success');
                    
                    // Kiểm tra quyền và chuyển hướng
                    $permissions = $this->config['permissions']['roles'][$user['role']]['permissions'];
                    $modules = $this->config['permissions']['modules'];
                
                    // Nếu là super_admin hoặc có quyền dashboard.view
                    if ($user['role'] === 'super_admin') {
                        header('Location: /public/?route=dashboard');
                    } else {
                        // Tìm module đầu tiên mà user có quyền
                        foreach ($modules as $module => $moduleData) {
                            if ($module === 'dashboard') continue; // Bỏ qua dashboard
                            
                            $hasPermission = $permissions === '*' || in_array($module . '.view', $permissions);
                            if ($hasPermission) {
                                header('Location: /public/?route=' . $module);
                                exit;
                            }
                        }
                        
                        // Nếu không có quyền nào, hiển thị thông báo lỗi
                        $_SESSION['error'] = 'Bạn không có quyền truy cập bất kỳ trang nào';
                        header('Location: /public/?route=auth&action=login');
                    }
                    exit;
                } else {
                    // Failed
                    $this->security->logLoginAttempt($username);
                    $this->logAction('login', 'failed', ['username' => $username]);
                    
                    $_SESSION['error'] = 'Tên đăng nhập hoặc mật khẩu không đúng';
                    header('Location: /public/?route=auth&action=login');
                    exit;
                }
            } catch (\Exception $e) {
                $_SESSION['error'] = 'Có lỗi xảy ra. Vui lòng thử lại sau.';
                $this->logAction('login', 'Error', [
                    'username' => $username,
                    'error' => $e->getMessage()
                ]);
                header('Location: /public/?route=auth&action=login');
                exit;
            }
        }
        
        // Show login form
        $csrf_token = $this->security->generateCSRFToken();
        require_once __DIR__ . '/../../resources/views/auth/login.php';
    }
    
    public function logout() {
        // Xóa toàn bộ session
        session_unset();
        session_destroy();
        
        // Chuyển hướng về trang đăng nhập
        header('Location: /public/?route=auth&action=login');
        exit;
    }
    
    private function logAction($action, $status, $data = []) {
        $this->db->log($action, $status, $data);
    }
} 