<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Chỉnh sửa thông tin người dùng
        </h3>
    </div>
    <div class="border-t border-gray-200">
        <form method="POST" action="/public/?route=user&action=edit&id=<?php echo $user['id']; ?>" class="p-4">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700">User ID</label>
                    <input type="text" id="user_id" value="<?php echo htmlspecialchars($user['user_id']); ?>" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" readonly>
                </div>
                
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700">Tên người dùng</label>
                    <input type="text" name="username" id="username" value="<?php echo htmlspecialchars($user['username']); ?>" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" required>
                </div>
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <input type="email" name="email" id="email" value="<?php echo htmlspecialchars($user['email']); ?>" 
                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                </div>
                
                <div class="relative flex items-start py-2">
                    <div class="flex items-center h-5">
                        <input type="checkbox" 
                               name="is_gm" 
                               id="is_gm" 
                               <?php echo $user['is_gm'] ? 'checked' : ''; ?> 
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded transition duration-150 ease-in-out">
                    </div>
                    <div class="ml-3">
                        <label for="is_gm" class="text-sm font-medium text-gray-700">
                            Quyền GM
                        </label>
                        <p class="text-sm text-gray-500">
                            Có thể truy cập máy chủ sớm và trong thời gian bảo trì
                        </p>
                    </div>
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" name="status" id="status" <?php echo $user['status'] ? 'checked' : ''; ?> 
                           class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                    <label for="status" class="ml-2 block text-sm text-gray-900">Trạng thái hoạt động</label>
                </div>
                
                <div>
                    <label for="notes" class="block text-sm font-medium text-gray-700">Ghi chú</label>
                    <textarea name="notes" id="notes" rows="3" 
                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"><?php echo htmlspecialchars($user['notes']); ?></textarea>
                </div>
                
                <div class="bg-gray-50 p-4 rounded-md">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Thông tin khác</h4>
                    <div class="space-y-1 text-sm text-gray-500">
                        <p>Nguồn đăng ký: <?php echo htmlspecialchars($user['register_source']); ?></p>
                        <p>Lần đăng nhập cuối: <?php echo $user['last_login'] ? date('d/m/Y H:i', strtotime($user['last_login'])) : 'Chưa đăng nhập'; ?></p>
                        <p>Ngày tạo: <?php echo date('d/m/Y H:i', strtotime($user['created_at'])); ?></p>
                        <p>Cập nhật lần cuối: <?php echo date('d/m/Y H:i', strtotime($user['updated_at'])); ?></p>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 flex justify-end space-x-3">
                <a href="/public/?route=user" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Quay lại
                </a>
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Lưu thay đổi
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 