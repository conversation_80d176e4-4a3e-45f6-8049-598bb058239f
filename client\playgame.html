<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <title>Egret</title>
    <meta name="viewport"
        content="width=device-width,initial-scale=1, minimum-scale=1, maximum-scale=1, user-scalable=no ,minimal-ui" />
    <meta name="full-screen" content="true" />
    <meta name="screen-orientation" content="landscape" />
    <meta name="x5-fullscreen" content="true" />
    <meta name="360-fullscreen" content="true" />
	
	<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
	<meta http-equiv="Pragma" content="no-cache">
	<meta http-equiv="Expires" content="0">

    <!-- 允许全屏模式浏览 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <!-- 定义safari顶端状态条的样式 -->
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
	
    <style>
        @font-face{
            font-family: "HuaKanYT";
            src: url("resource/font/hkytW7p.ttf");
        }
        html,
        body {
            background: #000000;
            padding: 0;
            border: 0;
            margin: 0;
            width: 100%;
            height: 100%;
            font-family:"HuaKanYT";
            text-align: center;
        }
    </style>
</head>
<body>
    <div style="margin:0 auto;width: 100%;height:100%;" class="egret-player" data-entry-class="Main"
        data-orientation="landscape" data-scale-mode="showAll" data-frame-rate="60" data-content-width="1334"
        data-content-height="750" data-multi-fingered="2" data-show-fps="false" data-show-log="false"
        data-show-fps-style="x:0,y:0,size:20,textColor:0xffffff,bgAlpha:0.9">

        <div id="proDiv"
            style="position: absolute;margin:auto;width: 1334;height: 750; background-repeat: no-repeat; background-size: cover;left: 50%; top: 50%;transform:  translateX(-50%) translateY(-50%)">

            <div id="loading2"
                style="position: absolute; margin: 0; padding: 0; width:243;height: 243; left: 50%; top: 50%; transform:  translateX(-50%) translateY(-50%)">
                <div id="loadingFloor"
                    style="position: relative; left: 50%; top: 50%;height: 37; transform: translateX(-50%)">
                    <img src="resource/eui/loading/loadingFloor.png" />
                </div>
                <div id="loading3jxw"
                    style="position: absolute; width: 79; height: 105;  left: 50%; top: 50%; transform:  translateX(-50%) translateY(-60%)">
                    <img src="resource/eui/loading/loading3jxw.png" />
                </div>
                <div
                    style="position: absolute;width: 210;height: 221;  top: 50%; left: 50%;transform: translateX(-50%)  translateY(-50%)">
                    <img id="loadingPro" src="resource/eui/loading/loadingPro.png" />
                </div>
                <div
                    style="position: absolute; height: 20; top: 50%;left: 50%;transform: translateX(-50%)  translateY(225%)">
                    <label style="color: #ffffff; display: block; font-size: 20;">LOADING</label>
                </div>
                <div
                    style="position:absolute; height:35; top: 50%;left: 50%;transform: translateX(-50%)  translateY(250%);">
                    <label id="proLb" style="color: #ffffff; display: block; font-size: 35px;"> ... 0% </label>
                </div>
            </div>
        </div>
    </div>
    <script>
        //判断是否是移动端
        function browserRedirect() {
            var sUserAgent = navigator.userAgent.toLowerCase();

            var bIsIpad = sUserAgent.match(/ipad/i) == "ipad";
            var bIsIphoneOs = sUserAgent.match(/iphone os/i) == "iphone os";
            var bIsMidp = sUserAgent.match(/midp/i) == "midp";
            var bIsUc7 = sUserAgent.match(/rv:*******/i) == "rv:*******";
            var bIsUc = sUserAgent.match(/ucweb/i) == "ucweb";
            var bIsAndroid = sUserAgent.match(/android/i) == "android";
            var bIsCE = sUserAgent.match(/windows ce/i) == "windows ce";
            var bIsWM = sUserAgent.match(/windows phone/i) == "windows mobile";
            var bIsMobile = sUserAgent.match(/mobile/i) == "mobile";
            var bIsPlaybook = sUserAgent.match(/playbook/i) == "playbook";

            if (bIsIpad || bIsIphoneOs || bIsMidp || bIsUc7 || bIsCE || bIsWM || bIsMobile || bIsPlaybook) {
                return true;
            } else {
                return false;
            }
        }
        
        var isPhone = browserRedirect();

        if (!isPhone) {
            if (window && window.setInterval) {
                function rotate() {
                    rotateVal -= 4;
                    var sty = img.style;
                    sty.webkitTransform = img.style.MozTransform = img.style.msTransform = img.style.OTransform = "rotate(" + rotateVal + "deg)"
                }
                var rotateVal = 0
                var img = document.getElementById('loadingPro')
                var rotater = window.setInterval("rotate()", 10);
            }
        }

        var loadScript = function (list, callback) {
            var loaded = 0;
            var label = document.getElementById("proLb");
            var loadNext = function () {
                loadSingleScript(list[loaded], function () {
                    loaded++;
                    label.innerText = "... " + Math.floor(loaded / list.length * 100) + "%";
                    if (loaded >= list.length) {
                        callback();
                    }
                    else {
                        loadNext();
                    }
                })
            };
            loadNext();
        };

        var loadSingleScript = function (src, callback) {
            var s = document.createElement('script');
            s.async = false;
            s.src = src;
            s.addEventListener('load', function () {
                s.parentNode.removeChild(s);
                s.removeEventListener('load', arguments.callee, false);
                callback();
            }, false);
            document.body.appendChild(s);
        };

        var xhr = new XMLHttpRequest();
        xhr.open('GET', './manifest.json?v=' + Math.random(), true);
        xhr.addEventListener("load", function () {
            var manifest = JSON.parse(xhr.response);
            var list = manifest.initial.concat(manifest.game);
            loadScript(list, function () {
				BASIS({
				  onRuntimeInitialized: () => {
					console.log('WASM is initialized');
				  }
				}).then(module => {
				  console.log('Promise resolved');
				  window.Module = module;
					egret.runEgret({
						renderMode: "webgl", 
						audioType: 2, 
						antialias: false, 
						calculateCanvasScaleFactor: function (context) {
							var backingStore = context.backingStorePixelRatio ||
								context.webkitBackingStorePixelRatio ||
								context.mozBackingStorePixelRatio ||
								context.msBackingStorePixelRatio ||
								context.oBackingStorePixelRatio ||
								context.backingStorePixelRatio || 1;
							return (window.devicePixelRatio || 1) / backingStore;
						}
					});
				});
            });
        });
        xhr.send(null);
    </script>
</body>
</html> 