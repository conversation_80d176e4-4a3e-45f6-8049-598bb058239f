<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <!-- Thêm div thông báo -->
    <div id="notification" class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 hidden">
        <div class="bg-white p-4 sm:p-8 rounded-xl shadow-2xl border-2 w-[90vw] sm:min-w-[400px] sm:max-w-[600px]">
            <div class="text-center">
                <div class="mb-4">
                    <svg id="notification-icon" class="h-12 sm:h-16 w-12 sm:w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <!-- Icon sẽ được thay đổi tùy theo trạng thái -->
                    </svg>
                </div>
                <h3 id="notification-title" class="text-xl sm:text-2xl font-bold mb-3"></h3>
                <p id="notification-message" class="text-gray-700 text-base sm:text-lg mb-4 sm:mb-6"></p>
                <div id="notification-details" class="text-left mb-4 sm:mb-6 bg-gray-50 p-3 sm:p-4 rounded-lg"></div>
                <button onclick="closeNotification()" class="bg-blue-600 text-white px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-150 ease-in-out text-sm sm:text-base">
                    Đóng
                </button>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <h2 class="text-xl sm:text-2xl font-bold text-gray-900">
                    Gửi thư hệ thống
                </h2>
            </div>
            
            <div class="p-3 sm:p-6">
                <form id="mail-form" class="space-y-4 sm:space-y-6">
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <!-- Chọn máy chủ -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300">
                        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2">
                            <label class="block text-base sm:text-lg font-medium text-gray-700">Máy chủ</label>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="select-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-blue-600 hover:text-blue-800 bg-blue-50 rounded-md border border-blue-200">Chọn tất cả</button>
                                <button type="button" id="deselect-all" class="px-3 sm:px-4 py-1.5 sm:py-2 text-sm sm:text-base text-red-600 hover:text-red-800 bg-red-50 rounded-md border border-red-200">Bỏ chọn tất cả</button>
                            </div>
                        </div>
                        <div class="relative">
                            <input type="text" id="server-search" placeholder="Tìm kiếm máy chủ..." 
                                   class="w-full px-3 sm:px-4 py-2 sm:py-3 text-base border-2 border-gray-400 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <div class="absolute right-3 top-2.5">
                                <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                        <div class="mt-3 sm:mt-4 max-h-96 overflow-y-auto border-2 border-gray-300 rounded-md">
                            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-3 p-2 sm:p-3">
                                <?php 
                                // Tạo mảng để lưu trữ các mysql_db và server tương ứng
                                $dbServers = [];
                                foreach ($servers as $server) {
                                    if (!empty($server['mysql_db'])) {
                                        $dbServers[$server['mysql_db']][] = $server;
                                    }
                                }
                                
                                foreach ($servers as $server): ?>
                                    <?php if (!$server['merged_into']): ?>
                                        <div class="server-item flex flex-col p-3 hover:bg-gray-100 rounded-md border border-gray-200">
                                            <div class="flex items-center">
                                                <input type="checkbox" name="server_ids[]" value="<?php echo $server['id']; ?>" 
                                                       class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400 rounded">
                                                <div class="ml-3">
                                                    <label class="block text-base font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($server['name']); ?>
                                                    </label>
                                                    <span class="text-sm text-gray-500">ID: <?php echo $server['id']; ?></span>
                                                </div>
                                            </div>
                                            <?php if (!empty($server['mysql_db']) && count($dbServers[$server['mysql_db']]) > 1): ?>
                                                <div class="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                                                    <div class="flex items-start">
                                                        <svg class="h-5 w-5 text-yellow-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                                                        </svg>
                                                        <div class="text-sm text-yellow-700">
                                                            <p class="font-medium">Cảnh báo: Database trùng lặp</p>
                                                            <p class="mt-1">Database này cũng được sử dụng bởi:</p>
                                                            <ul class="list-disc list-inside mt-1">
                                                                <?php foreach ($dbServers[$server['mysql_db']] as $otherServer): ?>
                                                                    <?php if ($otherServer['id'] != $server['id']): ?>
                                                                        <li>Server <?php echo $otherServer['id']; ?>: <?php echo htmlspecialchars($otherServer['name']); ?></li>
                                                                    <?php endif; ?>
                                                                <?php endforeach; ?>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>

                     <!-- Loại mail -->
                     <div class="bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300" hidden>
                        <label class="block text-base sm:text-lg font-medium text-gray-700 mb-2">Loại mail</label>
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-4">
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="kind" value="0" checked class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Mail thông thường
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="kind" value="1" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Mail cá nhân
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="kind" value="2" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Mail hệ thống
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="kind" value="3" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Mail công hội
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="kind" value="4" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Mail nạp thẻ
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="kind" value="5" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Mail gift code
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Loại gửi -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg border-2 border-gray-300">
                        <label class="block text-base sm:text-lg font-medium text-gray-700 mb-2">Loại gửi</label>
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-4">
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="type" value="1" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Người chơi cụ thể
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="type" value="2" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Người chơi đang online
                                </label>
                            </div>
                            <div class="flex items-center p-3 border-2 border-gray-300 rounded-md hover:bg-gray-100">
                                <input type="radio" name="type" value="3" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-2 border-gray-400">
                                <label class="ml-3 block text-base font-medium text-gray-700">
                                    Toàn bộ máy chủ
                                </label>
                            </div>
                        </div>
                    </div>

                   

                    <!-- ID người chơi -->
                    <div id="role-ids-container" class="hidden">
                        <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700">ID người chơi</label>
                            <div class="mt-1">
                                <textarea name="role_ids" rows="3" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md" placeholder="Nhập ID người chơi, mỗi ID một dòng"></textarea>
                            </div>
                            <p class="mt-2 text-sm text-gray-500">Mỗi ID người chơi một dòng</p>
                        </div>
                    </div>

                    

                    <!-- Level tối thiểu -->
                    <div id="min-level-container" class="hidden">
                        <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                            <label class="block text-sm font-medium text-gray-700">Level tối thiểu</label>
                            <select name="min_level" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                <option value="50">50</option>
                                <option value="60">60</option>
                                <option value="70">70</option>
                                <option value="80">80</option>
                                <option value="90">90</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>

                    <!-- Vật phẩm -->
                    <div id="items-container" class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <div class="flex flex-col sm:flex-row sm:items-center justify-between mb-3 sm:mb-4 gap-2">
                            <label class="block text-sm font-medium text-gray-700">Vật phẩm (Tối đa 5 vật phẩm)</label>
                            <div class="flex space-x-2">
                                <button type="button" id="toggle-item-input" class="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                                    </svg>
                                    Nhập nhanh
                                </button>
                            </div>
                        </div>

                        <!-- Input nhập nhanh -->
                        <div id="quick-item-input" class="hidden mb-3 sm:mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Nhập nhanh vật phẩm (ID:Số lượng,ID:Số lượng,...)</label>
                            <div class="flex space-x-2">
                                <div class="relative flex-1">
                                    <input type="text" id="quick-items" name="quick_items" placeholder="Ví dụ: 10000:1,10001:2,10003:2" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                    <button type="button" onclick="requestAndShowItemInfo(document.getElementById('quick-items').value)" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none">
                                        <i class="fas fa-search text-gray-400"></i>
                                    </button>
                                </div>
                            </div>
                            <p class="mt-2 text-sm text-gray-500">Định dạng: ID:Số lượng,ID:Số lượng,...</p>
                        </div>

                        <!-- Input nhập thủ công -->
                        <div id="manual-item-input">
                            <div class="flex items-center justify-end mb-3 sm:mb-4">
                                <button type="button" id="add-item" class="inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    Thêm vật phẩm
                                </button>
                            </div>
                            <div class="space-y-3 sm:space-y-4">
                                <div class="item-row grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-4 p-2 sm:p-3 bg-white rounded-md shadow-sm">
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">ID vật phẩm</label>
                                        <input type="number" name="items[0][id]" placeholder="ID" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Số lượng</label>
                                        <input type="number" name="items[0][num]" placeholder="Số lượng" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Khóa</label>
                                        <select name="items[0][is_bind]" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                            <option value="0">Không khóa</option>
                                            <option value="1">Khóa</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Thời hạn (giờ)</label>
                                        <input type="number" name="items[0][invalid_time]" placeholder="Thời hạn" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                    </div>
                                    <div>
                                        <label class="block text-xs text-gray-500 mb-1">Tham số</label>
                                        <input type="text" name="items[0][param]" placeholder="Tham số" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                                    </div>
                                    <div class="flex items-end">
                                        <button type="button" class="remove-item inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                            <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                            Xóa
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tiêu đề -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm font-medium text-gray-700">Tiêu đề</label>
                        <div class="mt-1">
                            <input type="text" name="subject" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                        </div>
                    </div>

                    <!-- Nội dung -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm font-medium text-gray-700">Nội dung</label>
                        <div class="mt-1">
                            <textarea name="content" rows="4" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md"></textarea>
                        </div>
                    </div>

                    <!-- Lý do -->
                    <div class="bg-gray-50 p-3 sm:p-4 rounded-lg">
                        <label class="block text-sm font-medium text-gray-700">Lý do</label>
                        <div class="mt-1">
                            <textarea name="reason" rows="2" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md"></textarea>
                        </div>
                    </div>

                    <!-- Nút gửi -->
                    <div class="flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 border border-transparent text-sm sm:text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                            </svg>
                            Gửi thư
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script>
// Thêm hàm hiển thị thông báo
function showNotification(title, message, details = '') {
    const isSuccess = title === 'Thành công';
    
    let html = `<div class="text-left">`;
    html += `<p class="mb-3 sm:mb-4">${message}</p>`;
    if (details) {
        html += `<div class="bg-gray-50 p-2 sm:p-4 rounded-lg">${details}</div>`;
    }
    html += `</div>`;

    Swal.fire({
        title: title,
        html: html,
        icon: isSuccess ? 'success' : 'error',
        confirmButtonText: 'Đóng',
        confirmButtonColor: isSuccess ? '#2563eb' : '#dc2626',
        width: '90vw',
        maxWidth: '600px',
        customClass: {
            popup: 'rounded-xl',
            title: 'text-xl sm:text-2xl font-bold mb-3 sm:mb-4',
            content: 'text-base sm:text-lg',
            confirmButton: 'px-4 sm:px-6 py-2 sm:py-3 text-sm sm:text-base'
        }
    });
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('mail-form');
    const roleIdsContainer = document.getElementById('role-ids-container');
    const typeRadios = document.querySelectorAll('input[name="type"]');
    const itemsContainer = document.getElementById('items-container');
    const addItemButton = document.getElementById('add-item');
    const selectAllButton = document.getElementById('select-all');
    const deselectAllButton = document.getElementById('deselect-all');
    const serverSearch = document.getElementById('server-search');
    const serverItems = document.querySelectorAll('.server-item');
    const minLevelContainer = document.getElementById('min-level-container');
    let itemCount = 1;

    // Kiểm tra sự tồn tại của form
    if (!form) {
        console.error('Form not found');
        return;
    }

    // Tìm kiếm server
    if (serverSearch) {
        serverSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            serverItems.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                const id = item.querySelector('span').textContent.toLowerCase();
                if (label.includes(searchTerm) || id.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Chọn/bỏ chọn tất cả máy chủ
    if (selectAllButton) {
        selectAllButton.addEventListener('click', function() {
            serverItems.forEach(item => {
                if (item.style.display !== 'none') {
                    item.querySelector('input[type="checkbox"]').checked = true;
                }
            });
        });
    }

    if (deselectAllButton) {
        deselectAllButton.addEventListener('click', function() {
            serverItems.forEach(item => {
                if (item.style.display !== 'none') {
                    item.querySelector('input[type="checkbox"]').checked = false;
                }
            });
        });
    }

    // Hiển thị/ẩn ID người chơi
    if (typeRadios.length > 0) {
        typeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (roleIdsContainer) {
                    roleIdsContainer.style.display = this.value === '1' ? 'block' : 'none';
                }
                if (minLevelContainer) {
                    minLevelContainer.style.display = this.value === '3' ? 'block' : 'none';
                }
            });
        });
    }

    // Thêm vật phẩm
    if (addItemButton) {
        addItemButton.addEventListener('click', function() {
            const itemRows = document.querySelectorAll('.item-row');
            if (itemRows.length >= 5) {
                Swal.fire({
                    title: 'Lỗi',
                    text: 'Tối đa chỉ được 5 vật phẩm',
                    icon: 'error',
                    confirmButtonText: 'Đóng',
                    confirmButtonColor: '#dc2626',
                    width: '90vw',
                    maxWidth: '400px'
                });
                return;
            }

            const newIndex = itemRows.length;
            const itemRow = document.createElement('div');
            itemRow.className = 'item-row grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-2 sm:gap-4 p-2 sm:p-3 bg-white rounded-md shadow-sm';
            itemRow.innerHTML = `
                <div>
                    <label class="block text-xs text-gray-500 mb-1">ID vật phẩm</label>
                    <input type="number" name="items[${newIndex}][id]" placeholder="ID" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-500 mb-1">Số lượng</label>
                    <input type="number" name="items[${newIndex}][num]" placeholder="Số lượng" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-500 mb-1">Khóa</label>
                    <select name="items[${newIndex}][is_bind]" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                        <option value="0">Không khóa</option>
                        <option value="1">Khóa</option>
                    </select>
                </div>
                <div>
                    <label class="block text-xs text-gray-500 mb-1">Thời hạn (giờ)</label>
                    <input type="number" name="items[${newIndex}][invalid_time]" placeholder="Thời hạn" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                </div>
                <div>
                    <label class="block text-xs text-gray-500 mb-1">Tham số</label>
                    <input type="text" name="items[${newIndex}][param]" placeholder="Tham số" class="text-base px-3 py-2 shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full border-2 border-gray-400 rounded-md">
                </div>
                <div class="flex items-end">
                    <button type="button" class="remove-item inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <svg class="h-4 w-4 sm:h-5 sm:w-5 mr-1 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                        Xóa
                    </button>
                </div>
            `;
            document.querySelector('.space-y-3').appendChild(itemRow);
        });
    }

    // Xóa vật phẩm
    if (itemsContainer) {
        itemsContainer.addEventListener('click', function(e) {
            if (e.target.classList.contains('remove-item') || e.target.closest('.remove-item')) {
                const itemRow = e.target.closest('.item-row');
                if (itemRow) {
                    itemRow.remove();
                }
            }
        });
    }

    // Xử lý submit form
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Hiển thị loading
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = `
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Đang gửi...
        `;

        // Gửi request
        fetch('?route=mail&action=send', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: $(form).serialize()
        })
        .then(res => res.json())
        .then(data => {
            console.log('Response data:', data);
            
            if (!data.status) {
                // Hiển thị thông báo lỗi từ API
                showNotification(
                    'Lỗi',
                    data.message || 'Có lỗi xảy ra khi gửi thư. Vui lòng thử lại sau.'
                );
                return;
            }

            let details = '<div class="space-y-2">';
            data.data.results.forEach(result => {
                if (result.status) {
                    details += `<p class="text-green-600 font-medium">Server ${result.server_id}: ${result.message}</p>`;
                } else {
                    details += `<p class="text-red-600 font-medium">Server ${result.server_id}: ${result.message}</p>`;
                    if (result.details && result.details.length > 0) {
                        details += '<div class="pl-4 mt-1">';
                        result.details.forEach(detail => {
                            details += `<p class="text-sm text-red-500">- ${detail.error}</p>`;
                        });
                        details += '</div>';
                    }
                }
            });
            details += '</div>';

            showNotification(
                'Thành công',
                `Gửi thư thành công: ${data.data.success_count} máy chủ, Thất bại: ${data.data.error_count} máy chủ`,
                details
            );
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification(
                'Lỗi',
                'Có lỗi xảy ra khi gửi thư. Vui lòng thử lại sau.'
            );
        })
        .finally(() => {
            // Khôi phục nút submit
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        });
    });

    // Xử lý chuyển đổi giữa 2 chế độ nhập items
    const toggleItemInput = document.getElementById('toggle-item-input');
    const quickItemInput = document.getElementById('quick-item-input');
    const manualItemInput = document.getElementById('manual-item-input');
    const quickItemsInput = document.getElementById('quick-items');

    if (toggleItemInput) {
        toggleItemInput.addEventListener('click', function() {
            quickItemInput.classList.toggle('hidden');
            manualItemInput.classList.toggle('hidden');
            const isQuickInputVisible = !quickItemInput.classList.contains('hidden');
            this.textContent = isQuickInputVisible ? 'Nhập thủ công' : 'Nhập nhanh';
            this.classList.toggle('bg-green-600');
            this.classList.toggle('bg-blue-600');
        });
    }

    // Xử lý khi nhập nhanh thay đổi
    if (quickItemsInput) {
        quickItemsInput.addEventListener('input', function() {
            const itemsText = this.value.trim();
            if (itemsText) {
                requestAndShowItemInfo(itemsText);
            }
        });
    }
});
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 