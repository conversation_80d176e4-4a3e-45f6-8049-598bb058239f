<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white shadow-xl rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-2xl font-bold text-gray-900">
                        AI Chat - Phân tích dữ liệu
                    </h2>
                    <div class="flex items-center space-x-2">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <span class="w-2 h-2 mr-2 bg-green-500 rounded-full"></span>
                            Online
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="flex flex-col h-[calc(100vh-12rem)]">
                <div id="chat-container" class="flex-1 overflow-y-auto p-6 space-y-4 bg-gray-50">
                    <div id="welcome-message" class="flex items-center justify-center h-full">
                        <div class="text-center text-gray-500">
                            <i class="fas fa-robot text-4xl mb-4"></i>
                            <p class="text-lg">Xin chào! Tôi có thể giúp gì cho bạn?</p>
                            <p class="text-sm mt-2">Bạn có thể hỏi tôi về dữ liệu, thống kê hoặc bất kỳ câu hỏi nào khác.</p>
                        </div>
                    </div>
                </div>
                
                <div class="border-t border-gray-200 bg-white p-4">
                    <form id="chat-form" class="flex gap-4">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        <div class="flex-1">
                            <input type="text" id="query" 
                                   class="w-full p-4 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-lg"
                                   placeholder="Nhập câu hỏi của bạn...">
                        </div>
                        <button type="submit" 
                                class="bg-blue-600 text-white px-6 py-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="fas fa-paper-plane mr-2"></i>
                            Gửi
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let isProcessing = false;

document.getElementById('chat-form').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (isProcessing) {
        return;
    }
    
    const query = document.getElementById('query').value;
    if (!query) return;
    
    // Đánh dấu đang xử lý
    isProcessing = true;
    document.getElementById('query').disabled = true;
    document.querySelector('button[type="submit"]').disabled = true;
    
    // Thêm câu hỏi vào chat
    addMessage('user', query);
    document.getElementById('query').value = '';
    
    // Hiển thị loading
    const loadingId = 'loading-' + Date.now();
    const loadingMessageId = addMessage('ai', `
        <div id="${loadingId}" class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
        </div>
    `);
    
    try {
        const response = await fetch('/public/?route=aichat&action=analyze', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `query=${encodeURIComponent(query)}&csrf_token=${document.querySelector('input[name="csrf_token"]').value}`
        });
        
        const data = await response.json();
        
        // Xóa loading animation
        const loadingElement = document.getElementById(loadingId);
        if (loadingElement) {
            loadingElement.parentElement.parentElement.remove();
        }
        
        if (data.success) {
            if (data.type === 'chat') {
                // Hiển thị phản hồi chat thông thường
                addMessage('ai', `
                    <div class="bg-white p-4 rounded-lg border shadow-sm">
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-robot text-blue-500 text-xl"></i>
                            </div>
                            <div class="flex-1">
                                ${data.response}
                            </div>
                        </div>
                    </div>
                `);
            } else {
                // Hiển thị SQL
                addMessage('ai', `
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2 text-gray-700">
                            <i class="fas fa-database"></i>
                            <span class="font-medium">SQL được tạo:</span>
                        </div>
                        <div class="bg-gray-800 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto">
                            ${data.sql}
                        </div>
                    </div>
                `);
                
                // Hiển thị phân tích
                addMessage('ai', `
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2 text-gray-700">
                            <i class="fas fa-chart-bar"></i>
                            <span class="font-medium">Phân tích:</span>
                        </div>
                        <div class="bg-white p-4 rounded-lg border shadow-sm markdown-content">
                            ${formatMarkdown(data.analysis)}
                        </div>
                    </div>
                `);
                
                // Hiển thị kết quả
                if (data.results && data.results.length > 0) {
                    addMessage('ai', `
                        <div class="space-y-3">
                            <div class="flex items-center space-x-2 text-gray-700">
                                <i class="fas fa-table"></i>
                                <span class="font-medium">Kết quả (${data.results.length} bản ghi):</span>
                            </div>
                            <div class="bg-white rounded-lg border shadow-sm overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            ${Object.keys(data.results[0]).map(key => `
                                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">${key}</th>
                                            `).join('')}
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        ${data.results.map(row => `
                                            <tr class="hover:bg-gray-50">
                                                ${Object.values(row).map(value => `
                                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${value}</td>
                                                `).join('')}
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    `);
                }
            }
        } else {
            addMessage('ai', `
                <div class="bg-red-50 text-red-700 p-4 rounded-lg border border-red-200">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>Lỗi: ${data.error}</span>
                    </div>
                </div>
            `);
        }
    } catch (error) {
        // Xóa loading animation trong trường hợp lỗi
        const loadingElement = document.getElementById(loadingId);
        if (loadingElement) {
            loadingElement.parentElement.parentElement.remove();
        }
        
        addMessage('ai', `
            <div class="bg-red-50 text-red-700 p-4 rounded-lg border border-red-200">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Lỗi: ${error.message}</span>
                </div>
            </div>
        `);
    } finally {
        // Cho phép gửi tin nhắn mới
        isProcessing = false;
        document.getElementById('query').disabled = false;
        document.querySelector('button[type="submit"]').disabled = false;
        document.getElementById('query').focus();
    }
});

function addMessage(role, content) {
    const container = document.getElementById('chat-container');
    const welcomeMessage = document.getElementById('welcome-message');
    
    // Ẩn welcome message khi có tin nhắn đầu tiên
    if (welcomeMessage) {
        welcomeMessage.style.display = 'none';
    }
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `flex ${role === 'user' ? 'justify-end' : 'justify-start'}`;
    
    const messageContent = document.createElement('div');
    messageContent.className = `max-w-3xl ${role === 'user' ? 'bg-blue-600 text-white' : 'bg-white'} p-4 rounded-lg shadow-sm`;
    messageContent.innerHTML = content;
    
    messageDiv.appendChild(messageContent);
    container.appendChild(messageDiv);
    container.scrollTop = container.scrollHeight;
    
    return messageDiv.id = 'msg-' + Date.now();
}

function formatMarkdown(text) {
    // Convert markdown to HTML
    let html = text
        // Headings
        .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>')
        .replace(/^### (.*$)/gm, '<h3 class="text-lg font-medium mt-3 mb-2">$1</h3>')
        
        // Lists
        .replace(/^- (.*$)/gm, '<li class="ml-4">$1</li>')
        .replace(/(<li>.*<\/li>)/g, '<ul class="list-disc ml-6 mb-2">$1</ul>')
        
        // Code blocks
        .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 p-2 rounded-lg font-mono text-sm overflow-x-auto my-2">$1</pre>')
        
        // Inline code
        .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 rounded font-mono text-sm">$1</code>')
        
        // Bold
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-bold">$1</strong>')
        
        // Italic
        .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
        
        // Line breaks
        .replace(/\n/g, '<br>');
    
    return html;
}
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 