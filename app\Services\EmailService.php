<?php

namespace App\Services;

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService 
{
    private static $instance = null;
    private $mailer;
    private $config;
    
    private function __construct() {
        $this->config = [
            'host' => getenv('MAIL_HOST') ?: 'smtp.gmail.com',
            'port' => getenv('MAIL_PORT') ?: 587,
            'username' => getenv('MAIL_USERNAME') ?: '',
            'password' => getenv('MAIL_PASSWORD') ?: '',
            'encryption' => getenv('MAIL_ENCRYPTION') ?: 'tls',
            'from_address' => getenv('MAIL_FROM_ADDRESS') ?: '',
            'from_name' => getenv('MAIL_FROM_NAME') ?: 'GMTool'
        ];
        
        $this->initializeMailer();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Initialize PHPMailer
     */
    private function initializeMailer(): void
    {
        $this->mailer = new PHPMailer(true);
        
        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['username'];
            $this->mailer->Password = $this->config['password'];
            $this->mailer->SMTPSecure = $this->config['encryption'];
            $this->mailer->Port = $this->config['port'];
            $this->mailer->CharSet = 'UTF-8';
            
            // Default sender
            $this->mailer->setFrom($this->config['from_address'], $this->config['from_name']);
            
        } catch (Exception $e) {
            throw new \Exception("Email configuration error: " . $e->getMessage());
        }
    }
    
    /**
     * Send email verification code
     */
    public function sendEmailVerification(string $email, string $verificationCode, string $username = ''): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);
            
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Xác thực email - ' . $this->config['from_name'];
            
            // Load email template
            $template = $this->loadTemplate('email_verification', [
                'username' => $username ?: $email,
                'verification_code' => $verificationCode,
                'app_name' => $this->config['from_name'],
                'app_url' => getenv('APP_URL') ?: 'http://localhost'
            ]);
            
            $this->mailer->Body = $template;
            
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Send password reset email
     */
    public function sendPasswordReset(string $email, string $resetToken, string $username = ''): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);
            
            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Khôi phục mật khẩu - ' . $this->config['from_name'];
            
            // Create reset URL
            $resetUrl = (getenv('APP_URL') ?: 'http://localhost') . '/reset-password?token=' . $resetToken;
            
            // Load email template
            $template = $this->loadTemplate('password_reset', [
                'username' => $username ?: $email,
                'reset_url' => $resetUrl,
                'reset_token' => $resetToken,
                'app_name' => $this->config['from_name'],
                'app_url' => getenv('APP_URL') ?: 'http://localhost'
            ]);
            
            $this->mailer->Body = $template;
            
            return $this->mailer->send();
            
        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Load email template
     */
    private function loadTemplate(string $templateName, array $variables = []): string
    {
        $templatePath = __DIR__ . '/../../resources/email_templates/' . $templateName . '.php';
        
        if (!file_exists($templatePath)) {
            // Return basic template if file doesn't exist
            return $this->getBasicTemplate($templateName, $variables);
        }
        
        // Extract variables for use in template
        extract($variables);
        
        // Start output buffering
        ob_start();
        include $templatePath;
        $content = ob_get_clean();
        
        return $content;
    }
    
    /**
     * Get basic template if template file doesn't exist
     */
    private function getBasicTemplate(string $templateName, array $variables): string
    {
        $appName = $variables['app_name'] ?? 'GMTool';
        $username = $variables['username'] ?? '';
        
        if ($templateName === 'email_verification') {
            $verificationCode = $variables['verification_code'] ?? '';
            return "
                <h2>Xác thực email - {$appName}</h2>
                <p>Xin chào {$username},</p>
                <p>Mã xác thực email của bạn là: <strong>{$verificationCode}</strong></p>
                <p>Mã này có hiệu lực trong 5 phút.</p>
                <p>Trân trọng,<br>{$appName}</p>
            ";
        } elseif ($templateName === 'password_reset') {
            $resetUrl = $variables['reset_url'] ?? '';
            $resetToken = $variables['reset_token'] ?? '';
            return "
                <h2>Khôi phục mật khẩu - {$appName}</h2>
                <p>Xin chào {$username},</p>
                <p>Bạn đã yêu cầu khôi phục mật khẩu. Vui lòng click vào link bên dưới để đặt lại mật khẩu:</p>
                <p><a href='{$resetUrl}'>Đặt lại mật khẩu</a></p>
                <p>Hoặc sử dụng mã: <strong>{$resetToken}</strong></p>
                <p>Link này có hiệu lực trong 1 giờ.</p>
                <p>Nếu bạn không yêu cầu khôi phục mật khẩu, vui lòng bỏ qua email này.</p>
                <p>Trân trọng,<br>{$appName}</p>
            ";
        }
        
        return "<p>Email template not found.</p>";
    }
    
    /**
     * Test email configuration
     */
    public function testConnection(): bool
    {
        try {
            return $this->mailer->smtpConnect();
        } catch (Exception $e) {
            error_log("SMTP connection test failed: " . $e->getMessage());
            return false;
        }
    }
}
