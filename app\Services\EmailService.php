<?php

namespace App\Services;

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class EmailService
{
    private static $instance = null;
    private $mailer;
    private $config;

    private function __construct() {
        $this->config = [
            'host' => getenv('MAIL_HOST') ?: 'smtp.gmail.com',
            'port' => getenv('MAIL_PORT') ?: 587,
            'username' => getenv('MAIL_USERNAME') ?: '',
            'password' => getenv('MAIL_PASSWORD') ?: '',
            'encryption' => getenv('MAIL_ENCRYPTION') ?: 'tls',
            'from_address' => getenv('MAIL_FROM_ADDRESS') ?: '',
            'from_name' => getenv('MAIL_FROM_NAME') ?: 'GMTool'
        ];

        $this->initializeMailer();
    }

    /**
     * Get singleton instance
     */
    public static function getInstance(): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize PHPMailer
     */
    private function initializeMailer(): void
    {
        $this->mailer = new PHPMailer(true);

        try {
            // Server settings
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['host'];
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['username'];
            $this->mailer->Password = $this->config['password'];
            $this->mailer->SMTPSecure = $this->config['encryption'];
            $this->mailer->Port = $this->config['port'];
            $this->mailer->CharSet = 'UTF-8';

            // Default sender
            $this->mailer->setFrom($this->config['from_address'], $this->config['from_name']);

        } catch (Exception $e) {
            throw new \Exception("Email configuration error: " . $e->getMessage());
        }
    }

    /**
     * Send email verification code
     */
    public function sendEmailVerification(string $email, string $verificationCode, string $username = ''): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);

            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Xác thực email - ' . $this->config['from_name'];

            // Load email template
            $template = $this->loadTemplate('email_verification', [
                'username' => $username ?: $email,
                'verification_code' => $verificationCode,
                'app_name' => $this->config['from_name'],
                'app_url' => getenv('APP_URL') ?: 'http://localhost'
            ]);

            $this->mailer->Body = $template;

            return $this->mailer->send();

        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Send password reset email
     */
    public function sendPasswordReset(string $email, string $resetToken, string $username = ''): bool
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->addAddress($email);

            $this->mailer->isHTML(true);
            $this->mailer->Subject = 'Khôi phục mật khẩu - ' . $this->config['from_name'];

            // Create reset URL pointing to account management domain
            $accountDomain = getenv('ACCOUNT_DOMAIN') ?: 'http://id.mtfgame.com';
            $resetUrl = $accountDomain . '/reset-password?token=' . $resetToken;

            // Load email template
            $template = $this->loadTemplate('password_reset', [
                'username' => $username ?: $email,
                'reset_url' => $resetUrl,
                'reset_token' => $resetToken,
                'app_name' => $this->config['from_name'],
                'app_url' => getenv('APP_URL') ?: 'http://localhost'
            ]);

            $this->mailer->Body = $template;

            return $this->mailer->send();

        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Load email template
     */
    private function loadTemplate(string $templateName, array $variables = []): string
    {
        $templatePath = __DIR__ . '/../../resources/email_templates/' . $templateName . '.php';

        if (!file_exists($templatePath)) {
            throw new \Exception("Email template not found: " . $templateName);
        }

        // Extract variables for use in template
        extract($variables);

        // Start output buffering
        ob_start();
        include $templatePath;
        $content = ob_get_clean();

        return $content;
    }

    /**
     * Test email configuration
     */
    public function testConnection(): bool
    {
        try {
            return $this->mailer->smtpConnect();
        } catch (Exception $e) {
            error_log("SMTP connection test failed: " . $e->getMessage());
            return false;
        }
    }
}
