<?php
namespace App\Core;

use PDO;
use PDOException;

class GameLogDatabase {
    private $connection;
    private $host;
    private $dbname;
    private $username;
    private $password;

    public function __construct($host, $dbname, $username, $password) {
        $this->host = $host;
        $this->dbname = $dbname;
        $this->username = $username;
        $this->password = $password;
    }

    public function connect() {
        try {
            if (!$this->connection) {
                $this->connection = new PDO(
                    "mysql:host={$this->host};dbname={$this->dbname};charset=utf8mb4",
                    $this->username,
                    $this->password,
                    [
                        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                        PDO::ATTR_EMULATE_PREPARES => false
                    ]
                );
            }
            return true;
        } catch (PDOException $e) {
            error_log("Game log database connection failed: " . $e->getMessage());
            return false;
        }
    }

    public function query($sql, $params = []) {
        try {
            if (!$this->connection && !$this->connect()) {
                return null;
            }
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log("Game log query failed: " . $e->getMessage());
            return null;
        }
    }

    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetch() : null;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt ? $stmt->fetchAll() : [];
    }

    /**
     * Lấy log item của người chơi
     * @param int $uid User ID
     * @param int $serverId Server ID
     * @param array $options Các tùy chọn tìm kiếm (log_type, action_type, item_id, từ ngày, đến ngày)
     * @param int $limit Giới hạn kết quả
     * @return array
     */
    public function getItemLogs($uid, $serverId, $options = [], $limit = 100) {
        $conditions = ['serverid = ?', 'uid = ?'];
        $params = [$serverId, $uid];

        if (!empty($options['log_type'])) {
            $conditions[] = 'log_type = ?';
            $params[] = $options['log_type'];
        }

        if (!empty($options['action_type'])) {
            $conditions[] = 'action_type = ?';
            $params[] = $options['action_type'];
        }

        if (!empty($options['item_id'])) {
            $conditions[] = 'item_id = ?';
            $params[] = $options['item_id'];
        }

        if (!empty($options['from_time'])) {
            $conditions[] = 'time >= ?';
            $params[] = $options['from_time'];
        }

        if (!empty($options['to_time'])) {
            $conditions[] = 'time <= ?';
            $params[] = $options['to_time'];
        }

        $sql = "SELECT * FROM item_log WHERE " . implode(' AND ', $conditions) . " 
                ORDER BY time DESC LIMIT ?";
        $params[] = $limit;

        return $this->fetchAll($sql, $params);
    }

    /**
     * Lấy log hero của người chơi
     * @param int $uid User ID
     * @param int $serverId Server ID
     * @param array $options Các tùy chọn tìm kiếm (log_type, action_type, hero_id, từ ngày, đến ngày)
     * @param int $limit Giới hạn kết quả
     * @return array
     */
    public function getHeroLogs($uid, $serverId, $options = [], $limit = 100) {
        $conditions = ['serverid = ?', 'uid = ?'];
        $params = [$serverId, $uid];

        if (!empty($options['log_type'])) {
            $conditions[] = 'log_type = ?';
            $params[] = $options['log_type'];
        }

        if (!empty($options['action_type'])) {
            $conditions[] = 'action_type = ?';
            $params[] = $options['action_type'];
        }

        if (!empty($options['hero_id'])) {
            $conditions[] = 'hero_id = ?';
            $params[] = $options['hero_id'];
        }

        if (!empty($options['from_time'])) {
            $conditions[] = 'time >= ?';
            $params[] = $options['from_time'];
        }

        if (!empty($options['to_time'])) {
            $conditions[] = 'time <= ?';
            $params[] = $options['to_time'];
        }

        $sql = "SELECT * FROM hero_add_log WHERE " . implode(' AND ', $conditions) . " 
                ORDER BY time DESC LIMIT ?";
        $params[] = $limit;

        return $this->fetchAll($sql, $params);
    }

    public function close() {
        $this->connection = null;
    }
} 