<?php
namespace Api\Services;

use <PERSON>reait\Firebase\Factory;
use Kreait\Firebase\Exception\Auth\FailedToVerifyToken;
use Google\Auth\Credentials\ServiceAccountCredentials;
use GuzzleHttp\Client;

class FirebaseService {
    private $auth;
    private $credentials;
    private $packageName;

    /**
     * 1. <PERSON><PERSON><PERSON> cấp quyền xác minh thanh toán google play, vào google play cấp quyền cho email của firebase
     * 2. Tiếp theo https://console.cloud.google.com 
     * 3. Chọn API và dịch vụ
     * 4. T<PERSON><PERSON> kiếm Google Play Android Developer API
     * 5. Nhấn vào "Bật"
     * 6. Tạo API key mới
     * 7. Lấy API key và thêm vào file firebase-credentials.json
     */
    public function __construct() {
        $factory = (new Factory)
            ->withServiceAccount(__DIR__ . '/../../data/firebase-credentials.json');
        
        $this->auth = $factory->createAuth();
        
        // Load credentials cho Google Play API
        $this->credentials = json_decode(
            file_get_contents(__DIR__ . '/../../data/firebase-credentials.json'),
            true
        );
        $this->packageName = getenv('ANDROID_PACKAGE_NAME');
    }

    public function verifyIdToken($idToken) {
        try {
            $verifiedToken = $this->auth->verifyIdToken($idToken);
            return [
                'uid' => $verifiedToken->claims()->get('sub'),
                'email' => $verifiedToken->claims()->get('email'),
                'verified' => true
            ];
        } catch (FailedToVerifyToken $e) {
            throw new \Exception('Token không hợp lệ: ' . $e->getMessage());
        }
    }

    /**
     * Xác minh và tiêu thụ giao dịch Google Play
     */
    public function verifyPlayPurchase($purchaseToken, $productId) {
        try {
            // Tạo Google API client
            $client = new Client();
            $scope = 'https://www.googleapis.com/auth/androidpublisher';
            
            // Lấy access token từ service account
            $serviceAccount = new ServiceAccountCredentials(
                $scope,
                $this->credentials
            );
            $accessToken = $serviceAccount->fetchAuthToken()['access_token'];

            // Verify purchase trước
            $response = $client->get(
                "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{$this->packageName}/purchases/products/{$productId}/tokens/{$purchaseToken}",
                [
                    'headers' => [
                        'Authorization' => "Bearer {$accessToken}",
                        'Accept' => 'application/json'
                    ]
                ]
            );

            $purchaseData = json_decode($response->getBody(), true);

            // Validate response
            if (!isset($purchaseData['purchaseState'])) {
                throw new \Exception('Invalid purchase data from Google Play');
            }

            // Kiểm tra trạng thái mua
            if ($purchaseData['purchaseState'] !== 0) { // 0 = Purchased
                throw new \Exception('Purchase is not in valid state');
            }

            // Kiểm tra trạng thái consume
            if (isset($purchaseData['consumptionState']) && $purchaseData['consumptionState'] === 1) {
                // Đã được consume trước đó
                return $purchaseData;
            }

            // Consume purchase nếu chưa được consume
            $consumeResponse = $client->post(
                "https://androidpublisher.googleapis.com/androidpublisher/v3/applications/{$this->packageName}/purchases/products/{$productId}/tokens/{$purchaseToken}:consume",
                [
                    'headers' => [
                        'Authorization' => "Bearer {$accessToken}",
                        'Accept' => 'application/json'
                    ]
                ]
            );

            if ($consumeResponse->getStatusCode() !== 200) {
                throw new \Exception('Failed to consume purchase');
            }

            return $purchaseData;

        } catch (\Exception $e) {
            error_log("Google Play API error: " . $e->getMessage());
            throw new \Exception('Lỗi xác thực với Google Play: ' . $e->getMessage());
        }
    }
} 