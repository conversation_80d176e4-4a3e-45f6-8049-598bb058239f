<?php
namespace App\Controllers;

use App\Core\Controller;
use Exception;

class ServerController extends Controller {

    private $serverConfig;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->serverConfig = require __DIR__ . '/../../config/server.php';
    }

    public function index() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('server.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        $perPage = $this->serverConfig['pagination']['per_page'];
        $maxLinks = $this->serverConfig['pagination']['max_links'];

        // Phân trang
        $page = max(1, intval($_GET['page'] ?? 1));
        $offset = ($page - 1) * $perPage;

        // Lấy tổng số máy chủ
        $total = (int)$this->db->fetch("SELECT COUNT(*) as total FROM game_servers")['total'];
        $totalPages = max(1, ceil($total / $perPage));
        $page = min($page, $totalPages);

        // Lấy danh sách máy chủ phân trang
        $servers = $this->db->fetchAll(
            "SELECT s1.*, s2.id as merged_into_id FROM game_servers s1 LEFT JOIN game_servers s2 ON s1.merged_into = s2.id ORDER BY s1.id ASC LIMIT $perPage OFFSET $offset"
        );
        
        // Kiểm tra quyền thêm/sửa/gộp
        $canCreate = $this->hasPermission('server.create');
        $canEdit = $this->hasPermission('server.edit');
        $canMerge = $this->hasPermission('server.merge');

        // Truyền cấu hình trạng thái sang view
        $serverStatus = $this->serverConfig['server_status'];
        
        // Lấy trạng thái toàn máy chủ từ config
        $serverStatusConfig = $this->db->getConfigByKey('server_status');
        $isGlobalMaintenance = ($serverStatusConfig && $serverStatusConfig['type'] === 'boolean' && $serverStatusConfig['config_value'] == '1');

        // Truyền biến phân trang sang view
        $pagination = [
            'current' => $page,
            'total' => $totalPages,
            'per_page' => $perPage,
            'total_items' => $total,
            'max_links' => $maxLinks
        ];
        
        $now = time();
        
        require_once __DIR__ . '/../../resources/views/server/index.php';
    }
    
    public function create() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('server.create')) {
            $this->redirectToFirstAccessiblePage();
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = trim($_POST['name'] ?? '');
            $ip = trim($_POST['ip'] ?? '');
            $port = intval($_POST['port'] ?? 0);
            $opentime = trim($_POST['opentime'] ?? '');
            $mysql_ip = trim($_POST['mysql_ip'] ?? '');
            $mysql_db = trim($_POST['mysql_db'] ?? '');
            $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
            
            // Validate
            $errors = [];
            if (empty($name)) $errors[] = "Tên máy chủ không được để trống";
            if (empty($ip)) $errors[] = "IP không được để trống";
            if ($port <= 0) $errors[] = "Port không hợp lệ";
            if (empty($opentime)) $errors[] = "Thời gian mở máy chủ không được để trống";
            
            // Kiểm tra IP:Port có mở không
            if (!empty($ip) && $port > 0) {
                $fp = @fsockopen($ip, $port, $errno, $errstr, 2);
                if (!$fp) {
                    $errors[] = "Không thể kết nối tới IP:Port $ip:$port";
                } else {
                    fclose($fp);
                }
            }
            // Kiểm tra kết nối MySQL
            if (!empty($mysql_ip) && !empty($mysql_db)) {
                $mysql_user = getenv('GAME_DB_USER');
                $mysql_pass = getenv('GAME_DB_PASS');
                try {
                    $pdo = new \PDO("mysql:host=$mysql_ip;dbname=$mysql_db", $mysql_user, $mysql_pass, [\PDO::ATTR_TIMEOUT => 2]);
                } catch (\PDOException $e) {
                    $errors[] = "Không thể kết nối MySQL: " . $e->getMessage();
                }
            }
            
            if (empty($errors)) {
                try {
                    $this->db->insert('game_servers', [
                        'name' => $name,
                        'ip' => $ip,
                        'port' => $port,
                        'opentime' => $opentime,
                        'mysql_ip' => $mysql_ip,
                        'mysql_db' => $mysql_db,
                        'status' => $status
                    ]);
                    
                    $_SESSION['success'] = 'Thêm máy chủ thành công';
                    header('Location: /public/?route=server');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
                }
            }
        } else {
            // Lấy ID máy chủ lớn nhất hiện tại để hiển thị ID mới
            $maxId = $this->db->query("SELECT MAX(id) as max_id FROM game_servers")->fetch()['max_id'] ?? 0;
            $newId = $maxId + 1;
            $_POST['server_id'] = $newId;
        }
        $serverStatus = $this->serverConfig['server_status'];
        $GAME_DB_IPS = getenv('GAME_DB_IPS') ? explode(',', getenv('GAME_DB_IPS')) : [];
        require_once __DIR__ . '/../../resources/views/server/create.php';
    }
    
    public function edit() {
        $this->requireLogin();

        if (!$this->hasPermission('server.edit')) {
            $this->redirectToFirstAccessiblePage();
        }
        
        $id = $_GET['id'] ?? 0;
        $server = $this->db->getServerById($id);
        
        if (!$server) {
            $_SESSION['error'] = 'Máy chủ không tồn tại';
            header('Location: /public/?route=server');
            exit;
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = trim($_POST['name'] ?? '');
            $ip = trim($_POST['ip'] ?? '');
            $port = intval($_POST['port'] ?? 0);
            $opentime = trim($_POST['opentime'] ?? '');
            $mysql_ip = trim($_POST['mysql_ip'] ?? '');
            $mysql_db = trim($_POST['mysql_db'] ?? '');
            $status = isset($_POST['status']) ? intval($_POST['status']) : 1;
            
            // Validate
            $errors = [];
            if (empty($name)) $errors[] = "Tên máy chủ không được để trống";
            if (empty($ip)) $errors[] = "IP không được để trống";
            if ($port <= 0) $errors[] = "Port không hợp lệ";
            if (empty($opentime)) $errors[] = "Thời gian mở máy chủ không được để trống";
            
            // Kiểm tra IP:Port có mở không
            if (!empty($ip) && $port > 0) {
                $fp = @fsockopen($ip, $port, $errno, $errstr, 2);
                if (!$fp) {
                    $errors[] = "Không thể kết nối tới IP:Port $ip:$port";
                } else {
                    fclose($fp);
                }
            }
            // Kiểm tra kết nối MySQL
            if (!empty($mysql_ip) && !empty($mysql_db)) {
                $mysql_user = getenv('GAME_DB_USER');
                $mysql_pass = getenv('GAME_DB_PASS');
                try {
                    $pdo = new \PDO("mysql:host=$mysql_ip;dbname=$mysql_db", $mysql_user, $mysql_pass, [\PDO::ATTR_TIMEOUT => 2]);
                } catch (\PDOException $e) {
                    $errors[] = "Không thể kết nối MySQL: " . $e->getMessage();
                }
            }
            
            if (empty($errors)) {
                try {
                    $this->db->update('game_servers', [
                        'name' => $name,
                        'ip' => $ip,
                        'port' => $port,
                        'opentime' => $opentime,
                        'mysql_ip' => $mysql_ip,
                        'mysql_db' => $mysql_db,
                        'status' => $status
                    ], 'id = ?', [$id]);
                    
                    $_SESSION['success'] = 'Cập nhật máy chủ thành công';
                    header('Location: /public/?route=server');
                    exit;
                } catch (Exception $e) {
                    $errors[] = 'Có lỗi xảy ra: ' . $e->getMessage();
                }
            }
        }
        $serverStatus = $this->serverConfig['server_status'];
        $GAME_DB_IPS = getenv('GAME_DB_IPS') ? explode(',', getenv('GAME_DB_IPS')) : [];

        require_once __DIR__ . '/../../resources/views/server/edit.php';
    }
    
    public function merge() {
        $this->requireLogin();

        if (!$this->hasPermission('server.merge')) {
            $this->redirectToFirstAccessiblePage();
        }
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $mergeData = json_decode($_POST['merge_data'] ?? '[]', true);

            if (empty($mergeData)) {
                $_SESSION['error'] = 'Không có dữ liệu gộp máy chủ';
                header('Location: /public/?route=server');
                exit;
            }

            try {
                // Nếu đã có transaction thì rollback trước
                if (method_exists($this->db, 'inTransaction') && $this->db->inTransaction()) {
                    $this->db->rollBack();
                }
                $this->db->beginTransaction();

                foreach ($mergeData as $merge) {
                    $targetId = intval($merge['target_id']);
                    $sourceIds = array_map('intval', $merge['source_ids']);

                    $targetServer = $this->db->getServerById($targetId);
                    if (!$targetServer) {
                        throw new Exception("Máy chủ đích không tồn tại");
                    }

                    foreach ($sourceIds as $sourceId) {
                        $sourceServer = $this->db->getServerById($sourceId);
                        if (!$sourceServer) {
                            throw new Exception("Máy chủ nguồn không tồn tại");
                        }

                        $this->db->mergeServers($sourceId, $targetId);

                        $this->db->log('server_merge', 'success', [
                            'source_id' => $sourceId,
                            'target_id' => $targetId
                        ]);
                    }
                }

                $this->db->commit();
                $_SESSION['success'] = 'Gộp máy chủ thành công';
            } catch (Exception $e) {
                if (method_exists($this->db, 'inTransaction') && $this->db->inTransaction()) {
                    $this->db->rollBack();
                }
                $_SESSION['error'] = 'Có lỗi xảy ra: ' . $e->getMessage();
            }

            header('Location: /public/?route=server');
            exit;
        }

        // Lấy tất cả máy chủ
        $serversFlat = $this->db->getServers(true);

        // Xây dựng cây
        $servers = $this->buildServerTree($serversFlat);


        $serverStatus = $this->serverConfig['server_status'];
        require_once __DIR__ . '/../../resources/views/server/merge.php';
    }

    // Hàm xây dựng cây máy chủ từ danh sách phẳng
    private function buildServerTree($serversFlat) {
        // Tạo mảng tạm để tra cứu nhanh
        $items = [];
        foreach ($serversFlat as $server) {
            $items[$server['id']] = [
                'id' => (string)$server['id'],
                'text' => $server['name'] . ' (ID: ' . $server['id'] . ')',
                'children' => [],
                'parent' => $server['merged_into'] ? (string)$server['merged_into'] : null
            ];
        }

        // Build cây
        $tree = [];
        foreach ($items as $id => &$item) {
            if ($item['parent'] && isset($items[$item['parent']])) {
                $items[$item['parent']]['children'][] = &$item;
            } else {
                $tree[] = &$item;
            }
        }
        // Xóa key 'parent' để jsTree không bị lỗi
        $this->removeParentKey($tree);
        return $tree;
    }

    private function removeParentKey(&$nodes) {
        foreach ($nodes as &$node) {
            unset($node['parent']);
            if (!empty($node['children'])) {
                $this->removeParentKey($node['children']);
            }
        }
    }
} 