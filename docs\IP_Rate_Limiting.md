# IP Rate Limiting Documentation

## Tổng quan

Hệ thống IP Rate Limiting được triển khai để bảo vệ các endpoint public khỏi spam và abuse. Khác với user-based rate limiting, IP rate limiting hoạt động dựa trên địa chỉ IP của client.

## Cấu hình Rate Limiting

### File cấu hình: `config/api.php`

```php
'authentication' => [
    // Rate limiting theo user (cho endpoint cần auth)
    'rate_limits' => [
        'send_phone_verification' => 1,   // 1 lần/phút
        'send_email_verification' => 1,   // 1 lần/phút
        'verify_phone_otp' => 5,          // 5 lần/phút
        'verify_email' => 5,              // 5 lần/phút
        'change_password' => 3,           // 3 lần/phút
        'default' => 120                   // 120 lần/phút (mặc định)
    ],

    // Rate limiting theo IP (cho endpoint public)
    'ip_rate_limits' => [
        'send_phone_verification' => 3,   // 3 lần/phút per IP
        'send_email_verification' => 3,   // 3 lần/phút per IP
        'forgot_password' => 5,           // 5 lần/phút per IP
        'reset_password' => 10,           // 10 lần/phút per IP
        'register' => 10,                 // 10 lần/phút per IP
        'login' => 20,                    // 20 lần/phút per IP
        'default' => 60                   // 60 lần/phút per IP (mặc định)
    ]
]
```

## Endpoints được bảo vệ

### 1. sendEmailVerification
- **Rate Limit**: 3 requests/minute per IP
- **Endpoint**: `/api/GameUser/SendEmailVerification`
- **Authentication**: Required
- **IP Rate Limiting**: Áp dụng thêm để chống spam

### 2. sendPhoneVerification  
- **Rate Limit**: 3 requests/minute per IP
- **Endpoint**: `/api/GameUser/SendPhoneVerification`
- **Authentication**: Required
- **IP Rate Limiting**: Áp dụng thêm để chống spam

### 3. forgotPassword
- **Rate Limit**: 5 requests/minute per IP
- **Endpoint**: `/api/GameUser/ForgotPassword`
- **Authentication**: Not required (public endpoint)
- **IP Rate Limiting**: Primary protection

### 4. resetPassword
- **Rate Limit**: 10 requests/minute per IP
- **Endpoint**: `/api/GameUser/ResetPassword`
- **Authentication**: Not required (public endpoint)
- **IP Rate Limiting**: Primary protection

## Cách hoạt động

### 1. IP Detection
```php
// Lấy IP address từ các headers
$validator = Validator::getInstance();
$clientIp = $validator->getIp();

// Ưu tiên headers:
// 1. HTTP_X_FORWARDED_FOR
// 2. HTTP_X_REAL_IP  
// 3. HTTP_CLIENT_IP
// 4. REMOTE_ADDR
```

### 2. Redis Key Structure
```
ip_rate_limit:{endpoint_name}:{client_ip}
```

Ví dụ:
- `ip_rate_limit:forgot_password:*************`
- `ip_rate_limit:send_email_verification:***********`

### 3. Sliding Window Algorithm
- Sử dụng Redis Sorted Sets
- Window size: 60 giây
- Tự động cleanup các entries cũ
- Atomic operations để đảm bảo consistency

## Implementation

### IpRateLimit Trait

```php
// File: api/Core/IpRateLimit.php
trait IpRateLimit
{
    protected function checkIpRateLimit(string $endpointName): void
    {
        // Lấy cấu hình limit
        $authConfig = $this->config['api']['authentication'] ?? [];
        $ipRateLimits = $authConfig['ip_rate_limits'] ?? [];
        $limit = $ipRateLimits[$endpointName] ?? $ipRateLimits['default'] ?? 60;
        
        // Lấy IP và tạo Redis key
        $validator = Validator::getInstance();
        $clientIp = $validator->getIp();
        $redisKey = "ip_rate_limit:{$endpointName}:{$clientIp}";
        
        // Kiểm tra với Redis
        $redisService = RedisService::getInstance();
        $result = $redisService->checkRateLimit($redisKey, $limit, 60);
        
        if (!$result['allowed']) {
            // Trả về HTTP 429 Too Many Requests
            $response = [
                'status' => false,
                'message' => "Bạn đã vượt quá giới hạn sử dụng từ IP này...",
                'error_code' => 'RATE_LIMIT_EXCEEDED',
                'retry_after' => $result['reset_time'] - time()
            ];
            $this->jsonResponse($response, 429);
            exit;
        }
    }
}
```

### Usage trong Controller

```php
class GameUserController extends Controller {
    use ApiHandler, IpRateLimit;
    
    public function sendEmailVerification() {
        return $this->apiEndpoint('send_email_verification', function($validator) {
            // Kiểm tra IP rate limiting
            $this->checkIpRateLimit('send_email_verification');
            
            // Tiếp tục xử lý logic...
        });
    }
    
    public function forgotPassword() {
        return $this->apiEndpoint('forgot_password', function($validator) {
            // Kiểm tra IP rate limiting cho endpoint public
            $this->checkIpRateLimit('forgot_password');
            
            // Tiếp tục xử lý logic...
        });
    }
}
```

## Error Response

Khi vượt quá rate limit, API sẽ trả về:

```json
{
    "status": false,
    "message": "Bạn đã vượt quá giới hạn sử dụng từ IP này. Vui lòng thử lại sau 2025-05-30 21:30:00",
    "error_code": "RATE_LIMIT_EXCEEDED",
    "retry_after": 45
}
```

- **HTTP Status**: 429 Too Many Requests
- **retry_after**: Số giây cần chờ trước khi thử lại
- **message**: Thông báo chi tiết với thời gian có thể thử lại

## Testing

### Test Script: `test_rate_limit.php`

```bash
php test_rate_limit.php
```

Script sẽ test:
1. forgotPassword rate limiting (5 requests/minute)
2. resetPassword rate limiting (10 requests/minute)
3. Hiển thị kết quả và HTTP status codes

### Manual Testing

```bash
# Test forgotPassword endpoint
for i in {1..7}; do
  curl -X POST http://localhost/api/GameUser/ForgotPassword \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>"}' \
    -w "HTTP %{http_code}\n"
  sleep 0.1
done
```

## Monitoring và Debugging

### Redis Keys Monitoring

```bash
# Xem tất cả IP rate limit keys
redis-cli KEYS "ip_rate_limit:*"

# Xem chi tiết một key
redis-cli ZRANGE "ip_rate_limit:forgot_password:*************" 0 -1 WITHSCORES

# Xóa rate limit cho một IP (để test)
redis-cli DEL "ip_rate_limit:forgot_password:*************"
```

### Log Files

Rate limiting errors được log vào PHP error log:

```
Redis IP rate limit failed: Connection refused
```

## Best Practices

### 1. Cấu hình Limits hợp lý
- **Email/SMS endpoints**: 1-3 requests/minute (tránh spam)
- **Password reset**: 5-10 requests/minute (cho phép retry)
- **Login/Register**: 10-20 requests/minute (user experience)

### 2. Error Handling
- Luôn có fallback khi Redis fail
- Không block requests nếu có lỗi hệ thống
- Log errors để monitoring

### 3. IP Detection
- Hỗ trợ proxy headers (X-Forwarded-For, X-Real-IP)
- Validate IP addresses
- Handle IPv6 addresses

### 4. Security Considerations
- Rate limiting không thay thế authentication
- Kết hợp với other security measures
- Monitor cho distributed attacks

## Troubleshooting

### Common Issues

1. **Rate limiting không hoạt động**
   - Kiểm tra Redis connection
   - Verify trait được import đúng
   - Check method được gọi trong endpoint

2. **False positives**
   - Kiểm tra IP detection logic
   - Verify proxy configuration
   - Check for shared IP addresses

3. **Performance issues**
   - Monitor Redis memory usage
   - Optimize key expiration
   - Consider Redis clustering

### Debug Commands

```php
// Kiểm tra IP được detect
$validator = Validator::getInstance();
echo "Client IP: " . $validator->getIp();

// Kiểm tra rate limit info
$info = $this->getIpRateLimitInfo('forgot_password');
var_dump($info);
```
