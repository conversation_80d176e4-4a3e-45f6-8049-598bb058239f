<?php
/**
 * Test script for EmailService
 * Run this script to test email functionality
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load .env configuration
$envPath = __DIR__;
$dotenv = Dotenv\Dotenv::createUnsafeImmutable($envPath);
$dotenv->load();

use App\Services\EmailService;

try {
    echo "Testing EmailService...\n";
    
    // Get EmailService instance
    $emailService = EmailService::getInstance();
    
    // Test SMTP connection
    echo "Testing SMTP connection...\n";
    if ($emailService->testConnection()) {
        echo "✅ SMTP connection successful!\n";
    } else {
        echo "❌ SMTP connection failed!\n";
        exit(1);
    }
    
    // Test email verification
    echo "\nTesting email verification...\n";
    $testEmail = '<EMAIL>'; // Change this to your test email
    $verificationCode = '123456';
    
    if ($emailService->sendEmailVerification($testEmail, $verificationCode, 'TestUser')) {
        echo "✅ Email verification sent successfully!\n";
    } else {
        echo "❌ Failed to send email verification!\n";
    }
    
    // Test password reset
    echo "\nTesting password reset...\n";
    $resetToken = 'test_reset_token_123456789';
    
    if ($emailService->sendPasswordReset($testEmail, $resetToken, 'TestUser')) {
        echo "✅ Password reset email sent successfully!\n";
    } else {
        echo "❌ Failed to send password reset email!\n";
    }
    
    echo "\n✅ All tests completed!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
