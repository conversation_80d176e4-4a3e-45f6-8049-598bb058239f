<?php
namespace App\Middleware;
class SecurityMiddleware {
    public function checkCSRF() {
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
                http_response_code(403);
                die('CSRF token validation failed');
            }
        }
    }

    public function checkIP() {
        $allowedIPs = explode(',', getenv('ALLOWED_IPS'));
        $clientIP = $_SERVER['REMOTE_ADDR'];
        if (!empty($allowedIPs[0])) {
            $allowed = false;
            foreach ($allowedIPs as $ip) {
                if ($this->ipMatch($clientIP, trim($ip))) {
                    $allowed = true;
                    break;
                }
            }
            
            if (!$allowed) {
                http_response_code(403);
                die('Access denied from your IP');
            }
        }
    }

    private function ipMatch($ip, $pattern) {
        $pattern = str_replace('*', '.*', $pattern);
        return preg_match('/^' . $pattern . '$/', $ip);
    }

    public function generateCSRFToken() {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }

    public function checkBruteForce($username) {
        $attempts = $_SESSION['login_attempts'][$username] ?? 0;
        $maxAttempts = getenv('MAX_LOGIN_ATTEMPTS', 5);
        $timeout = getenv('LOGIN_TIMEOUT', 300); // 5 minutes
        
        if ($attempts >= $maxAttempts) {
            $lastAttempt = $_SESSION['last_attempt'][$username] ?? 0;
            if (time() - $lastAttempt < $timeout) {
                return false;
            }
            // Reset after timeout
            unset($_SESSION['login_attempts'][$username]);
            unset($_SESSION['last_attempt'][$username]);
        }
        return true;
    }

    public function logLoginAttempt($username, $success = false) {
        if (!$success) {
            $_SESSION['login_attempts'][$username] = ($_SESSION['login_attempts'][$username] ?? 0) + 1;
            $_SESSION['last_attempt'][$username] = time();
        } else {
            unset($_SESSION['login_attempts'][$username]);
            unset($_SESSION['last_attempt'][$username]);
        }
    }
} 