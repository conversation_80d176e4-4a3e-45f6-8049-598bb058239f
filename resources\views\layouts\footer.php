            </main>
        </div>
    </div>
    
    <script>
        // Mobile menu toggle
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        const sidebar = document.getElementById('sidebar');
        
        mobileMenuButton.addEventListener('click', () => {
            sidebar.classList.toggle('-translate-x-full');
        });
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', (e) => {
            if (window.innerWidth < 768 && 
                !sidebar.contains(e.target) && 
                !mobileMenuButton.contains(e.target)) {
                sidebar.classList.add('-translate-x-full');
            }
        });
        
        // Responsive tables
        document.querySelectorAll('table').forEach(table => {
            const wrapper = document.createElement('div');
            wrapper.className = 'overflow-x-auto';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        });

        // Hàm hiển thị thông báo toast
        function showToastMessage(type, message) {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
            })

            Toast.fire({
                icon: type,
                title: message
            })
        }

        // ====== Gửi request lấy thông tin item và hiển thị modal ======
        function requestAndShowItemInfo(listItem) {
            if (!listItem) {
                showToastMessage('error', 'Vui lòng nhập danh sách item hợp lệ!');
                return;
            }

            fetch('?route=item&action=apiGetByIds', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'ids=' + encodeURIComponent(listItem) + '&csrf_token=' + encodeURIComponent('<?php echo $_SESSION['csrf_token']; ?>')
            })
            .then(res => res.json())
            .then(res => {
                if (res.status) {
                    showItemInfoModal(res.data, res.ids);
                } else {
                    showToastMessage('error', res.message);
                }
            })
            .catch(() => showToastMessage('error', 'Lỗi khi lấy thông tin vật phẩm!'));
        }

        // ====== Hiển thị modal thông tin danh sách item theo input ======
        function showItemInfoModal(listItem, ids) {
            let oldModal = document.getElementById('item-info-modal');
            if (oldModal) oldModal.remove();
            let modal = document.createElement('div');
            modal.id = 'item-info-modal';
            modal.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40';
            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full p-6 relative">
                    <button type="button" id="close-item-info-modal" class="absolute top-2 right-2 text-gray-400 hover:text-gray-700 text-xl">&times;</button>
                    <h4 class="text-lg font-bold mb-4">Thông tin vật phẩm</h4>
                    <div class="mb-4 p-2 bg-gray-50 rounded">
                        <span class="font-medium">Danh sách ID gốc:</span>
                        <span class="text-gray-600 ml-2">${ids}</span>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full border text-sm">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="px-2 py-1 border">Icon</th>
                                    <th class="px-2 py-1 border">ID</th>
                                    <th class="px-2 py-1 border">Tên</th>
                                    <th class="px-2 py-1 border">Loại</th>
                                    <th class="px-2 py-1 border">Số lượng</th>
                                    <th class="px-2 py-1 border">Cảnh báo</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${listItem.map(item => {
                                    const res = item.res || 'equipped';
                                    const iconId = item.icon_id || 'default';
                                    const iconBaseUrl = '<?= rtrim(getenv('APP_CLIENT_DOMAIN'), '/') ?>/resource/eui/item/' + res + '/';
                                    const iconUrl = iconBaseUrl + iconId + '.png';
                                    const heroHoleUrl = '<?= rtrim(getenv('APP_CLIENT_DOMAIN'), '/') ?>/resource/eui/hero/heroIcon_hole/' + iconId + '.png';
                                    
                                    // Đánh giá số lượng
                                    let amount = parseInt(item.amount) || 0;
                                    let warning = '';
                                    let warningClass = '';
                                    
                                    if (amount > 100000) {
                                        warning = '<span class="text-red-600 font-bold">Số lớn, xem kỹ đi</span>';
                                        warningClass = 'shake-error';
                                    } else if (amount > 10000) {
                                        warning = '<span class="text-orange-500">Hơi nhiều</span>';
                                        warningClass = 'shake-warning';
                                    }else {
                                        warning = '<span class="text-green-600">Không ý kiến</span>';
                                        warningClass = '';
                                    }
                                    
                                    return `
                                    <tr class="${warningClass}">
                                        <td class="border px-2 py-1 text-center">
                                            <img src="${iconUrl}"
                                                 alt="${item.name || ''}"
                                                 class="h-16 w-16 object-contain mx-auto rounded border border-gray-200 bg-gray-50"
                                                 onerror="if(this.src!=='${heroHoleUrl}' && '${res}'==='fragment'){this.src='${heroHoleUrl}';}else{this.style.display='none';}">
                                        </td>
                                        <td class="border px-2 py-1">${item.id || ''}</td>
                                        <td class="border px-2 py-1">${item.name || ''}</td>
                                        <td class="border px-2 py-1">${item.category_name || ''}</td>
                                        <td class="border px-2 py-1">${item.amount || ''}</td>
                                        <td class="border px-2 py-1">${warning}</td>
                                    </tr>
                                `}).join('')}
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4 flex justify-end">
                        <button type="button" id="close-item-info-modal2" class="px-4 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700">Đóng</button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            document.getElementById('close-item-info-modal').onclick = () => modal.remove();
            document.getElementById('close-item-info-modal2').onclick = () => modal.remove();
            modal.onclick = function(e) { if (e.target === modal) modal.remove(); };
        }

        // Thêm style cho hiệu ứng rung lắc
        const style = document.createElement('style');
        style.textContent = `
            .shake-warning {
                background-color: rgba(255, 0, 0, 0.02);
            }
            .shake-error {
                background-color: rgba(255, 0, 0, 0.05);
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 