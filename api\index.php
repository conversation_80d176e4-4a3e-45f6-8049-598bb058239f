<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/Core/Database.php';

// Load .env configuration
$envPath = dirname(__DIR__);
$dotenv = Dotenv\Dotenv::createUnsafeImmutable($envPath);
$dotenv->load();

// Thiết lập múi giờ từ .env (nếu có)
$timezone = getenv('APP_TIMEZONE') ?: 'Asia/Ho_Chi_Minh';
date_default_timezone_set($timezone);

// Error reporting
if ((getenv('APP_DEBUG') ?? '') === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Load configurations
$config = [
    'database' => require __DIR__ . '/../config/database.php',
    'api' => require __DIR__ . '/../config/api.php',
];

// Initialize database connection
try {
    $db = App\Core\Database::getInstance($config['database']);
} catch (Exception $e) {
    die("Connection failed: " . $e->getMessage());
}

// Kiểm tra IP
$clientIp = $_SERVER['REMOTE_ADDR'];
$blockedIps = $config['api']['settings']['blocked_ips'] ?? [];
if (in_array($clientIp, $blockedIps)) {
    http_response_code(200);
    header('Content-Type: application/json');
    echo json_encode(['status' => false, 'message' => 'IP is blocked']);
    exit;
}

// Lấy URI và chuyển thành route
$requestUri = $_SERVER['REQUEST_URI'];
$basePath = '/api/';
$route = str_replace($basePath, '', $requestUri);
$route = explode('?', $route)[0]; // Loại bỏ query string

// Phân tích route thành controller và method
$parts = explode('/', trim($route, '/'));
$controllerName = ucfirst($parts[0] ?? '') . 'Controller';
$methodName = strtolower($parts[1] ?? 'index');

// Kiểm tra file controller tồn tại
$controllerFile = __DIR__ . '/Controllers/' . $controllerName . '.php';
if (!file_exists($controllerFile)) {
    http_response_code(200);
    echo json_encode(['status' => false, 'message' => 'Controller not found']);
    exit;
}

// Load controller
require_once $controllerFile;
$controllerClass = 'Api\\Controllers\\' . $controllerName;

// Kiểm tra method tồn tại
if (!method_exists($controllerClass, $methodName)) {
    http_response_code(200);
    echo json_encode(['status' => false, 'message' => 'Method not found']);
    exit;
}

// Khởi tạo controller và gọi method
$controller = new $controllerClass($db, $config);
$controller->$methodName(); 