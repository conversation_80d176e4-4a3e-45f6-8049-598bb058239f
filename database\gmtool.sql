-- Create database
CREATE DATABASE IF NOT EXISTS gmtool CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gmtool;

SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- Đặt collation mặc định
SET collation_connection = utf8mb4_unicode_ci;

-- GM users table
CREATE TABLE IF NOT EXISTS gm_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    role ENUM('super_admin', 'admin', 'mod', 'viewer') NOT NULL,
    email VARCHAR(100),
    last_login DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status TINYINT(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Game users table
-- Bảng game_users: <PERSON><PERSON><PERSON> trữ thông tin người dùng game
-- Các trường chính:
-- id: Khóa chính tự tăng
-- user_id: ID định danh người dùng (unique)
-- username: Tên đăng nhập
-- password: Mật khẩu (có thể null cho tài khoản OAuth)
-- email: Email người dùng
-- phone: Số điện thoại
-- full_name: Họ tên đầy đủ
-- address: Địa chỉ
-- date_of_birth: Ngày sinh
-- id_number: Số CMND/CCCD
-- id_issue_date: Ngày cấp CMND/CCCD
-- id_issue_place: Nơi cấp CMND/CCCD
-- gender: Giới tính (male/female/other)
-- is_email_verified: Trạng thái xác thực email (0/1)
-- is_phone_verified: Trạng thái xác thực số điện thoại (0/1)
-- oauth_id: ID từ dịch vụ OAuth (Google/Facebook/Apple)
-- is_gm: Có phải là GM không (0/1)
-- register_source: Nguồn đăng ký
-- register_info: Thông tin đăng ký dạng JSON
-- user_type: Loại tài khoản (google/facebook/apple/guest/account)
-- last_login: Thời gian đăng nhập cuối
-- last_login_servers: Danh sách server đã đăng nhập dạng JSON
-- balance: Số dư tài khoản
-- status: Trạng thái tài khoản (1: active, 0: inactive)
-- notes: Ghi chú
-- created_at: Thời gian tạo
-- updated_at: Thời gian cập nhật

CREATE TABLE IF NOT EXISTS game_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL UNIQUE,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(255),
    email VARCHAR(100),
    phone VARCHAR(20),
    full_name VARCHAR(100),
    address TEXT,
    date_of_birth DATE,
    id_number VARCHAR(50),
    id_issue_date DATE,
    id_issue_place VARCHAR(100),
    gender ENUM('male', 'female', 'other') DEFAULT 'male',
    is_email_verified TINYINT(1) DEFAULT 0,
    is_phone_verified TINYINT(1) DEFAULT 0,
    oauth_id VARCHAR(100),
    is_gm TINYINT(1) DEFAULT 0,
    register_source VARCHAR(50),
    register_info JSON,
    user_type ENUM('google', 'facebook', 'apple', 'guest', 'account') NOT NULL DEFAULT 'account',
    last_login DATETIME,
    last_login_servers JSON,
    balance INT DEFAULT 0,
    status TINYINT(1) DEFAULT 1,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Access tokens table
CREATE TABLE IF NOT EXISTS access_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    token VARCHAR(255) NOT NULL,
    refresh_token VARCHAR(255),
    expires_at TIMESTAMP NOT NULL,
    device_info JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_token (token),
    INDEX idx_refresh_token (refresh_token)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Bảng xác thực điện thoại
CREATE TABLE `phone_verifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `otp` varchar(6) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_phone` (`user_id`,`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Bảng xác thực email
CREATE TABLE IF NOT EXISTS email_verifications (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    user_id BIGINT(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    verification_code VARCHAR(6) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    KEY idx_user_email (user_id, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Payment transactions table
CREATE TABLE IF NOT EXISTS payment_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(200) NOT NULL UNIQUE,
    client_order_id VARCHAR(200) DEFAULT NULL,
    user_id VARCHAR(50) NOT NULL,
    game_uid VARCHAR(50) NOT NULL,
    game_level INT NOT NULL,
    package_id VARCHAR(50) NOT NULL,
    package_count INT NOT NULL DEFAULT 1,
    server_id VARCHAR(50) NOT NULL,
    amount INT NOT NULL,
    source ENUM('ios', 'android', 'web', 'facebook', 'google', 'guest') NOT NULL,
    status ENUM('pending', 'success', 'error') NOT NULL,
    msg TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- System logs table
CREATE TABLE IF NOT EXISTS gm_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    ip_address VARCHAR(45),
    data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES gm_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- API logs table
CREATE TABLE IF NOT EXISTS api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(100) NOT NULL,
    method VARCHAR(10) NOT NULL,
    request_data JSON,
    response_data JSON,
    ip_address VARCHAR(45),
    status_code INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- User notes table
CREATE TABLE IF NOT EXISTS user_notes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    note TEXT NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES gm_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Statistics table
CREATE TABLE IF NOT EXISTS statistics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL,
    new_users INT DEFAULT 0,
    active_users INT DEFAULT 0,
    total_revenue DECIMAL(15,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY date_idx (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Game servers table
CREATE TABLE IF NOT EXISTS game_servers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    ip VARCHAR(45) NOT NULL,
    port INT NOT NULL,
    opentime DATETIME NOT NULL,
    mysql_ip VARCHAR(45),
    mysql_db VARCHAR(100),
    merged_into INT DEFAULT NULL,
    status TINYINT(1) DEFAULT 1 COMMENT '1: hoạt động, 0: bảo trì',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (merged_into) REFERENCES game_servers(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Bảng cấu hình hệ thống
CREATE TABLE IF NOT EXISTS config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT NOT NULL,
    description VARCHAR(255),
    type VARCHAR(20) NOT NULL DEFAULT 'text',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- API logs table
CREATE TABLE IF NOT EXISTS api_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    endpoint VARCHAR(100) NOT NULL,
    method VARCHAR(10) NOT NULL,
    request_data JSON,
    response_data JSON,
    ip_address VARCHAR(45),
    status_code INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default super admin
INSERT INTO gm_users (username, password, role, email) VALUES
('asdasd22', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', '<EMAIL>');

-- New table for gift code groups
CREATE TABLE IF NOT EXISTS gift_code_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_name VARCHAR(100) NOT NULL,
    type ENUM('single', 'common') NOT NULL, -- single: code riêng, common: code chung
    code_prefix VARCHAR(50),                -- tiền tố cho code random
    code_value VARCHAR(100),                -- code chung (nếu là common)
    max_use INT DEFAULT 1,                  -- số lần sử dụng cho code chung
    items Text NOT NULL,                    -- danh sách item (id:qty,id2:qty,...)
    is_disabled TINYINT(1) DEFAULT 0,       -- 1: tắt nhóm code, 0: bật nhóm code
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expired_at TIMESTAMP NULL DEFAULT NULL  -- thời hạn sử dụng của nhóm code
);

-- New table for gift codes
CREATE TABLE IF NOT EXISTS gift_codes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    code VARCHAR(100) NOT NULL UNIQUE,
    is_used TINYINT(1) DEFAULT 0,
    used_by INT DEFAULT NULL,
    used_at TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES gift_code_groups(id)
);

-- New table for gift code claims
CREATE TABLE IF NOT EXISTS gift_code_claims (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    code_id INT DEFAULT NULL,
    code VARCHAR(100) NOT NULL,
    user_id VARCHAR(50) NOT NULL,
    role_id VARCHAR(50) NOT NULL,
    server_id VARCHAR(50) NOT NULL,
    claimed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES gift_code_groups(id)
);

-- Bảng lệnh game
CREATE TABLE IF NOT EXISTS game_commands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    server_id INT NOT NULL,
    role_id VARCHAR(50) NOT NULL,
    command VARCHAR(100) NOT NULL,
    params TEXT,
    status ENUM('pending', 'executed', 'failed') DEFAULT 'pending',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    executed_at TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (created_by) REFERENCES gm_users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;


-- Bảng thống kê online role
CREATE TABLE online_role_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    total_online INT NOT NULL,
    server_stats JSON NOT NULL,
    created_at DATETIME NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- App settings table
CREATE TABLE IF NOT EXISTS app_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    app_id INT NOT NULL UNIQUE,
    app_secret VARCHAR(255) NOT NULL,
    google_login TINYINT(1) DEFAULT 1,
    facebook_login TINYINT(1) DEFAULT 1,
    apple_login TINYINT(1) DEFAULT 1,
    guest_login TINYINT(1) DEFAULT 1,
    is_active TINYINT(1) DEFAULT 1,
    is_debug TINYINT(1) DEFAULT 0,
    phone_sync TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default app settings
INSERT INTO app_settings (app_id, app_secret, google_login, facebook_login, apple_login, guest_login, is_active, is_debug, phone_sync)
VALUES (1001, MD5(CONCAT('default_secret_', RAND())), 1, 1, 1, 1, 1, 0, 0);

-- Create indexes
CREATE INDEX idx_game_users_status ON game_users(status);
CREATE INDEX idx_payment_transactions_status ON payment_transactions(status);
CREATE INDEX idx_payment_transactions_created_at ON payment_transactions(created_at);
CREATE INDEX idx_gm_logs_created_at ON gm_logs(created_at);
CREATE INDEX idx_api_logs_created_at ON api_logs(created_at);
CREATE INDEX idx_game_servers_status ON game_servers(status);
CREATE INDEX idx_game_servers_merged_into ON game_servers(merged_into);
CREATE INDEX idx_api_logs_created_at ON api_logs(created_at);

