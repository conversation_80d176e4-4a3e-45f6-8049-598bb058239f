<?php

namespace Api\Controllers;

use App\Core\Controller;
use App\Core\GameDatabaseManager;
use App\Services\TelegramService;
use Api\Core\Validator;
use Api\Core\ApiHandler;

class CronController extends Controller {
    use ApiHandler;

    private $telegramService;
    private $serverConfig;
    private $validator;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->serverConfig = require __DIR__ . '/../../config/server.php';
        // Lazy loading - chỉ khởi tạo khi cần
    }

    /**
     * Get Validator instance (lazy loading)
     */
    private function getValidator(): Validator
    {
        if ($this->validator === null) {
            $this->validator = Validator::getInstance();
        }
        return $this->validator;
    }

    /**
     * Get TelegramService instance (lazy loading)
     */
    private function getTelegramService(): TelegramService
    {
        if ($this->telegramService === null) {
            $this->telegramService = new TelegramService();
        }
        return $this->telegramService;
    }

    public function collectOnlineRoles() {
        return $this->apiEndpoint('collect_online_roles', function() {
            $result = $this->collectOnlineRolesData();
            $this->jsonResponse($result, 200);
        });
    }

    public function collectOnlineRolesData() {
        // Kết nối tới tất cả các server
        $servers = $this->db->getServers(true, true);
        $connectResults = $this->gameDatabaseManager->connectServers($servers);
        $this->checkGameDbConnections($connectResults);
        
        $totalOnlineRoles = 0;
        $serverStats = [];

        // Truy vấn số role online từ tất cả server
        $sql = $this->gameSqlBuilder->buildSelect('role', 'is_online = ?', [1]);
        $results = $this->gameDatabaseManager->executeOnAll($sql['sql'], $sql['params']);

        // Xử lý kết quả từ tất cả server
        foreach ($results as $serverId => $result) {
            if ($result['success']) {
                $onlineCount = count($result['data']);
                $totalOnlineRoles += $onlineCount;
                $serverStats[$serverId] = $onlineCount;
            } else {
                $serverStats[$serverId] = 0;
            }
        }

        // Lưu thống kê vào database
        $statsData = [
            'total_online' => $totalOnlineRoles,
            'server_stats' => json_encode($serverStats),
            'created_at' => date('Y-m-d H:i:s')
        ];

        $this->db->insert('online_role_stats', $statsData);

        // Đóng kết nối
        $this->gameDatabaseManager->closeAll();

        return [
            'status' => true,
            'message' => 'Thu thập dữ liệu thành công',
            'data' => [
                'total_online' => $totalOnlineRoles,
                'server_stats' => $serverStats
            ]
        ];
    }

    public function checkServers() {
        return $this->apiEndpoint('collect_online_roles', function() {
            // Lấy danh sách server không bảo trì
            $servers = $this->db->getServers(true);
            $errors = [];
            foreach ($servers as $server) {
                if ($server['status'] == $this->serverConfig['maintenance']) {
                    continue;
                }
                $serverErrors = [];

                // Kiểm tra port
                if (!$this->checkPort($server['ip'], $server['port'])) {
                    $serverErrors[] = "Không thể kết nối tới port {$server['port']}";
                }

                // Kiểm tra database
                if (!$this->checkDatabase($server['mysql_ip'], $server['mysql_db'])) {
                    $serverErrors[] = "Không thể kết nối tới database {$server['mysql_db']}";
                }

                if (!empty($serverErrors)) {
                    $errors[$server['id']] = [
                        'name' => $server['name'],
                        'errors' => $serverErrors
                    ];
                }
            }

            if (!empty($errors)) {
                $this->sendAlert($errors);
            }

            $this->jsonResponse([
                'status' => empty($errors),
                'errors' => $errors
            ], 200);
        });
    }

    private function checkPort($ip, $port) {
        $fp = @fsockopen($ip, $port, $errno, $errstr, 2);
        if (!$fp) {
            return false;
        }
        fclose($fp);
        return true;
    }

    private function checkDatabase($host, $database) {
        try {
            $user = getenv('GAME_DB_USER');
            $pass = getenv('GAME_DB_PASS');
            
            $dsn = "mysql:host={$host};dbname={$database}";
            $pdo = new \PDO($dsn, $user, $pass, [
                \PDO::ATTR_TIMEOUT => 2,
                \PDO::ATTR_ERRMODE => \PDO::ERRMODE_EXCEPTION
            ]);
            
            return true;
        } catch (\PDOException $e) {
            return false;
        }
    }

    private function sendAlert($errors) {
        $message = "<b>⚠️ Cảnh báo: Lỗi máy chủ</b>\n\n";
        $message .= "⏰ Thời gian: " . date('H:i:s d/m/Y') . "\n\n";

        foreach ($errors as $serverId => $data) {
            $message .= "<b>Server {$data['name']} (ID: {$serverId})</b>\n";
            foreach ($data['errors'] as $error) {
                $message .= "• {$error}\n";
            }
            $message .= "\n";
        }

        $this->getTelegramService()->sendMessage($message);
    }
} 