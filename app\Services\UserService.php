<?php
namespace App\Services;

use App\Core\Database;

class UserService 
{
    private static $instance = null;
    private $db;
    private $redis;
    private $cachePrefix = 'game_users:';
    private $cacheExpire = 1800; // 30 minutes for individual users
    private $mapExpire = 3600; // 1 hour for mapping cache
    
    private function __construct(Database $db) {
        $this->db = $db;
        $this->redis = RedisService::getInstance();
    }
    
    /**
     * Get singleton instance
     */
    public static function getInstance(Database $db): self
    {
        if (self::$instance === null) {
            self::$instance = new self($db);
        }
        return self::$instance;
    }
    
    /**
     * Get user data from cache or database
     */
    private function getUserData(string $userId): ?array 
    {
        $hashKey = $this->cachePrefix . 'data';
        
        // Try to get from Redis hash first
        if ($this->redis->isAvailable()) {
            try {
                $userData = $this->redis->hGet($hashKey, $userId);
                if ($userData !== false) {
                    $data = json_decode($userData, true);
                    if ($data !== null) {
                        // Check expiration
                        if (!isset($data['expires_at']) || $data['expires_at'] > time()) {
                            return $data['data'];
                        } else {
                            // Expired, remove from hash
                            $this->redis->hDel($hashKey, $userId);
                        }
                    }
                }
            } catch (\Exception $e) {
                // Silent fail, fallback to database
            }
        }

        return null;
    }

    /**
     * Cache user data
     */
    private function cacheUserData(array $userData): void
    {
        if (!$this->redis->isAvailable() || empty($userData['user_id'])) {
            return;
        }

        try {
            $hashKey = $this->cachePrefix . 'data';
            $cacheData = [
                'data' => $userData,
                'cached_at' => time(),
                'expires_at' => time() + $this->cacheExpire
            ];
            
            // Set in hash and ensure TTL
            $this->redis->hSetex($hashKey, [
                $userData['user_id'] => json_encode($cacheData)
            ], $this->cacheExpire);

            // Cache all available mappings
            $mappings = [
                'username' => $userData['username'] ?? null,
                'email' => $userData['email'] ?? null,
                'phone' => $userData['phone'] ?? null,
                'id' => (string)($userData['id'] ?? null)
            ];

            foreach ($mappings as $type => $value) {
                if (!empty($value)) {
                    $this->cacheUserIdMapping($type, $value, $userData['user_id']);
                }
            }

            if (!empty($userData['register_info'])) {
                $registerInfo = is_array($userData['register_info']) 
                    ? $userData['register_info'] 
                    : json_decode($userData['register_info'], true);

                if (!empty($registerInfo['device_id'])) {
                    $deviceKey = $this->cachePrefix . 'device:' . $registerInfo['device_id'];
                    $this->redis->setex($deviceKey, $this->cacheExpire, json_encode($cacheData));
                }
            }

        } catch (\Exception $e) {
            error_log("Error caching user data: " . $e->getMessage());
            // Silent fail
        }
    }

    /**
     * Get user ID from mapping cache
     */
    private function getUserIdFromMapping(string $type, string $value): ?string
    {
        if (!$this->redis->isAvailable()) {
            return null;
        }

        try {
            $hashKey = $this->cachePrefix . 'map:' . $type;
            $mapValue = md5($value);
            $userId = $this->redis->hGet($hashKey, $mapValue);
            if ($userId !== false) {
                return $userId;
            }
        } catch (\Exception $e) {
            // Silent fail
        }

        return null;
    }

    /**
     * Cache user ID mapping
     */
    private function cacheUserIdMapping(string $type, string $value, string $userId): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }

        try {
            $hashKey = $this->cachePrefix . 'map:' . $type;
            $mapValue = md5($value);
            
            // Set in hash and ensure TTL
            $this->redis->hSetex($hashKey, [
                $mapValue => $userId
            ], $this->mapExpire);
        } catch (\Exception $e) {
            // Silent fail
        }
    }

    /**
     * Fetch user from database with condition
     */
    private function fetchUser(string $field, $value): ?array
    {
        $conditions = [
            'user_id' => 'user_id = ?',
            'username' => 'username = ?',
            'email' => 'email = ?', 
            'phone' => 'phone = ?',
            'id' => 'id = ?',
            'device' => "user_type = 'guest' AND register_info IS NOT NULL AND JSON_EXTRACT(register_info, '$.device_id') = ?"
        ];

        if (!isset($conditions[$field])) {
            return null;
        }

        $where = $conditions[$field];
        $params = is_array($value) ? $value : [$value];
        
        try {
            $result = $this->db->fetch("SELECT * FROM game_users WHERE $where", $params);
            return $result ?: null;
        } catch (\Exception $e) {
            error_log("Error fetching user: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get user by user_id with cache
     */
    public function getByUserId(string $userId): ?array
    {
        // Try to get from cache first
        $userData = $this->getUserData($userId);
        if ($userData !== null) {
            return $userData;
        }

        // Get from database
        $user = $this->fetchUser('user_id', $userId);
        
        // Cache the result
        if ($user) {
            $this->cacheUserData($user);
        }
        
        return $user ?: null;
    }
    
    /**
     * Get user by username with cache
     */
    public function getByUsername(string $username): ?array
    {
        // Try to get user_id from mapping
        $userId = $this->getUserIdFromMapping('username', $username);
        if ($userId !== null) {
            $userData = $this->getUserData($userId);
            if ($userData !== null) {
                return $userData;
            }
        }

        // Get from database
        $user = $this->fetchUser('username', $username);
        
        // Cache data and all mappings
        if ($user) {
            $this->cacheUserData($user);
        }
        
        return $user ?: null;
    }
    
    /**
     * Get user by email with cache
     */
    public function getByEmail(string $email): ?array
    {
        // Try to get user_id from mapping
        $userId = $this->getUserIdFromMapping('email', $email);
        if ($userId !== null) {
            $userData = $this->getUserData($userId);
            if ($userData !== null) {
                return $userData;
            }
        }

        // Get from database
        $user = $this->fetchUser('email', $email);
        
        // Cache data and all mappings
        if ($user) {
            $this->cacheUserData($user);
        }
        
        return $user ?: null;
    }
    
    /**
     * Get user by phone with cache
     */
    public function getByPhone(string $phone): ?array
    {
        // Try to get user_id from mapping
        $userId = $this->getUserIdFromMapping('phone', $phone);
        if ($userId !== null) {
            $userData = $this->getUserData($userId);
            if ($userData !== null) {
                return $userData;
            }
        }

        // Get from database
        $user = $this->fetchUser('phone', $phone);
        
        // Cache data and all mappings
        if ($user) {
            $this->cacheUserData($user);
        }
        
        return $user ?: null;
    }
    
    /**
     * Get user by ID with cache
     */
    public function getById(int $id): ?array
    {
        // Try to get user_id from mapping
        $userId = $this->getUserIdFromMapping('id', (string)$id);
        if ($userId !== null) {
            $userData = $this->getUserData($userId);
            if ($userData !== null) {
                return $userData;
            }
        }

        // Get from database
        $user = $this->fetchUser('id', (string)$id);
        
        // Cache data and all mappings
        if ($user) {
            $this->cacheUserData($user);
        }
        
        return $user ?: null;
    }
    
    /**
     * Get user list from database (no cache)
     */
    public function getList(string $query, array $params = [], int $page = 1, int $perPage = 20): array
    {
        // Direct database query without cache for list operations
        return $this->db->fetchAll($query, $params);
    }
    
    /**
     * Get user by device ID with cache (for guest users)
     */
    public function getByDeviceId(string $deviceId): ?array
    {
        $cacheKey = $this->cachePrefix . 'device:' . $deviceId;
        
        // Try to get from Redis cache first
        if ($this->redis->isAvailable()) {
            try {
                $cached = $this->redis->get($cacheKey);
                if ($cached !== false) {
                    $data = json_decode($cached, true);
                    if ($data !== null && isset($data['data'])) {
                        return $data['data'];
                    }
                }
            } catch (\Exception $e) {
                // Silent fail, fallback to database
            }
        }
        
        // Get from database
        $user = $this->fetchUser('device', $deviceId);
        
        // Cache the result if Redis is available
        if ($user && $this->redis->isAvailable()) {
            try {
                $cacheData = [
                    'data' => $user,
                    'cached_at' => time(),
                    'expires_at' => time() + $this->cacheExpire
                ];
                $this->redis->setex($cacheKey, $this->cacheExpire, json_encode($cacheData));
            } catch (\Exception $e) {
                // Silent fail
            }
        }
        
        return $user ?: null;
    }

    /**
     * Count number of guest accounts by IP with cache
     */
    public function countGuestsByIp(string $ip): int
    {
        $cacheKey = $this->cachePrefix . 'guest_count:' . $ip;
        
        // Try to get from Redis cache first
        if ($this->redis->isAvailable()) {
            try {
                $cached = $this->redis->get($cacheKey);
                if ($cached !== false) {
                    $data = json_decode($cached, true);
                    if ($data !== null && isset($data['data'])) {
                        return (int)$data['data'];
                    }
                }
            } catch (\Exception $e) {
                // Silent fail, fallback to database
            }
        }
        
        // Get from database
        $result = $this->db->fetch(
            "SELECT COUNT(*) as count FROM game_users 
             WHERE user_type = 'guest' 
             AND register_info IS NOT NULL
             AND JSON_EXTRACT(register_info, '$.ip') = ?",
            [$ip]
        );
        
        $count = (int)($result['count'] ?? 0);
        
        // Cache the result if Redis is available
        if ($this->redis->isAvailable()) {
            try {
                $cacheData = [
                    'data' => $count,
                    'cached_at' => time(),
                    'expires_at' => time() + 300 // Cache 5 phút
                ];
                $this->redis->setex($cacheKey, 300, json_encode($cacheData));
            } catch (\Exception $e) {
                // Silent fail
            }
        }
        
        return $count;
    }

    /**
     * Create new user and invalidate cache
     */
    public function create(array $data): int
    {
        $id = $this->db->insert('game_users', $data);
        
        // Invalidate related caches
        $this->invalidateUserCache($data);
        
        if (isset($data['register_info'])) {
            $registerInfo = json_decode($data['register_info'], true);
            if (isset($registerInfo['device_id'])) {
                $this->redis->del($this->cachePrefix . 'device:' . $registerInfo['device_id']);
            }
            if (isset($registerInfo['ip'])) {
                $this->redis->del($this->cachePrefix . 'guest_count:' . $registerInfo['ip']);
            }
        }
        
        return $id;
    }

    /**
     * Update user and invalidate cache
     */
    public function update(int $id, array $data): bool
    {
        // Start transaction
        $this->db->beginTransaction();
        
        try {
            // Get current user data for cache invalidation
            $user = $this->getById($id);
            if (!$user) {
                $this->db->rollback();
                return false;
            }

            // Update database
            $this->db->update('game_users', $data, 'id = ?', [$id]);

            // Invalidate basic cache keys
            $this->invalidateUserCache($user);

            // Invalidate special cache keys if relevant fields changed
            if ($this->redis->isAvailable()) {
                // Email verification cache
                if (isset($data['email']) && $data['email'] !== $user['email']) {
                    $this->redis->del($this->cachePrefix . 'email_verified:' . md5($user['email']) . ':1:' . $user['id']);
                    // Also invalidate new email's cache
                    $this->redis->del($this->cachePrefix . 'email_verified:' . md5($data['email']) . ':1:' . $user['id']);
                }
                // Device and IP cache
                $oldInfo = isset($user['register_info']) ? json_decode($user['register_info'], true) : [];
                $newInfo = isset($data['register_info']) ? json_decode($data['register_info'], true) : [];

                if ($oldInfo) {
                    if (isset($oldInfo['device_id'])) {
                        $this->redis->del($this->cachePrefix . 'device:' . $oldInfo['device_id']);
                    }
                    if (isset($oldInfo['ip'])) {
                        $this->redis->del($this->cachePrefix . 'guest_count:' . $oldInfo['ip']);
                    }
                }

                if ($newInfo) {
                    if (isset($newInfo['device_id'])) {
                        $this->redis->del($this->cachePrefix . 'device:' . $newInfo['device_id']);
                    }
                    if (isset($newInfo['ip'])) {
                        $this->redis->del($this->cachePrefix . 'guest_count:' . $newInfo['ip']);
                    }
                }
            }

            $this->db->commit();
            return true;

        } catch (\Exception $e) {
            $this->db->rollback();
            error_log("Failed to update user: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete user and invalidate cache
     */
    public function delete(int $id): bool
    {
        // Get user data before deletion
        $user = $this->getById($id);
        
        try {
            $this->db->delete('game_users', 'id = ?', [$id]);
            
            // Invalidate cache
            if ($user) {
                $this->invalidateUserCache($user);
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Invalidate all cache for a specific user
     */
    private function invalidateUserCache(array $userData): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }
        
        try {
            // Delete from data hash
            if (isset($userData['user_id'])) {
                $this->redis->hDel($this->cachePrefix . 'data', $userData['user_id']);
            }

            // Delete from mapping hashes
            if (isset($userData['username'])) {
                $this->redis->hDel($this->cachePrefix . 'map:username', md5($userData['username']));
            }
            if (isset($userData['email'])) {
                $this->redis->hDel($this->cachePrefix . 'map:email', md5($userData['email']));
            }
            if (isset($userData['phone'])) {
                $this->redis->hDel($this->cachePrefix . 'map:phone', md5($userData['phone']));
            }
            if (isset($userData['id'])) {
                $this->redis->hDel($this->cachePrefix . 'map:id', $userData['id']);
            }
        } catch (\Exception $e) {
            // Silent fail
        }
    }
    
    /**
     * Clear all user cache
     */
    public function clearAllCache(): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }
        
        try {
            $keys = $this->redis->keys($this->cachePrefix . '*');
            if (!empty($keys)) {
                $this->redis->del($keys);
            }
        } catch (\Exception $e) {
            // Silent fail
        }
    }
    
    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        if (!$this->redis->isAvailable()) {
            return ['redis_available' => false];
        }
        
        try {
            // Lấy thông tin từ các hash keys
            $dataHash = $this->cachePrefix . 'data';
            
            // Đếm số lượng entries trong mỗi hash
            $dataCount = $this->redis->hLen($dataHash) ?: 0;
            
            // Lấy tất cả token keys
            $tokenKeys = $this->redis->keys($this->cachePrefix . 'token:*');
            $tokenCount = count($tokenKeys);

            // Lấy chi tiết từ hash data
            $cacheDetails = [];
            $lastUpdated = null;
            
            // Lấy tất cả entries từ data hash
            $allData = $this->redis->hGetAll($dataHash);
            if ($allData) {
                $processedKeys = 0;
                foreach ($allData as $key => $value) {
                    if ($processedKeys >= 100) break; // Giới hạn 100 entries
                    
                    $data = json_decode($value, true);
                    if ($data) {
                        // Format key name
                        $keyName = 'user:' . $key;
                        
                        $cacheDetails[$keyName] = [
                            'cached_at' => $data['cached_at'] ?? time(),
                            'expires_at' => $data['expires_at'] ?? null
                        ];
                        
                        // Cập nhật last_updated
                        if (!$lastUpdated || ($data['cached_at'] ?? 0) > $lastUpdated) {
                            $lastUpdated = $data['cached_at'];
                        }
                        
                        $processedKeys++;
                    }
                }
            }

            // Lấy thông tin mapping
            $mappingTypes = ['username', 'email', 'phone', 'id'];
            $mappings = [];
            
            foreach ($mappingTypes as $type) {
                $hashKey = $this->cachePrefix . 'map:' . $type;
                $mappingData = $this->redis->hGetAll($hashKey) ?: [];
                
                $mappings["{$type}_mappings"] = [];
                $processedMappings = 0;
                
                foreach ($mappingData as $key => $value) {
                    if ($processedMappings >= 50) break; // Giới hạn 50 mappings mỗi loại
                    
                    $mappings["{$type}_mappings"][] = [
                        'key' => $key,
                        'value' => $value
                    ];
                    
                    $processedMappings++;
                }
            }

            return array_merge([
                'redis_available' => true,
                'user_keys' => $dataCount,
                'token_keys' => $tokenCount,
                'cache_prefix' => $this->cachePrefix,
                'cache_expire' => $this->cacheExpire,
                'last_updated' => $lastUpdated,
                'cache_details' => $cacheDetails
            ], $mappings);
            
        } catch (\Exception $e) {
            error_log("Error getting cache stats: " . $e->getMessage());
            return ['redis_available' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get access token with cache
     */
    public function getAccessToken(string $token): ?array
    {
        $cacheKey = $this->cachePrefix . 'token:' . md5($token);
        
        // Try to get from Redis cache first
        if ($this->redis->isAvailable()) {
            try {
                $cached = $this->redis->get($cacheKey);
                if ($cached !== false) {
                    $data = json_decode($cached, true);
                    if ($data !== null && isset($data['data'])) {
                        // Check if token is still valid
                        if (strtotime($data['data']['expires_at']) > time()) {
                            return $data['data'];
                        } else {
                            // Token expired, remove from cache
                            $this->redis->del($cacheKey);
                        }
                    }
                }
            } catch (\Exception $e) {
                // Silent fail, fallback to database
            }
        }
        
        // Get from database
        $tokenData = $this->db->fetch(
            "SELECT * FROM access_tokens WHERE token = ? AND expires_at > NOW()",
            [$token]
        );
        
        // Cache the result if Redis is available
        if ($tokenData && $this->redis->isAvailable()) {
            try {
                // Calculate TTL based on token expiry
                $expiresAt = strtotime($tokenData['expires_at']);
                $ttl = min($expiresAt - time(), $this->cacheExpire); // Use shorter of token TTL or cache TTL
                
                if ($ttl > 0) {
                    $cacheData = [
                        'data' => $tokenData,
                        'cached_at' => time(),
                        'expires_at' => time() + $ttl
                    ];
                    $this->redis->setex($cacheKey, $ttl, json_encode($cacheData));
                }
            } catch (\Exception $e) {
                // Silent fail
            }
        }
        
        return $tokenData ?: null;
    }
    
    /**
     * Cache new access token
     */
    public function cacheAccessToken(array $tokenData): void
    {
        if (!$this->redis->isAvailable() || empty($tokenData['token'])) {
            return;
        }
        
        try {
            $cacheKey = $this->cachePrefix . 'token:' . md5($tokenData['token']);
            
            // Calculate TTL based on token expiry
            $expiresAt = strtotime($tokenData['expires_at']);
            $ttl = min($expiresAt - time(), $this->cacheExpire);
            
            if ($ttl > 0) {
                $cacheData = [
                    'data' => $tokenData,
                    'cached_at' => time(),
                    'expires_at' => time() + $ttl
                ];
                $this->redis->setex($cacheKey, $ttl, json_encode($cacheData));
            }
        } catch (\Exception $e) {
            // Silent fail
        }
    }
    
    /**
     * Invalidate access token cache
     */
    public function invalidateAccessToken(string $token): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }
        
        try {
            $cacheKey = $this->cachePrefix . 'token:' . md5($token);
            $this->redis->del($cacheKey);
        } catch (\Exception $e) {
            // Silent fail
        }
    }
    
    /**
     * Invalidate all tokens for a user
     */
    public function invalidateUserTokens(string $userId): void
    {
        if (!$this->redis->isAvailable()) {
            return;
        }
        
        try {
            // Get all tokens for this user from database
            $tokens = $this->db->fetchAll(
                "SELECT token FROM access_tokens WHERE user_id = ?",
                [$userId]
            );
            
            $keysToDelete = [];
            foreach ($tokens as $tokenData) {
                $keysToDelete[] = $this->cachePrefix . 'token:' . md5($tokenData['token']);
            }
            
            if (!empty($keysToDelete)) {
                $this->redis->del($keysToDelete);
            }
        } catch (\Exception $e) {
            // Silent fail
        }
    }
}
