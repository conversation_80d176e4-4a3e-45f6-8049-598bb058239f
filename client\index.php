<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load .env configuration
$envPath = dirname(__DIR__);
$dotenv = Dotenv\Dotenv::createUnsafeImmutable($envPath);
$dotenv->load();

error_reporting(E_ALL);
ini_set('display_errors', 1);

$user_id = $_GET['user_id'] ?? '';
$key = $_GET['key'] ?? '';
$sign = $_GET['sign'] ?? '';
$error = '';

if($user_id) {
    if(!$sign) {
        $keyCheck = getenv('GAME_TEST_KEY');
        if($keyCheck != $key) {
            $error = "Key không hợp lệ";
        } else {
            $signNew = md5($user_id . getenv('API_TOKEN'));
            $api_url = 'http://gmtool_nginx/api/GameUser/Login';
            
            $data = array(
                'user_id' => $user_id,
                'username' => $user_id . "@gmail.com",
                'sign' => $signNew,
                'email' => ""
            );

            $options = array(
                'http' => array(
                    'header'  => "Content-type: application/json\r\n",
                    'method'  => 'POST',
                    'content' => json_encode($data),
                    'ignore_errors' => true,
                    'timeout' => 30
                )
            );
            $context = stream_context_create($options);
            
            $response = @file_get_contents($api_url, false, $context);
            
            if ($response === false) {
                $error = "Lỗi khi kết nối đến server";
            } else {
                $result = json_decode($response, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    $error = "Lỗi khi xử lý dữ liệu";
                } else if($result['status'] === true) {
                    $sign = $result['data']['sign'];
                    $redirect_url = "?user_id=" . $user_id . "&sign=" . $sign . "&type=web&debug=true";
                    header("Location: " . $redirect_url);
                    exit;
                } else {
                    $error = $result['message'] ?? "Lỗi không xác định";
                }
            }
        }
    } else {
        // Nếu đã có sign, nhúng file playgame.html
        include 'playgame.html';
        exit;
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Game Portal</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .form-container {
            width: 90%;
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: #e0e0e0;
        }
        input {
            width: 100%;
            padding: 12px 15px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            font-size: 14px;
            color: #fff;
            transition: all 0.3s ease;
        }
        input:focus {
            outline: none;
            border-color: #4CAF50;
            background: rgba(255, 255, 255, 0.15);
        }
        input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        button {
            width: 100%;
            padding: 12px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 1px;
            text-transform: uppercase;
            transition: all 0.3s ease;
        }
        button:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: translateY(-2px);
        }
        button:active {
            transform: translateY(0);
        }
        h2 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
            color: #4CAF50;
            text-transform: uppercase;
            letter-spacing: 2px;
        }
        .error-message {
            background: rgba(255, 0, 0, 0.1);
            color: #ff4444;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            border: 1px solid rgba(255, 0, 0, 0.2);
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @media (max-width: 480px) {
            .form-container {
                padding: 20px;
            }
            input, button {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Game Portal</h2>
        <?php if ($error): ?>
            <div class="error-message">
                <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        <form method="GET" action="">
            <div class="form-group">
                <label for="user_id">Tài Khoản</label>
                <input type="text" id="user_id" name="user_id" placeholder="Nhập User ID" required value="<?php echo htmlspecialchars($user_id); ?>">
            </div>
            <div class="form-group">
                <label for="key">Mật Khẩu</label>
                <input type="password" id="key" name="key" placeholder="Nhập Key" required>
            </div>
            <button type="submit">Đăng Nhập</button>
        </form>
    </div>
</body>
</html>