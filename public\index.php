<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Khởi tạo session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Load composer autoloader
require __DIR__ . '/../vendor/autoload.php';

// Load .env configuration
$envPath = dirname(__DIR__);
$dotenv = Dotenv\Dotenv::createUnsafeImmutable($envPath);
$dotenv->load();

// Thiết lập múi giờ từ .env (nếu có)
$timezone = getenv('APP_TIMEZONE') ?: 'Asia/Ho_Chi_Minh';
date_default_timezone_set($timezone);

// Error reporting
if ((getenv('APP_DEBUG') ?? '') === 'true') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Security headers
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: same-origin');

// Load core files
require_once __DIR__ . '/../app/Core/Database.php';

// Load configurations
$config = [
    'database' => require __DIR__ . '/../config/database.php',
    'permissions' => require __DIR__ . '/../config/permissions.php',
    'api' => require __DIR__ . '/../config/api.php'
];

// Initialize database connection
try {
    $db = App\Core\Database::getInstance($config['database']);
} catch (Exception $e) {
    die("Connection failed: " . $e->getMessage());
}

// Security checks
require_once __DIR__ . '/../app/Middleware/SecurityMiddleware.php';
$security = new \App\Middleware\SecurityMiddleware();

// Route handling
$route = $_GET['route'] ?? 'auth';
$action = $_GET['action'] ?? 'index';

// Default route to login
if ($route === 'auth') {
    require_once __DIR__ . '/../app/Controllers/AuthController.php';
    $controller = new \App\Controllers\AuthController($db, $config);
    
    // Kiểm tra action
    if (method_exists($controller, $action)) {
        $controller->$action();
    } else {
        if (isset($_SESSION['user_id'])) {
            header('Location: /public/?route=dashboard');
            exit;
        }
        $controller->login();
    }
} else {
    // Check authentication for other routes
    if (!isset($_SESSION['user_id'])) {
        header('Location: /public/?route=auth&action=login');
        exit;
    }
    
    // Check CSRF and IP for authenticated routes
    $security->checkCSRF();
    $security->checkIP();
    
    // Load controller
    $controllerFile = __DIR__ . "/../app/Controllers/" . ucfirst($route) . "Controller.php";
    if (file_exists($controllerFile)) {
        require_once $controllerFile;
        $controllerName = ucfirst($route) . 'Controller';
        $controllerClass = "App\\Controllers\\" . $controllerName;
        $controller = new $controllerClass($db, $config);
        
        if (method_exists($controller, $action)) {
            $controller->$action();
        } else {
            // Nếu action không tồn tại, thử gọi method index
            if (method_exists($controller, 'index')) {
                $controller->index();
            } else {
                header("HTTP/1.0 404 Not Found");
                require_once __DIR__ . '/../resources/views/404.php';
            }
        }
    } else {
        header("HTTP/1.0 404 Not Found");
        require_once __DIR__ . '/../resources/views/404.php';
    }
} 