<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h3 class="text-lg sm:text-xl font-medium text-gray-900">
                Nhật ký hệ thống
            </h3>
            <form method="GET" action="/public/?route=log" class="w-full sm:w-auto" id="searchForm">
                <input type="hidden" name="route" value="log">
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div>
                        <label for="admin_id" class="block text-sm sm:text-base font-medium text-gray-700">Admin</label>
                        <select id="admin_id" 
                                name="admin_id" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm sm:text-base">
                            <option value="">Tất cả</option>
                            <?php foreach ($admins as $admin): ?>
                                <option value="<?= htmlspecialchars($admin['id']) ?>" 
                                        <?= isset($_GET['admin_id']) && $_GET['admin_id'] === $admin['id'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($admin['username']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="log_action" class="block text-sm sm:text-base font-medium text-gray-700">Action</label>
                        <select id="log_action" 
                                name="log_action" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm sm:text-base">
                            <option value="">Tất cả</option>
                            <?php foreach ($actions as $action): ?>
                                <option value="<?= htmlspecialchars($action['action']) ?>" 
                                        <?= isset($_GET['log_action']) && $_GET['log_action'] === $action['action'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($action['action']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="status" class="block text-sm sm:text-base font-medium text-gray-700">Status</label>
                        <select id="status" 
                                name="status" 
                                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 text-sm sm:text-base">
                            <option value="">Tất cả</option>
                            <?php foreach ($statuses as $status): ?>
                                <option value="<?= htmlspecialchars($status['status']) ?>" 
                                        <?= isset($_GET['status']) && $_GET['status'] === $status['status'] ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($status['status']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div class="mt-4">
                    <button type="submit" 
                            class="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm sm:text-base font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-search mr-2"></i>
                        Tìm kiếm
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="border-t border-gray-200">
        <div class="p-4">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Admin</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Action</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">IP</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Data</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Created At</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($logs)): ?>
                        <tr>
                            <td colspan="7" class="px-3 sm:px-6 py-4 text-center text-sm text-gray-500">
                                Không có dữ liệu
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($logs as $log): ?>
                            <tr>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= htmlspecialchars($log['id']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= htmlspecialchars($log['username'] ?? 'N/A') ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= htmlspecialchars($log['action']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-<?= $log['status'] === 'success' ? 'green' : 'red' ?>-100 text-<?= $log['status'] === 'success' ? 'green' : 'red' ?>-800">
                                        <?= htmlspecialchars($log['status']) ?>
                                    </span>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= htmlspecialchars($log['ip_address']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 text-sm text-gray-500">
                                    <?php if (!empty($log['data'])): ?>
                                        <div class="space-y-1">
                                            <?php foreach ($log['data'] as $key => $value): ?>
                                                <div class="flex">
                                                    <span class="font-medium text-gray-900"><?= htmlspecialchars($key) ?>:</span>
                                                    <span class="ml-2 text-gray-600"><?= is_array($value) ? htmlspecialchars(json_encode($value)) : htmlspecialchars($value) ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else: ?>
                                        <span class="text-gray-400">-</span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= htmlspecialchars($log['created_at']) ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($pagination['total'] > 1): ?>
            <div class="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                <div class="text-sm text-gray-700">
                    Hiển thị <span class="font-medium"><?= ($offset + 1) ?></span> đến 
                    <span class="font-medium"><?= min($offset + $perPage, $total) ?></span> của 
                    <span class="font-medium"><?= $total ?></span> kết quả
                </div>
                <div class="flex flex-wrap gap-2">
                    <?php
                    // Hàm tạo URL phân trang
                    function buildPaginationUrl($page) {
                        $url = '/public/?route=log';
                        if (!empty($_GET['admin_id'])) {
                            $url .= '&admin_id=' . urlencode($_GET['admin_id']);
                        }
                        if (!empty($_GET['log_action'])) {
                            $url .= '&log_action=' . urlencode($_GET['log_action']);
                        }
                        if (!empty($_GET['status'])) {
                            $url .= '&status=' . urlencode($_GET['status']);
                        }
                        $url .= '&page=' . $page;
                        return $url;
                    }
                    ?>

                    <?php if ($pagination['current'] > 1): ?>
                        <a href="<?= buildPaginationUrl(1) ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                        <a href="<?= buildPaginationUrl($pagination['current'] - 1) ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $pagination['current'] - floor($pagination['max_links'] / 2));
                    $end = min($pagination['total'], $start + $pagination['max_links'] - 1);
                    $start = max(1, $end - $pagination['max_links'] + 1);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <a href="<?= buildPaginationUrl($i) ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md <?= $i == $pagination['current'] ? 'bg-indigo-600 text-white' : 'text-gray-700 bg-white hover:bg-gray-50' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($pagination['current'] < $pagination['total']): ?>
                        <a href="<?= buildPaginationUrl($pagination['current'] + 1) ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="<?= buildPaginationUrl($pagination['total']) ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 