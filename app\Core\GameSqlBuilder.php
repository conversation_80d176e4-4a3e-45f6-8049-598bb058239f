<?php
namespace App\Core;

class GameSqlBuilder
{
    // INSERT
    public static function buildInsert($table, array $data)
    {
        $fields = array_keys($data);
        $placeholders = array_fill(0, count($fields), '?');
        $sql = "INSERT INTO $table (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        return [
            'sql' => $sql,
            'params' => array_values($data)
        ];
    }

    // UPDATE
    public static function buildUpdate($table, array $data, $where, array $whereParams = [])
    {
        $fields = array_keys($data);
        $set = implode(', ', array_map(function($field) {
            return "$field = ?";
        }, $fields));
        $sql = "UPDATE $table SET $set WHERE $where";
        return [
            'sql' => $sql,
            'params' => array_merge(array_values($data), $whereParams)
        ];
    }

    // DELETE
    public static function buildDelete($table, $where, array $whereParams = [])
    {
        $sql = "DELETE FROM $table WHERE $where";
        return [
            'sql' => $sql,
            'params' => $whereParams
        ];
    }

    // SELECT
    public static function buildSelect($table, $where = '', array $whereParams = [], $fields = ['*'], $orderBy = '', $limit = null, $offset = null)
    {
        $sql = "SELECT " . implode(', ', $fields) . " FROM $table";
        if ($where) {
            $sql .= " WHERE $where";
        }
        if ($orderBy) {
            $sql .= " ORDER BY $orderBy";
        }
        if ($limit !== null) {
            $sql .= " LIMIT " . (int)$limit;
            if ($offset !== null) {
                $sql .= " OFFSET " . (int)$offset;
            }
        }
        return [
            'sql' => $sql,
            'params' => $whereParams
        ];
    }

    public static function buildSelectByArray($table, array $whereArr = [], $fields = ['*'])
    {
        $sql = "SELECT " . implode(', ', $fields) . " FROM $table";
        $where = [];
        $params = [];
        foreach ($whereArr as $key => $value) {
            $where[] = "$key = ?";
            $params[] = $value;
        }
        if ($where) {
            $sql .= " WHERE " . implode(' AND ', $where);
        }
        return [
            'sql' => $sql,
            'params' => $params
        ];
    }
}
