<?php
return [
    'settings' => [
        'token' => getenv('API_TOKEN') ?: 'your-secret-token-here',
        'blocked_ips' => getenv('API_BLOCKED_IPS') ? explode(',', getenv('API_BLOCKED_IPS')) : [],
        'debug' => getenv('APP_DEBUG') === 'true'
    ],

    // C<PERSON>u hình xác thực và bảo mật
    'authentication' => [
        // Cấu hình rate limiting (số request/phút cho mỗi endpoint)
        'rate_limits' => [
            'send_phone_verification' => 1,   // 1 lần/phút
            'send_email_verification' => 1,   // 1 lần/phút  
            'verify_phone_otp' => 5,          // 5 lần/phút
            'verify_email' => 5,              // 5 lần/phút
            'change_password' => 3,           // 3 lần/phút
            'default' => 120                   // 60 lần/phút (mặc định)
        ],

        // C<PERSON><PERSON> hình token
        'token_settings' => [
            'default_expire' => '+7 days',
            'guest_expire' => '+1 month', 
            'max_tokens_per_user' => 120,    // Tối đa 120 token/user trong 24h
            'cleanup_expired' => true        // Tự động xóa token hết hạn
        ]
    ],

    'endpoints' => [
        'create_order' => [
            'name' => 'Tạo đơn hàng',
            'path' => '/api/Order/Create',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => false,  // Cần xác minh token
            'params' => [
                'user_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID của người dùng'
                ],
                'role_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID nhân vật'
                ],
                'role_level' => [
                    'type' => 'numeric',
                    'required' => true,
                    'description' => 'Level nhân vật'
                ],
                'server_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID server'
                ],
                'time' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'Thời gian client gửi'
                ],
                'source' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['ios', 'android', 'web', 'facebook', 'google', 'guest'],
                    'description' => 'Nguồn tạo đơn hàng'
                ],
                'order_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Mã đơn hàng client gửi'
                ],
                'sign' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Chữ ký xác thực'
                ]
            ]
        ],
        'game_query' => [
            'name' => 'Lấy thông tin server game',
            'path' => '/api/Game/Query',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => [
                'user_id' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'ID của người chơi'
                ],
                'sign' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'Chữ ký xác thực'
                ]
            ]
        ],
        'game_user_info' => [
            'name' => 'Cập nhật thông tin server của người chơi',
            'path' => '/api/GameUser/UpdateInfo',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Cần xác minh token
            'params' => [
                'server_id' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'ID của server'
                ],
                'role_name' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Tên nhân vật'
                ],
                'role_level' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'Cấp độ nhân vật'
                ],
                'user_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID của người chơi'
                ],
                'sign' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Chữ ký xác thực'
                ]
            ]
        ],
        'claim_giftcode' => [
            'name' => 'Nhập Gift Code',
            'path' => '/api/GiftCode/Claim',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'spid' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID của người chơi'
                ],
                'server_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID server'
                ],
                'user_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID người dùng'
                ],
                'role_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID nhân vật'
                ],
                'role_level' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'Cấp độ nhân vật'
                ],
                'vip' => [
                    'type' => 'integer',
                    'required' => false,
                    'description' => 'Cấp độ VIP'
                ],
                'code' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Mã code'
                ],
                'time' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'Thời gian client gửi'
                ],
                'sign' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Chữ ký xác thực'
                ]
            ]
        ],
        'get_notices' => [
            'name' => 'Lấy danh sách thông báo',
            'path' => '/api/Notice/GetNotices',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => []
        ],
        'get_policy' => [
            'name' => 'Lấy nội dung chính sách',
            'path' => '/api/Notice/GetPolicy',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => []
        ],
        'get_terms' => [
            'name' => 'Lấy nội dung điều khoản',
            'path' => '/api/Notice/GetTerms',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => []
        ],
        'collect_online_roles' => [
            'name' => 'Thu thập số role online',
            'path' => '/api/Cron/collectOnlineRoles',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => []
        ],
        'check_servers' => [
            'name' => 'Kiểm tra tình trạng máy chủ',
            'path' => '/api/Cron/checkServers',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => []
        ],
        'register' => [
            'name' => 'Đăng ký tài khoản',
            'path' => '/api/GameUser/Register',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => [
                'register_type' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['google', 'facebook', 'apple', 'guest', 'account'],
                    'description' => 'Loại đăng ký'
                ],
                'source' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['ios', 'android', 'web'],
                    'description' => 'Nguồn đăng ký'
                ],
                'username' => [
                    'type' => 'username',
                    'required' => false,
                    'description' => 'Tên người dùng (bắt buộc với đăng ký social)'
                ],
                'password' => [
                    'type' => 'password',
                    'required' => false,
                    'description' => 'Mật khẩu (bắt buộc với đăng ký account)'
                ],
                'email' => [
                    'type' => 'email',
                    'required' => false,
                    'description' => 'Email (tùy chọn với đăng ký social)'
                ],
                'phone' => [
                    'type' => 'phone',
                    'required' => false,
                    'description' => 'Số điện thoại (tùy chọn với đăng ký account)'
                ],
                'oauth_id' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'ID từ mạng xã hội (bắt buộc với đăng ký Google/Facebook)'
                ],
                'device_id' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'ID thiết bị (tùy chọn)'
                ],
                'device_info' => [
                    'type' => 'json',
                    'required' => false,
                    'description' => 'Thông tin thiết bị (tùy chọn)'
                ]
            ]
        ],
        'login' => [
            'name' => 'Đăng nhập',
            'path' => '/api/GameUser/Login',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => [
                'login_type' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['google', 'facebook', 'apple', 'guest', 'account'],
                    'description' => 'Loại đăng nhập'
                ],
                'email' => [
                    'type' => 'email',
                    'required' => false,
                    'description' => 'Email (bắt buộc với đăng nhập email)'
                ],
                'password' => [
                    'type' => 'password',
                    'required' => false,
                    'description' => 'Mật khẩu (bắt buộc với đăng nhập email)'
                ],
                'oauth_id' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'ID từ mạng xã hội (bắt buộc với đăng nhập Google/Facebook)'
                ],
                'user_id' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'ID người dùng (bắt buộc với đăng nhập guest)'
                ]
            ]
        ],
        'convert_guest_account' => [
            'name' => 'Chuyển đổi tài khoản khách',
            'path' => '/api/GameUser/ConvertGuestAccount',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'guest_user_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID của tài khoản khách cần chuyển đổi'
                ],
                'username' => [
                    'type' => 'username',
                    'required' => true,
                    'description' => 'Tên người dùng mới'
                ],
                'password' => [
                    'type' => 'password',
                    'required' => true,
                    'description' => 'Mật khẩu tài khoản'
                ],
                'email' => [
                    'type' => 'email',
                    'required' => false,
                    'description' => 'Email (tùy chọn)'
                ]
            ]
        ],
        'verify_token' => [
            'name' => 'Xác minh token và lấy thông tin user',
            'path' => '/api/GameUser/VerifyToken',
            'method' => 'GET',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => []
        ],
        'forgot_password' => [
            'name' => 'Quên mật khẩu',
            'path' => '/api/GameUser/ForgotPassword',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => [
                'email' => [
                    'type' => 'email',
                    'required' => true,
                    'description' => 'Email đăng ký tài khoản'
                ]
            ]
        ],
        'app_info' => [
            'name' => 'Lấy thông tin ứng dụng',
            'path' => '/api/App/Info',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => [
                'source' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['ios', 'android', 'web'],
                    'description' => 'Nguồn đăng ký'
                ],
                'app_id' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'ID của ứng dụng'
                ],
                'app_secret' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Mã bí mật của ứng dụng'
                ]
            ]
        ],
        'send_phone_verification' => [
            'name' => 'Gửi mã xác thực điện thoại',
            'path' => '/api/GameUser/SendPhoneVerification',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'phone' => [
                    'type' => 'phone',
                    'required' => true,
                    'description' => 'Số điện thoại cần xác thực'
                ]
            ]
        ],
        'verify_phone_otp' => [
            'name' => 'Xác thực mã OTP điện thoại',
            'path' => '/api/GameUser/VerifyPhoneOTP',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'phone' => [
                    'type' => 'phone',
                    'required' => true,
                    'description' => 'Số điện thoại cần xác thực'
                ],
                'otp' => [
                    'type' => 'numeric',
                    'required' => true,
                    'description' => 'Mã OTP nhận được'
                ]
            ]
        ],
        'verify_email' => [
            'name' => 'Xác thực email',
            'path' => '/api/GameUser/VerifyEmail',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'email' => [
                    'type' => 'email',
                    'required' => true,
                    'description' => 'Email cần xác thực'
                ],
                'verification_code' => [
                    'type' => 'numeric',
                    'required' => true,
                    'description' => 'Mã xác thực nhận được qua email'
                ]
            ]
        ],
        'send_email_verification' => [
            'name' => 'Gửi mã xác thực email',
            'path' => '/api/GameUser/SendEmailVerification',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'email' => [
                    'type' => 'email',
                    'required' => true,
                    'description' => 'Email cần xác thực'
                ]
            ]
        ],
        'verify_purchase' => [
            'name' => 'Xác minh giao dịch mua hàng',
            'path' => '/api/Payment/VerifyPurchase',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => false,  // Endpoint công khai
            'params' => [
                'sdk_order_id' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'ID đơn hàng từ SDK'
                ],
                'product_id' => [
                    'type' => 'string', 
                    'required' => true,
                    'description' => 'ID của sản phẩm'
                ],
                'purchase_source' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['google_play', 'app_store'],
                    'description' => 'Nguồn thanh toán (google_play hoặc app_store)'
                ],
                'verification_data' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Token xác thực (Android) hoặc receipt base64 (iOS)'
                ],
                'transaction_date' => [
                    'type' => 'integer',
                    'required' => true,
                    'description' => 'Thời gian giao dịch (timestamp)'
                ],
                'msg' => [
                    'type' => 'string',
                    'required' => false,
                    'description' => 'Thông báo lỗi (nếu có)'
                ],
                'status' => [
                    'type' => 'enum',
                    'required' => true,
                    'values' => ['purchased', 'pending', 'error'],
                    'description' => 'Trạng thái giao dịch'
                ]
            ]
        ],
        'change_password' => [
            'name' => 'Đổi mật khẩu',
            'path' => '/api/GameUser/ChangePassword',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => [
                'current_password' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Mật khẩu hiện tại'
                ],
                'new_password' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Mật khẩu mới'
                ],
                'confirm_password' => [
                    'type' => 'string',
                    'required' => true,
                    'description' => 'Xác nhận mật khẩu mới'
                ]
            ]
        ],
        'delete_account' => [
            'name' => 'Xóa tài khoản',
            'path' => '/api/GameUser/DeleteAccount',
            'method' => 'POST',
            'enabled' => true,
            'require_auth' => true,  // Cần xác minh token
            'params' => []
        ]
    ]
];