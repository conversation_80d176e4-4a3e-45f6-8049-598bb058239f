@echo off
REM Tạo thư mục chứa SSL
if not exist "..\nginx\ssl" mkdir "..\nginx\ssl"

REM Tạo private key
openssl genrsa -out ..\nginx\ssl\gmtool.local.key 2048

REM Tạo CSR (Certificate Signing Request)
openssl req -new -key ..\nginx\ssl\gmtool.local.key -out ..\nginx\ssl\gmtool.local.csr -subj "/CN=gmtool.local"

REM Tạo self-signed certificate
openssl x509 -req -days 365 -in ..\nginx\ssl\gmtool.local.csr -signkey ..\nginx\ssl\gmtool.local.key -out ..\nginx\ssl\gmtool.local.crt

echo SSL certificates đã được tạo thành công!
pause 