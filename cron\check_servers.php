<?php
require_once __DIR__ . '/../vendor/autoload.php';
$config = require __DIR__ . '/../config/api.php';
$token = $config['settings']['token'];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://gmtool_nginx/api/Cron/checkServers');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    var_dump("Lỗi khi kiểm tra server: " . $response);
    exit(1);
}

$result = json_decode($response, true);
echo json_encode($result);