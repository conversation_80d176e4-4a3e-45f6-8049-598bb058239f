<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex justify-between items-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Chi tiết đơn hàng #<?= htmlspecialchars($order['order_id']) ?>
            </h3>
            <a href="?route=order" 
               class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                <i class="fas fa-arrow-left mr-2"></i>
                Quay lại
            </a>
        </div>
    </div>

    <div class="border-t border-gray-200">
        <div class="p-4">
            <?php if (isset($_SESSION['error'])): ?>
                <div class="mb-4 rounded-md bg-red-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-times-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">
                                <?= htmlspecialchars($_SESSION['error']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <?php if (isset($_SESSION['success'])): ?>
                <div class="mb-4 rounded-md bg-green-50 p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">
                                <?= htmlspecialchars($_SESSION['success']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <!-- Thông tin đơn hàng -->
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-base font-medium text-gray-900 mb-4">Thông tin đơn hàng</h4>
                <dl class="grid grid-cols-1 gap-3">
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Order ID:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['order_id']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">User ID:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['user_id']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Game UID:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['game_uid']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Package ID:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['package_id']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Package Count:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['package_count']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Server ID:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['server_id']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Số tiền:</dt>
                        <dd class="text-sm text-gray-900"><?= number_format($order['amount'], 0) ?> VNĐ</dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Nguồn:</dt>
                        <dd>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                <?= htmlspecialchars($order['source']) ?>
                            </span>
                        </dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Trạng thái:</dt>
                        <dd>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-<?= $orderConfig['display']['status_colors'][$order['status']] ?>-100 text-<?= $orderConfig['display']['status_colors'][$order['status']] ?>-800">
                                <?= htmlspecialchars(ucfirst($order['status'])) ?>
                            </span>
                        </dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Message:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['msg'] ?? '-') ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Ngày tạo:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['created_at']) ?></dd>
                    </div>
                    <div class="flex justify-between">
                        <dt class="text-sm font-medium text-gray-500">Cập nhật lần cuối:</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($order['updated_at'] ?? '-') ?></dd>
                    </div>
                </dl>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 