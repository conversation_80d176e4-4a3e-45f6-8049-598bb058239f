version: '3'
services:
  # PHP Service
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gmtool_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/conf.d/custom.ini:/usr/local/etc/php/conf.d/custom.ini
      - ./docker/php/logs:/var/log/php
    environment:
      - TZ=Asia/Ho_Chi_Minh
    extra_hosts:
      - "host.docker.internal:host-gateway" # <PERSON><PERSON><PERSON> h<PERSON><PERSON> kết nối đến MySQL trên Windows
    networks:
      - gmtool-network

  # Nginx Service
  nginx:
    build:
      context: .
      dockerfile: Dockerfile.nginx
    image: nginx:alpine
    container_name: gmtool_nginx
    restart: unless-stopped
    ports:
      - "81:80"
      # - "443:443"
    volumes:
      - ./:/var/www
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./docker/nginx/ssl:/etc/nginx/ssl
      - ./docker/nginx/logs:/var/log/nginx
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - gmtool-network

  # MySQL Service
  db:
    image: mysql:5.7
    container_name: gmtool_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_ROOT_PASSWORD: ${DB_PASS}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASS}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
      TZ: Asia/Ho_Chi_Minh
    ports:
      - "3307:3306" #3307 là port mới có thể dùng navicat để kết nối
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/logs:/var/log/mysql
      - ./database/gmtool.sql:/docker-entrypoint-initdb.d/gmtool.sql
    command: --max-connections=1000 --general-log=1 --general-log-file=/var/log/mysql/mysql.log --slow-query-log=1 --slow-query-log-file=/var/log/mysql/mysql-slow.log --log-error=/var/log/mysql/error.log --default-time-zone='+7:00'
    networks:
      - gmtool-network

  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: gmtool_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redisdata:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - ./docker/redis/logs:/var/log/redis
    command: redis-server /usr/local/etc/redis/redis.conf
    environment:
      - TZ=Asia/Ho_Chi_Minh
    networks:
      - gmtool-network

  # MySQL Backup Service
  db_backup:
    image: fradelg/mysql-cron-backup
    container_name: gmtool_db_backup
    restart: unless-stopped
    environment:
      - MYSQL_HOST=gmtool_db
      - MYSQL_PORT=3306
      - MYSQL_USER=${DB_USER}
      - MYSQL_PASS=${DB_PASS}
      - MYSQL_DATABASE=${DB_NAME}
      - CRON_TIME=0 0 * * *  # 0h mỗi ngày
      - MAX_BACKUPS=7        # Giữ lại 7 bản backup
      - TZ=Asia/Ho_Chi_Minh
    volumes:
      - ./docker/mysql/backups:/backup
    depends_on:
      - db
    networks:
      - gmtool-network

  # Cron Service
  cron:
    image: jeffkolb/multi-cron:latest
    container_name: gmtool_cron
    restart: unless-stopped
    environment:
      - TZ=Asia/Ho_Chi_Minh
      - CRON_SCH_1=*/10 * * * *
      - CRON_CMD_1=wget
      - CRON_ARGS_1=-q -O /dev/null http://gmtool_nginx/cron/check_servers.php
      - CRON_SCH_2=*/5 * * * *
      - CRON_CMD_2=wget
      - CRON_ARGS_2=-q -O /dev/null http://gmtool_nginx/cron/collect_online_roles.php
    networks:
      - gmtool-network

networks:
  gmtool-network:
    driver: bridge

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local 