<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    
    <div class="px-2 py-4 sm:px-6">
        <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3">
            <h3 class="text-lg leading-6 font-medium text-gray-900 text-left">
                <i class="fas fa-gem mr-2"></i>Danh sách Vật phẩm
            </h3>
            <div class="flex flex-col sm:flex-row gap-2 sm:gap-4 items-stretch w-full sm:w-auto">
                <form method="GET" action="/public/?route=item" class="flex flex-col sm:flex-row gap-2 sm:gap-4 w-full sm:w-auto">
                    <input type="hidden" name="route" value="item">
                    <div>
                        <select name="category" class="border border-gray-300 rounded-md px-2 py-2 text-sm w-full">
                            <option value="">Tất cả loại</option>
                            <?php foreach ($categories as $catKey => $catName): ?>
                                <option value="<?= htmlspecialchars($catKey) ?>" <?= ($catKey === ($category ?? '')) ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($catName) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="relative w-full sm:w-auto">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text" 
                               name="search" 
                               value="<?= htmlspecialchars($search ?? '') ?>" 
                               placeholder="Tìm kiếm theo ID, Tên vật phẩm" 
                               class="block w-full sm:w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                               minlength="<?= $itemConfig['search']['min_length'] ?>">
                    </div>
                    <button type="submit" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full sm:w-auto">
                        <i class="fas fa-search mr-2"></i>Tìm kiếm
                    </button>
                </form>
                <a href="?route=item&action=export<?= $category ? '&category=' . urlencode($category) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>"
                   class="inline-flex items-center px-4 py-2 border border-indigo-600 text-sm font-medium rounded-md shadow-sm text-indigo-700 bg-white hover:bg-indigo-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 w-full sm:w-auto justify-center"
                   style="white-space:nowrap">
                    <i class="fas fa-file-excel mr-2"></i>Xuất Excel
                </a>
            </div>
        </div>
    </div>

    <div class="border-t border-gray-200">
        <div class="p-4">
            <div class="flex flex-col lg:flex-row gap-6">
                <div class="flex-1 min-w-0">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Chọn</th>
                                    <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Icon</th>
                                    <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">ID</th>
                                    <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Tên</th>
                                    <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Loại</th>
                                    <th class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Mô tả</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($items)): ?>
                                <tr>
                                    <td colspan="6" class="px-4 sm:px-6 py-4 text-center text-sm text-gray-500">
                                        Không có vật phẩm nào phù hợp.
                                    </td>
                                </tr>
                                <?php else:
                                    foreach ($items as $item):
                                        $res = $item['res'] ?? 'equipped';
                                        $iconId = htmlspecialchars($item['icon_id'] ?? 'default');
                                        $iconBaseUrl = rtrim($appClientDomain, '/') . '/resource/eui/item/' . htmlspecialchars($res) . '/';
                                        $iconUrl = $iconBaseUrl . $iconId . '.png';
                                        // Đường dẫn fallback cho fragment
                                        $heroHoleUrl = rtrim($appClientDomain, '/') . '/resource/eui/hero/heroIcon_hole/' . $iconId . '.png';
                                        $description = $item['description'] ?? '';
                                        $shortDesc = mb_strlen($description) > 100 ? mb_substr($description, 0, 100) . '...' : $description;
                                        $descFull = htmlspecialchars($description);
                                    ?>
                                    <tr>
                                        <td class="px-2 py-4 text-center">
                                            <button type="button" class="add-item-btn px-2 py-1 bg-green-500 hover:bg-green-600 text-white rounded shadow transition" data-id="<?= htmlspecialchars($item['id']) ?>" data-name="<?= htmlspecialchars($item['name']) ?>">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </td>
                                        <td class="px-2 py-4 whitespace-nowrap">
                                            <img src="<?= $iconUrl ?>"
                                                 alt="<?= htmlspecialchars($item['name'] ?? '') ?>"
                                                 class="h-16 w-16 object-contain mx-auto rounded border border-gray-200 bg-gray-50"
                                                 onerror="if(this.src!=='<?= $heroHoleUrl ?>' && '<?= $res ?>'==='fragment'){this.src='<?= $heroHoleUrl ?>';}else{this.style.display='none';}">
                                        </td>
                                        <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-900 font-mono">
                                            <?= htmlspecialchars($item['id'] ?? '') ?>
                                        </td>
                                        <td class="px-2 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            <?= htmlspecialchars($item['name'] ?? '') ?>
                                        </td>
                                        <td class="px-2 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?= htmlspecialchars($item['category_name'] ?? 'Không rõ') ?>
                                        </td>
                                        <td class="px-2 py-4 text-sm text-gray-500 whitespace-pre-line max-w-xs">
                                            <?= nl2br(htmlspecialchars($shortDesc)) ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Danh sách item đã chọn -->
                <div id="selected-items-box" class="lg:w-96 w-full bg-white border border-indigo-200 p-4 rounded shadow flex flex-col gap-2 h-fit max-h-[70vh] lg:sticky lg:top-24 overflow-y-auto">
                    <h4 class="font-bold mb-2 text-indigo-700 flex items-center gap-2 text-base"><i class="fas fa-list"></i> Danh sách đã chọn</h4>
                    <div id="selected-items-list" class="mb-2"></div>
                    <div class="mt-2">
                        <label class="font-semibold">Chuỗi xuất:</label>
                        <input id="selected-items-string" class="w-full border px-2 py-1 rounded bg-gray-50 font-mono text-sm" readonly>
                        <button onclick="copySelectedItemsString()" class="mt-1 px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded shadow transition">Copy</button>
                    </div>
                </div>
            </div>

            <?php if ($pagination['total'] > 1): ?>
            <div class="mt-4 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 overflow-x-auto">
                <div class="text-xs sm:text-sm text-gray-700">
                    Hiển thị <span class="font-medium"><?= ($pagination['offset'] + 1) ?></span> đến 
                    <span class="font-medium"><?= min($pagination['offset'] + $pagination['perPage'], $pagination['total_items']) ?></span> của 
                    <span class="font-medium"><?= $pagination['total_items'] ?></span> kết quả
                </div>
                <div class="flex flex-wrap gap-1">
                    <?php if ($pagination['current'] > 1): ?>
                        <a href="?route=item&page=1<?= $search ? '&search=' . urlencode($search) : '' ?><?= $category ? '&category=' . urlencode($category) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                        <a href="?route=item&page=<?= ($pagination['current'] - 1) ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $category ? '&category=' . urlencode($category) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $pagination['current'] - floor($pagination['max_links'] / 2));
                    $end = min($pagination['total'], $start + $pagination['max_links'] - 1);
                    $start = max(1, $end - $pagination['max_links'] + 1);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <a href="?route=item&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $category ? '&category=' . urlencode($category) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md <?= $i == $pagination['current'] ? 'bg-indigo-600 text-white' : 'text-gray-700 bg-white hover:bg-gray-50' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($pagination['current'] < $pagination['total']): ?>
                        <a href="?route=item&page=<?= ($pagination['current'] + 1) ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $category ? '&category=' . urlencode($category) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="?route=item&page=<?= $pagination['total'] ?><?= $search ? '&search=' . urlencode($search) : '' ?><?= $category ? '&category=' . urlencode($category) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<style>
#selected-items-list .group:hover .remove-item-btn {
    display: inline-flex !important;
}
@media (min-width: 1024px) {
    #selected-items-box {
        min-width: 320px;
        max-width: 400px;
    }
}
.desc-popup-row td {
    z-index: 50;
    position: relative;
}
</style>

<script>
let selectedItems = {};

function renderSelectedItems() {
    const listDiv = document.getElementById('selected-items-list');
    const strInput = document.getElementById('selected-items-string');
    let html = '';
    let str = '';
    for (const [id, obj] of Object.entries(selectedItems)) {
        html += `
            <div class="flex items-center gap-2 mb-1 px-2 py-1 rounded group hover:bg-gray-100 transition">
                <div class="flex-1 min-w-0">
                    <span class="font-semibold text-gray-900">${obj.name}</span>
                    <span class="ml-1 text-xs text-gray-400 font-mono">(${id})</span>
                </div>
                <input type="number" min="1" value="${obj.qty}" class="item-qty border border-gray-300 rounded px-2 py-1 w-14 text-sm focus:ring-2 focus:ring-indigo-500" data-id="${id}">
                <button type="button" class="remove-item-btn px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded hidden group-hover:inline-flex transition" data-id="${id}" title="Xóa">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        str += `${id}:${obj.qty},`;
    }
    str = str.replace(/,$/, '');
    listDiv.innerHTML = html || '<span class="text-gray-400">Chưa chọn item nào.</span>';
    strInput.value = str;
}

document.addEventListener('click', function(e) {
    if (e.target.closest('.add-item-btn')) {
        const btn = e.target.closest('.add-item-btn');
        const id = btn.getAttribute('data-id');
        const name = btn.getAttribute('data-name');
        if (!selectedItems[id]) {
            selectedItems[id] = {name, qty: 1};
        } else {
            selectedItems[id].qty++;
        }
        renderSelectedItems();
    }
    if (e.target.closest('.remove-item-btn')) {
        const id = e.target.closest('.remove-item-btn').getAttribute('data-id');
        delete selectedItems[id];
        renderSelectedItems();
    }
});

document.addEventListener('input', function(e) {
    if (e.target.classList.contains('item-qty')) {
        const id = e.target.getAttribute('data-id');
        let val = parseInt(e.target.value) || 1;
        if (val < 1) val = 1;
        selectedItems[id].qty = val;
        renderSelectedItems();
    }
});

function copySelectedItemsString() {
    const input = document.getElementById('selected-items-string');
    input.select();
    document.execCommand('copy');
}
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 