# Email Features Documentation

## Overview
This document describes the email verification and password reset features implemented using SMTP with Gmail.

## Features Implemented

### 1. Email Verification
- **Endpoint**: `/api/GameUser/SendEmailVerification`
- **Method**: POST
- **Authentication**: Required
- **Rate Limit**: 1 request per minute

**Parameters:**
- `email` (string, required): Email address to verify

**Response:**
```json
{
    "status": true,
    "message": "Mã xác thực đã được gửi đến email của bạn",
    "data": {
        "expires_at": "2025-05-30 21:00:00",
        "wait_time": 60
    }
}
```

### 2. Email Verification Confirmation
- **Endpoint**: `/api/GameUser/VerifyEmail`
- **Method**: POST
- **Authentication**: Required

**Parameters:**
- `email` (string, required): Email address
- `verification_code` (string, required): 6-digit verification code

### 3. Forgot Password
- **Endpoint**: `/api/GameUser/ForgotPassword`
- **Method**: POST
- **Authentication**: Not required
- **Rate Limit**: 2 requests per minute

**Parameters:**
- `email` (string, required): Registered email address

**Response:**
```json
{
    "status": true,
    "message": "Email khôi phục mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.",
    "data": {
        "expires_at": "2025-05-30 22:00:00"
    }
}
```

### 4. Reset Password
- **Endpoint**: `/api/GameUser/ResetPassword`
- **Method**: POST
- **Authentication**: Not required
- **Rate Limit**: 3 requests per minute

**Parameters:**
- `token` (string, required): Reset token from email
- `new_password` (string, required): New password
- `confirm_password` (string, required): Confirm new password

**Response:**
```json
{
    "status": true,
    "message": "Mật khẩu đã được đặt lại thành công. Vui lòng đăng nhập lại với mật khẩu mới."
}
```

## Email Configuration

### Environment Variables
Add these to your `.env` file:

```env
# Email Configuration
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=emkf fqaq qlei cecd
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=GMTool
```

### Gmail App Password Setup
1. Enable 2-factor authentication on your Gmail account
2. Generate an app password for this application
3. Use the app password in `MAIL_PASSWORD` (not your regular Gmail password)

## Database Tables

### Email Verifications Table
```sql
CREATE TABLE IF NOT EXISTS email_verifications (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    user_id BIGINT(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    verification_code VARCHAR(6) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    KEY idx_user_email (user_id, email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### Password Resets Table
```sql
CREATE TABLE IF NOT EXISTS password_resets (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    user_id BIGINT(20) NOT NULL,
    email VARCHAR(100) NOT NULL,
    token VARCHAR(255) NOT NULL,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) DEFAULT 0,
    used_at DATETIME NULL,
    created_at DATETIME NOT NULL,
    PRIMARY KEY (id),
    KEY idx_token (token),
    KEY idx_user_email (user_id, email),
    KEY idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## Email Templates

### Email Verification Template
- **File**: `resources/email_templates/email_verification.php`
- **Variables**: `username`, `verification_code`, `app_name`, `app_url`

### Password Reset Template
- **File**: `resources/email_templates/password_reset.php`
- **Variables**: `username`, `reset_url`, `reset_token`, `app_name`, `app_url`

## EmailService Class

### Usage Example
```php
use App\Services\EmailService;

// Get instance
$emailService = EmailService::getInstance();

// Send email verification
$emailService->sendEmailVerification('<EMAIL>', '123456', 'Username');

// Send password reset
$emailService->sendPasswordReset('<EMAIL>', 'reset_token', 'Username');

// Test connection
if ($emailService->testConnection()) {
    echo "SMTP connection successful";
}
```

## Testing

### Test Script
Run the test script to verify email functionality:

```bash
php test_email.php
```

### Manual Testing
1. Register a new account with email
2. Try to send email verification
3. Check your email for the verification code
4. Try forgot password functionality
5. Check your email for the reset link

## Security Features

### Rate Limiting
- Email verification: 1 request per minute
- Forgot password: 2 requests per minute
- Reset password: 3 requests per minute

### Token Security
- Email verification codes expire in 5 minutes
- Password reset tokens expire in 1 hour
- Reset tokens can only be used once
- All user tokens are invalidated after password reset

### Validation
- Email format validation
- Password strength requirements
- Confirmation password matching
- User authentication for email verification

## Troubleshooting

### Common Issues

1. **SMTP Connection Failed**
   - Check Gmail app password
   - Verify SMTP settings
   - Ensure 2FA is enabled on Gmail

2. **Email Not Received**
   - Check spam folder
   - Verify email address is correct
   - Check Gmail sending limits

3. **Template Not Found**
   - Ensure template files exist in `resources/email_templates/`
   - Check file permissions

### Error Logs
Email sending errors are logged to PHP error log. Check your server's error log for detailed information.

## Future Enhancements

1. **Email Queue System**: Implement background job processing for emails
2. **Multiple Email Providers**: Support for other SMTP providers
3. **Email Analytics**: Track email open rates and click rates
4. **Custom Templates**: Admin interface for managing email templates
5. **Localization**: Multi-language email templates
