FROM php:7.4-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libpng-dev \
    tzdata

# Set timezone
RUN ln -fs /usr/share/zoneinfo/Asia/Ho_Chi_Minh /etc/localtime && \
    dpkg-reconfigure -f noninteractive tzdata

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg
RUN docker-php-ext-install -j$(nproc) \
    pdo_mysql \
    mysqli \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    zip

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install and configure Xdebug (optional)
# RUN pecl install xdebug && docker-php-ext-enable xdebug

# Get latest Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Create log directory and set permissions
RUN mkdir -p /var/log/php && \
    chown -R www-data:www-data /var/log/php && \
    chmod -R 755 /var/log/php

# Set working directory
WORKDIR /var/www

# Copy PHP configuration
COPY docker/php/conf.d/custom.ini /usr/local/etc/php/conf.d/

# Copy composer files first
COPY composer.json composer.lock ./

# Install dependencies
RUN composer update --no-interaction --no-dev --optimize-autoloader

# Change ownership of our applications
RUN find /var/www -path /var/www/client -prune -o -exec chown www-data:www-data {} +

# Expose port 9000
EXPOSE 9000

# Start PHP-FPM
CMD ["php-fpm"] 