<?php

namespace Api\Controllers;

use App\Core\Controller;
use Api\Core\ApiHandler;

class GameController extends Controller {
    use ApiHandler;
    
    private $gameApiConfig;
    private $userConfig;
    private $validator;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $gameApiContent = file_get_contents(__DIR__ . '/../../data/gameapi.json');
        $replacements = [
            '{APP_URL}' => getenv('APP_URL'),
            '{APP_CLIENT_DOMAIN}' => getenv('APP_CLIENT_DOMAIN')
        ];
        $device = $_GET['device'] ?? '';
        if ($device == 'ios' || $device == 'android') {
            $replacements['{APP_CLIENT_DOMAIN}'] = getenv('MOBILE_CLIENT_DOMAIN');
        }
        $gameApiContent = str_replace(array_keys($replacements), array_values($replacements), $gameApiContent);
        $this->gameApiConfig = json_decode($gameApiContent, true);
        $this->userConfig = require __DIR__ . '/../../config/user.php';
        // Lazy loading - chỉ khởi tạo khi cần
    }

    public function query() {
        return $this->apiEndpoint('game_query', function($validator) {
            // Kiểm tra nếu có user_id và sign thì validate
            $userId = $validator->input('user_id');
            $sign = $validator->input('sign');
            
            $isGm = false;
            $userServers = [];
            
            // Nếu có userId và sign thì kiểm tra và lấy thông tin user
            if (!empty($userId) && !empty($sign)) {
                // Kiểm tra sign
                $expectedSign = $this->generateSign($userId);
                if ($sign !== $expectedSign) {
                    throw new \Exception('Invalid sign');
                }

                // Lấy thông tin user để kiểm tra is_gm
                $user = $this->db->fetch(
                    "SELECT is_gm, last_login_servers FROM game_users WHERE user_id = ?",
                    [$userId]
                );

                if (!$user) {
                    throw new \Exception('User not found');
                }

                $isGm = $user['is_gm'] == 1;
                
                // Lấy thông tin last_login_servers của user
                if ($user['last_login_servers']) {
                    $userServers = json_decode($user['last_login_servers'], true);
                }
            }
            
            // Lấy thông tin server từ database
            $servers = $this->db->getServerInfo($isGm);
            
            // Format lại dữ liệu theo cấu trúc của gameapi.json
            $serverList = [];
            $newestServerId = 0;
            foreach ($servers as $server) {
                $serverId = (int)$server['id'];
                $serverInfo = [
                    'id' => $serverId,
                    'name' => $server['server_name'],
                    'ip' => $server['ip'],
                    'port' => (int)$server['port'],
                    'open_time' => 0,
                    'ahead_time' => 300,
                    'flag' => (int)$server['status'],
                    'fsid' => 1,
                    'avatar' => 1,
                    'server_offset' => 0,
                    'showID' => $serverId,
                    'isRecommend' => false,
                    'role_name' => '',
                    'role_level' => 0
                ];

                // Nếu user đã đăng nhập server này, thêm thông tin role
                if (isset($userServers[$serverId])) {
                    $serverInfo['role_name'] = $userServers[$serverId]['role_name'] ?? '';
                    $serverInfo['role_level'] = (int)($userServers[$serverId]['role_level'] ?? 0);
                }

                // Lưu server mới nhất và set isRecommend
                if ($serverId > $newestServerId) {
                    $newestServerId = $serverId;
                    $serverInfo['isRecommend'] = true;
                }

                $serverList[] = $serverInfo;
            }

            // Giữ nguyên cấu trúc json và chỉ thay đổi server_list
            $this->gameApiConfig['server_info']['server_list'] = $serverList;
            
            // Nếu có server đã đăng nhập thì lấy server đó, không thì lấy server mới nhất
            $lastServerId = 0;
            if (!empty($userServers)) {
                $lastLoginTime = 0;
                foreach ($userServers as $serverId => $serverInfo) {
                    if (isset($serverInfo['time']) && $serverInfo['time'] > $lastLoginTime) {
                        $lastLoginTime = $serverInfo['time'];
                        $lastServerId = $serverId;
                    }
                }
            }
            $this->gameApiConfig['server_info']['last_server'] = $lastServerId > 0 ? $lastServerId : $newestServerId;
            
            // Recommand list lấy server mới nhất
            $this->gameApiConfig['server_info']['recommand_list'] = [$newestServerId];
            
            $this->gameApiConfig['server_info']['server_time'] = time();

            $this->jsonResponse($this->gameApiConfig, 200);
        });
    }

    public function userInfo() {
        return $this->apiEndpoint('game_user_info', function($validator) {
            // Validate required parameters
            $user_id = $validator->input('user_id');
            $sign = $validator->input('sign');
            $server_id = $validator->input('server_id');
            $role_id = $validator->input('role_id');
            $role_name = $validator->input('role_name');
            $role_level = $validator->input('role_level');

            // Kiểm tra sign
            $expectedSign = md5($server_id . $user_id . $role_id . $role_name . $role_level);
            if ($sign !== $expectedSign) {
                throw new \Exception('Invalid sign');
            }

            // Lấy thông tin user
            $user = $this->db->fetch(
                "SELECT last_login_servers FROM game_users WHERE user_id = ?",
                [$user_id]
            );

            if (!$user) {
                throw new \Exception('User not found');
            }

            // Cập nhật last_login_servers
            $lastLoginServers = [];
            if ($user['last_login_servers']) {
                $lastLoginServers = json_decode($user['last_login_servers'], true);
            }

            // Cập nhật thông tin server theo chuẩn server_format
            $serverId = (int)$server_id;
            $currentTime = time();
            
            // Thêm server mới vào đầu danh sách
            $newServerInfo = [
                'role_id' => $role_id,
                'role_name' => $role_name,
                'role_level' => (int)$role_level,
                'time' => $currentTime
            ];

            // Tạo mảng mới để sắp xếp theo thời gian
            $sortedServers = [];
            
            // Thêm server hiện tại vào đầu
            $sortedServers[$serverId] = $newServerInfo;
            
            // Thêm các server cũ theo thời gian giảm dần
            foreach ($lastLoginServers as $sid => $info) {
                if ($sid != $serverId && isset($info['time'])) { // Bỏ qua server hiện tại
                    $sortedServers[$sid] = $info;
                }
            }
            
            // Sắp xếp theo thời gian giảm dần
            uasort($sortedServers, function($a, $b) {
                return $b['time'] - $a['time'];
            });
            
            // Giới hạn chỉ lấy 10 server gần nhất
            $lastLoginServers = array_slice($sortedServers, 0, 10, true);

            // Cập nhật vào database
            $this->db->update(
                'game_users',
                ['last_login_servers' => json_encode($lastLoginServers)],
                'user_id = ?',
                [$user_id]
            );

            $this->jsonResponse([
                'status' => true,
                'message' => 'Server info updated successfully'
            ], 200);
        });
    }
} 