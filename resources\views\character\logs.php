<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg overflow-hidden border border-gray-200">
        <!-- Form tìm kiếm -->
        <div class="p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
            <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4" id="logSearchForm">
                <input type="hidden" name="route" value="character">
                <input type="hidden" name="action" value="logs">
                <input type="hidden" name="tab" id="tabInput" value="<?php echo htmlspecialchars($_GET['tab'] ?? 'item'); ?>">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Role ID</label>
                    <div class="relative">
                        <span class="absolute left-3 top-2.5 text-gray-400"><i class="fas fa-id-badge"></i></span>
                        <input type="number" name="role_id" value="<?php echo htmlspecialchars($_GET['role_id'] ?? ''); ?>"
                               class="pl-9 pr-3 py-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                               placeholder="Role ID">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">log_type</label>
                    <div class="relative">
                        <span class="absolute left-3 top-2.5 text-gray-400"><i class="fas fa-tags"></i></span>
                        <select name="log_type" class="pl-9 pr-3 py-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="">Tất cả</option>
                            <?php 
                            $logTypes = ($tab=='hero') ? ($heroLogTypes ?? []) : ($itemLogTypes ?? []);
                            foreach ($logTypes as $type): ?>
                                <option value="<?php echo htmlspecialchars($type); ?>" <?php if(isset($_GET['log_type']) && $_GET['log_type']==$type) echo 'selected'; ?>><?php echo htmlspecialchars($type); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">action_type</label>
                    <div class="relative">
                        <span class="absolute left-3 top-2.5 text-gray-400"><i class="fas fa-exchange-alt"></i></span>
                        <select name="action_type" class="pl-9 pr-3 py-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="">Tất cả</option>
                            <option value="1" <?php if(isset($_GET['action_type']) && $_GET['action_type']=="1") echo 'selected'; ?>>Nhận (1)</option>
                            <option value="2" <?php if(isset($_GET['action_type']) && $_GET['action_type']=="2") echo 'selected'; ?>>Tiêu hao (2)</option>
                        </select>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">item_id</label>
                    <div class="relative">
                        <span class="absolute left-3 top-2.5 text-gray-400"><i class="fas fa-cube"></i></span>
                        <input type="number" name="item_id" value="<?php echo htmlspecialchars($_GET['item_id'] ?? ''); ?>"
                               class="pl-9 pr-3 py-2 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                               placeholder="item_id">
                    </div>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center">
                        <i class="fas fa-search mr-2"></i>Tìm kiếm
                    </button>
                </div>
            </form>
        </div>

        <!-- Tab điều hướng -->
        <div class="border-b border-gray-200 bg-white">
            <nav class="flex" aria-label="Tabs">
                <button onclick="switchTab('item')" class="tab-btn <?php echo ($tab=='item')?'active-tab':''; ?> w-1/2 py-4 px-1 text-center border-b-4 font-bold text-base transition-all duration-200" data-tab="item">
                    <i class="fas fa-box-open mr-2"></i>Log Item
                </button>
                <button onclick="switchTab('hero')" class="tab-btn <?php echo ($tab=='hero')?'active-tab':''; ?> w-1/2 py-4 px-1 text-center border-b-4 font-bold text-base transition-all duration-200" data-tab="hero">
                    <i class="fas fa-user-shield mr-2"></i>Log Hero
                </button>
            </nav>
        </div>

        <!-- Nút export -->
        <div class="p-4 flex justify-end bg-white">
            <form method="get" action="?route=character&action=logs_export" target="_blank">
                <?php foreach ($_GET as $k=>$v) if($k!=='action') echo '<input type="hidden" name="'.htmlspecialchars($k).'" value="'.htmlspecialchars($v).'">'; ?>
                <input type="hidden" name="action" value="logs_export">
                <input type="hidden" name="tab" value="<?php echo htmlspecialchars($tab); ?>">
                <button type="submit" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 flex items-center"><i class="fas fa-file-excel mr-2"></i>Export CSV</button>
            </form>
        </div>

        <!-- Nội dung tab -->
        <div id="itemLogContent" class="tab-content <?php echo ($tab=='item')?'':'hidden'; ?>">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-blue-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">Thời gian</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">log_type</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">action_type</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">item_id</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">Tên vật phẩm</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">Số lượng</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">Role ID</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (!empty($itemLogs)): ?>
                            <?php foreach ($itemLogs as $log): ?>
                                <tr class="hover:bg-blue-50 transition-all">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('Y-m-d H:i:s', $log['time']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($log['log_type']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <?php if ($log['action_type'] == 1): ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded"><i class="fas fa-arrow-down mr-1"></i>Nhận</span>
                                        <?php elseif ($log['action_type'] == 2): ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded"><i class="fas fa-arrow-up mr-1"></i>Tiêu hao</span>
                                        <?php else: ?>
                                            <span class="inline-block px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-700 rounded"><?php echo htmlspecialchars($log['action_type']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $log['item_id']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-indigo-700">
                                        <?php echo $log['item_name'] ? htmlspecialchars($log['item_name']) : '<span class="text-gray-400">Không rõ</span>'; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo number_format($log['change_num']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $log['uid']; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="7" class="px-6 py-4 text-center text-gray-500">Không có dữ liệu</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="heroLogContent" class="tab-content <?php echo ($tab=='hero')?'':'hidden'; ?>">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-blue-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">Thời gian</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">log_type</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">action_type</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">hero_id</th>
                            <th class="px-6 py-3 text-left text-xs font-bold text-blue-700 uppercase">Role ID</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (!empty($heroLogs)): ?>
                            <?php foreach ($heroLogs as $log): ?>
                                <tr class="hover:bg-blue-50 transition-all">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('Y-m-d H:i:s', $log['time']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($log['log_type']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        <?php if ($log['action_type'] == 1): ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold bg-green-100 text-green-700 rounded"><i class="fas fa-arrow-down mr-1"></i>Nhận</span>
                                        <?php elseif ($log['action_type'] == 2): ?>
                                            <span class="inline-flex items-center px-2 py-1 text-xs font-semibold bg-red-100 text-red-700 rounded"><i class="fas fa-arrow-up mr-1"></i>Tiêu hao</span>
                                        <?php else: ?>
                                            <span class="inline-block px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-700 rounded"><?php echo htmlspecialchars($log['action_type']); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $log['hero_id']; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo $log['uid']; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-gray-500">Không có dữ liệu</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
function switchTab(tabName) {
    document.getElementById('tabInput').value = tabName;
    document.getElementById('logSearchForm').submit();
}
</script>

<style>
.tab-btn {
    transition: all 0.3s ease;
    border-bottom: 4px solid transparent;
    color: #64748b;
    background: none;
}
.tab-btn.active-tab {
    border-bottom: 4px solid #2563eb;
    color: #2563eb;
    background: #f0f6ff;
}
</style>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 