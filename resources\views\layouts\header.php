<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GMTool - Dashboard</title>
    <script src="/public/assets/js/tailwind.min.js"></script>
    <link rel="stylesheet" href="/public/assets/css/all.min.css">
    <link rel="stylesheet" href="/public/assets/css/toastr.min.css">
    <script src="/public/assets/js/toastr.min.js"></script>
    <link rel="stylesheet" href="/public/assets/css/sweetalert2.min.css">
    <script src="/public/assets/js/sweetalert2.min.js"></script>
    <style>
        .sidebar-scroll::-webkit-scrollbar {
            width: 6px;
        }
        .sidebar-scroll::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }
        .sidebar-scroll::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        .module-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 0.5rem;
        }
        .module-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
        }
        .submenu-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 0.5rem;
        }
        .submenu-item.active {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 0.5rem;
        }
        .search-input:focus {
            box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.5);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex flex-col md:flex-row">
        <!-- Mobile Menu Button -->
        <div class="md:hidden bg-gradient-to-r from-indigo-800 to-indigo-900 p-4 flex justify-between items-center shadow-lg">
            <a href="/public/?route=dashboard" class="flex items-center group">
                <h1 class="text-2xl font-bold text-white group-hover:text-indigo-200 transition-colors duration-200">
                    <i class="fas fa-gamepad mr-2"></i>GMTool
                </h1>
            </a>
            <button id="mobileMenuButton" class="text-white hover:text-indigo-200 transition-colors duration-200">
                <i class="fas fa-bars text-2xl"></i>
            </button>
        </div>

        <!-- Sidebar -->
        <div id="sidebar" class="w-64 bg-gradient-to-b from-indigo-800 to-indigo-900 text-white fixed md:relative inset-y-0 left-0 transform -translate-x-full md:translate-x-0 transition duration-300 ease-in-out z-50 shadow-xl">
            <div class="p-4 hidden md:block">
                <a href="/public/?route=dashboard" class="flex items-center group">
                    <h1 class="text-2xl font-bold group-hover:text-indigo-200 transition-colors duration-200">
                        <i class="fas fa-gamepad mr-2"></i>GMTool
                    </h1>
                </a>
            </div>

            <!-- Search Bar -->
            <div class="px-4 py-2">
                <div class="relative">
                    <input type="text" 
                           id="moduleSearch" 
                           class="w-full bg-indigo-700 text-white placeholder-indigo-300 rounded-lg pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-indigo-500 search-input transition-all duration-200"
                           placeholder="Tìm kiếm tính năng...">
                    <i class="fas fa-search absolute left-3 top-3 text-indigo-300"></i>
                </div>
            </div>

            <nav class="mt-4 overflow-y-auto sidebar-scroll" style="max-height: calc(100vh - 180px);">
                <?php
                // Load permissions
                $permissionsFile = __DIR__ . '/../../../config/permissions.php';
                if (!file_exists($permissionsFile)) {
                    die("File permissions.php không tồn tại");
                }
                
                $permissions = require $permissionsFile;
                
                // Kiểm tra cấu trúc permissions
                if (!isset($permissions['roles']) || !isset($permissions['modules'])) {
                    die("Cấu trúc file permissions.php không đúng");
                }
                
                // Lấy role của user
                $userRole = $_SESSION['role'] ?? 'viewer';
                
                // Kiểm tra role có tồn tại không
                if (!isset($permissions['roles'][$userRole])) {
                    die("Role '$userRole' không tồn tại trong permissions.php");
                }
                
                $userPermissions = $permissions['roles'][$userRole]['permissions'];
                $currentRoute = $_GET['route'] ?? '';
                $currentAction = $_GET['action'] ?? '';

                // Nhóm các module theo type
                $moduleGroups = [];
                foreach ($permissions['modules'] as $module => $moduleData) {
                    if (isset($moduleData['type'])) {
                        $type = $moduleData['type'];
                        if (!isset($moduleGroups[$type])) {
                            $moduleGroups[$type] = [];
                        }
                        $moduleGroups[$type][] = $module;
                    }
                }

                // Hiển thị theo type
                foreach ($permissions['module_types'] as $type => $typeData) {
                    if (isset($moduleGroups[$type])) {
                        $hasVisibleModule = false;
                        foreach ($moduleGroups[$type] as $module) {
                            if (isset($permissions['modules'][$module]) && 
                                ($userPermissions === '*' || in_array($module . '.view', $userPermissions))) {
                                $hasVisibleModule = true;
                                break;
                            }
                        }

                        if ($hasVisibleModule) {
                            echo '<div class="px-4 py-2">';
                            echo '<div class="text-xs font-semibold text-indigo-300 uppercase tracking-wider mb-2">' . $typeData['name'] . '</div>';
                            
                            foreach ($moduleGroups[$type] as $module) {
                                if (isset($permissions['modules'][$module])) {
                                    $moduleData = $permissions['modules'][$module];
                                    $hasPermission = $userPermissions === '*' || in_array($module . '.view', $userPermissions);
                                    
                                    if ($hasPermission) {
                                        $isActiveModule = $currentRoute === $module;
                                        $submenuClass = $isActiveModule ? '' : 'hidden';
                                        
                                        echo '<div class="module-item ' . ($isActiveModule ? 'active' : '') . '">';
                                        echo '<button type="button" class="flex items-center w-full text-left text-sm font-medium text-indigo-200 hover:text-white rounded-lg px-2 py-2 transition-colors duration-200" onclick="toggleSubmenu(event, \'' . $module . '\')">';
                                        echo '<i class="fas ' . $moduleData['icon'] . ' mr-2 w-4 text-center"></i>';
                                        echo '<span class="truncate text-sm">' . $moduleData['name'] . '</span>';
                                        echo '<i class="fas fa-chevron-down ml-auto text-xs transition-transform duration-200"></i>';
                                        echo '</button>';
                                        
                                        echo '<div id="submenu-' . $module . '" class="mt-1 space-y-1 pl-4 ' . $submenuClass . '">';
                                        foreach ($moduleData['actions'] as $action => $actionData) {
                                            if (isset($actionData['hidden']) && $actionData['hidden']) continue;
                                            $actionPermission = $module . '.' . $action;
                                            if ($userPermissions === '*' || in_array($actionPermission, $userPermissions)) {
                                                $isActiveAction = $currentRoute === $module && $currentAction === $action;
                                                echo '<a href="/public/?route=' . $module . '&action=' . $action . '"';
                                                echo ' class="flex items-center px-2 py-2 text-sm rounded hover:bg-indigo-700 submenu-item ' . 
                                                     ($isActiveAction ? 'active' : '') . ' transition-colors duration-200">';
                                                echo '<i class="fas ' . $actionData['icon'] . ' mr-2 w-4 text-center"></i>';
                                                echo '<span class="truncate">' . $actionData['name'] . '</span>';
                                                echo '</a>';
                                                
                                                if (isset($actionData['sub_actions']) && !empty($actionData['sub_actions'])) {
                                                    foreach ($actionData['sub_actions'] as $subAction => $subActionData) {
                                                        if (isset($subActionData['hidden']) && $subActionData['hidden']) continue;
                                                        $subActionPermission = $module . '.' . $action . '.' . $subAction;
                                                        if ($userPermissions === '*' || in_array($subActionPermission, $userPermissions)) {
                                                            $isActiveSubAction = $currentRoute === $module && 
                                                                                $currentAction === $action && 
                                                                                (isset($_GET['sub_action']) && $_GET['sub_action'] === $subAction);
                                                            echo '<a href="/public/?route=' . $module . '&action=' . $action . '&sub_action=' . $subAction . '"';
                                                            echo ' class="flex items-center px-2 py-2 text-sm rounded hover:bg-indigo-700 submenu-item ' . 
                                                                 ($isActiveSubAction ? 'active' : '') . ' transition-colors duration-200 pl-8">';
                                                            echo '<i class="fas ' . $subActionData['icon'] . ' mr-2 w-4 text-center"></i>';
                                                            echo '<span class="truncate">' . $subActionData['name'] . '</span>';
                                                            echo '</a>';
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        echo '</div>';
                                        echo '</div>';
                                    }
                                }
                            }
                            echo '</div>';
                        }
                    }
                }
                ?>
            </nav>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1">
            <!-- Top Navigation -->
            <header class="bg-white shadow-lg">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex justify-between h-16">
                        <div class="flex items-center">
                            <h2 class="text-xl font-semibold text-gray-800">
                                <?php 
                                $route = $_GET['route'] ?? 'dashboard';
                                $action = $_GET['action'] ?? 'index';
                                echo ucfirst($route) . ' - ' . ucfirst($action);
                                ?>
                            </h2>
                        </div>
                        <div class="flex items-center">
                            <div class="ml-3 relative">
                                <div class="flex items-center space-x-4">
                                    <span class="text-gray-700 flex items-center">
                                        <i class="fas fa-user-circle text-lg mr-2"></i>
                                        <?php echo htmlspecialchars($_SESSION['username']); ?>
                                    </span>
                                    <a href="/public/?route=auth&action=logout" 
                                       class="text-red-600 hover:text-red-800 transition-colors duration-200 flex items-center">
                                        <i class="fas fa-power-off text-lg mr-2"></i>Đăng xuất
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Page Content -->
            <main class="<?php 
                $viewConfig = $this->getViewConfig();
                echo $viewConfig['container'] . ' mx-auto ' . $viewConfig['padding']; 
            ?>">

<script>
function toggleSubmenu(event, module) {
    event.preventDefault();
    const submenu = document.getElementById('submenu-' + module);
    const icon = event.currentTarget.querySelector('.fa-chevron-down');
    const allSubmenus = document.querySelectorAll('[id^="submenu-"]');
    const allIcons = document.querySelectorAll('.fa-chevron-down');
    
    // Đóng tất cả các submenu khác
    allSubmenus.forEach(menu => {
        if (menu.id !== 'submenu-' + module && !menu.classList.contains('hidden')) {
            menu.classList.add('hidden');
        }
    });
    
    // Reset tất cả các icon khác
    allIcons.forEach(i => {
        if (i !== icon) {
            i.style.transform = 'rotate(0deg)';
        }
    });
    
    // Toggle submenu hiện tại
    if (submenu.classList.contains('hidden')) {
        submenu.classList.remove('hidden');
        icon.style.transform = 'rotate(180deg)';
    } else {
        submenu.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
}

// Tự động bung menu mẹ khi load trang
document.addEventListener('DOMContentLoaded', function() {
    const currentRoute = '<?php echo $currentRoute; ?>';
    if (currentRoute) {
        const submenu = document.getElementById('submenu-' + currentRoute);
        if (submenu) {
            submenu.classList.remove('hidden');
            const icon = submenu.previousElementSibling.querySelector('.fa-chevron-down');
            if (icon) {
                icon.style.transform = 'rotate(180deg)';
            }
        }
    }

    // Xử lý tìm kiếm module
    const searchInput = document.getElementById('moduleSearch');
    const moduleItems = document.querySelectorAll('.module-item');
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        
        moduleItems.forEach(item => {
            const moduleName = item.querySelector('button span').textContent.toLowerCase();
            const isVisible = moduleName.includes(searchTerm);
            item.style.display = isVisible ? 'block' : 'none';
        });
    });
});
</script> 