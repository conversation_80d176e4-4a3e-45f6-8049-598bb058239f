<?php
namespace App\Controllers;

use App\Core\Controller;
use Exception;

class OrderController extends Controller {
    private $orderConfig;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->orderConfig = require __DIR__ . '/../../config/order.php';
    }

    public function index() {
        $this->requireLogin();

        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('order.view')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy tham số tìm kiếm và phân trang
        $search = $_GET['search'] ?? '';
        $page = max(1, intval($_GET['page'] ?? 1));
        $perPage = $this->orderConfig['pagination']['per_page'];
        $offset = ($page - 1) * $perPage;

        // Xây dựng query tìm kiếm
        $where = [];
        $params = [];

        if (!empty($search) && strlen($search) >= $this->orderConfig['search']['min_length']) {
            $searchFields = $this->orderConfig['search']['fields'];
            $searchConditions = [];
            
            foreach ($searchFields as $field) {
                $searchConditions[] = "$field LIKE ?";
                $params[] = "%$search%";
            }
            
            $where[] = '(' . implode(' OR ', $searchConditions) . ')';
        }

        // Xây dựng query đếm tổng số bản ghi
        $countQuery = "SELECT COUNT(*) as total FROM payment_transactions";
        if (!empty($where)) {
            $countQuery .= " WHERE " . implode(' AND ', $where);
        }
        $total = (int)$this->db->fetch($countQuery, $params)['total'];

        // Xây dựng query lấy dữ liệu
        $query = "SELECT * FROM payment_transactions";
        if (!empty($where)) {
            $query .= " WHERE " . implode(' AND ', $where);
        }
        $query .= " ORDER BY created_at DESC";
        
        if ($total > 0) {
            $query .= " LIMIT " . (int)$perPage . " OFFSET " . (int)$offset;
        }
        
        // Lấy danh sách đơn hàng
        $orders = $this->db->fetchAll($query, $params);
    
        // Format dữ liệu
        foreach ($orders as &$order) {
            $order['source'] = $this->orderConfig['display']['sources'][$order['source']] ?? $order['source'];
            $order['created_at'] = date($this->orderConfig['display']['date_format'], strtotime($order['created_at']));
            if ($order['updated_at']) {
                $order['updated_at'] = date($this->orderConfig['display']['date_format'], strtotime($order['updated_at']));
            }
        }
        unset($order);

        // Tính toán phân trang
        $totalPages = max(1, ceil($total / $perPage));
        $page = min($page, $totalPages);
        
        $pagination = [
            'current' => $page,
            'total' => $totalPages,
            'per_page' => $perPage,
            'total_items' => $total,
            'max_links' => $this->orderConfig['pagination']['max_links']
        ];

        // Kiểm tra quyền xem chi tiết
        $canViewDetail = $this->hasPermission('order.view.view_detail');
        $orderConfig = $this->orderConfig;
        require_once __DIR__ . '/../../resources/views/order/index.php';
    }

    public function view_detail() {
        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('order.view.view_detail')) {
            header('Location: /public/?route=auth&action=login');
            exit;
        }

        $orderId = $_GET['id'] ?? '';
        if (empty($orderId)) {
            $_SESSION['error'] = 'Order ID không được để trống';
            header('Location: /public/?route=order');
            exit;
        }

        // Lấy thông tin đơn hàng
        $order = $this->db->fetch(
            "SELECT * FROM payment_transactions WHERE order_id = ?", 
            [$orderId]
        );

        if (!$order) {
            $_SESSION['error'] = 'Đơn hàng không tồn tại';
            header('Location: /public/?route=order');
            exit;
        }

        // Format dữ liệu
        $order['source'] = $this->orderConfig['display']['sources'][$order['source']] ?? $order['source'];
        $order['created_at'] = date($this->orderConfig['display']['date_format'], strtotime($order['created_at']));
        if ($order['updated_at']) {
            $order['updated_at'] = date($this->orderConfig['display']['date_format'], strtotime($order['updated_at']));
        }

        $orderConfig = $this->orderConfig;
        require_once __DIR__ . '/../../resources/views/order/detail.php';
    }

    public function approve() {
        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('order.approve')) {
            header('Location: /public/?route=auth&action=login');
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $orderId = trim($_POST['order_id'] ?? '');
            $status = trim($_POST['status'] ?? '');
            $note = trim($_POST['note'] ?? '');
            
            if (empty($orderId)) {
                $_SESSION['error'] = 'Order ID không được để trống';
                header('Location: /public/?route=order&action=approve');
                exit;
            }

            if (empty($status)) {
                $_SESSION['error'] = 'Trạng thái không được để trống';
                header('Location: /public/?route=order&action=approve');
                exit;
            }

            // Kiểm tra đơn hàng tồn tại
            $order = $this->db->fetch(
                "SELECT * FROM payment_transactions WHERE order_id = ? AND status = 'pending'", 
                [$orderId]
            );

            if (!$order) {
                $_SESSION['error'] = 'Đơn hàng không tồn tại hoặc không thể duyệt';
                header('Location: /public/?route=order&action=approve');
                exit;
            }

            // Cập nhật trạng thái
            $this->db->beginTransaction();
            try {
                $this->db->update('payment_transactions', [
                    'status' => $status,
                    'updated_at' => date('Y-m-d H:i:s'),
                    'msg' => $note
                ], 'order_id = ?', [$orderId]);

                $this->db->commit();

                $this->db->log('order_approve', 'success', [
                    'order_id' => $orderId,
                    'status' => $status,
                    'note' => $note
                ]);
                $_SESSION['success'] = 'Cập nhật trạng thái đơn hàng thành công';
            } catch (Exception $e) {
                $this->db->rollBack();
                $this->db->log('order_approve', 'error', [
                    'order_id' => $orderId,
                    'error' => $e->getMessage()
                ]);
                $_SESSION['error'] = 'Có lỗi xảy ra: ' . $e->getMessage();
            }   
            
            header('Location: /public/?route=order&action=view_detail&id=' . $orderId);
            exit;
        }

        $orderConfig = $this->orderConfig;
        require_once __DIR__ . '/../../resources/views/order/approve.php';
    }

    protected function hasPermission($permission) {
        if (!isset($_SESSION['user_id'])) {
            return false;
        }

        $userRole = $_SESSION['role'] ?? '';
        $roles = $this->config['permissions']['roles'];

        if (!isset($roles[$userRole])) {
            return false;
        }

        $userPermissions = $roles[$userRole]['permissions'];
        
        return $userPermissions === '*' || in_array($permission, $userPermissions);
    }
} 