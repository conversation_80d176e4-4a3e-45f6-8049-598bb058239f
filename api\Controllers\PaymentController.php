<?php

namespace Api\Controllers;

use App\Core\Controller;
use Api\Core\ApiHandler;
use Api\Services\FirebaseService;

class PaymentController extends Controller {
    use ApiHandler;
    
    private $orderConfig;
    private $firebaseService;
    private $validator;

    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->orderConfig = require __DIR__ . '/../../config/order.php';
        // Lazy loading - chỉ khởi tạo khi cần
    }

    /**
     * Get FirebaseService instance (lazy loading)
     */
    private function getFirebaseService(): FirebaseService
    {
        if ($this->firebaseService === null) {
            $this->firebaseService = new FirebaseService();
        }
        return $this->firebaseService;
    }

    /**
     * Kiểm tra đơn hàng tồn tại và trạng thái là pending
     */
    private function checkOrderExists($sdkOrderId) {
        $order = $this->db->fetch(
            "SELECT * FROM payment_transactions WHERE client_order_id = ? AND status = 'pending'",
            [$sdkOrderId]
        );
        return $order;
    }

    /**
     * Cập nhật trạng thái đơn hàng
     */
    private function updateOrderStatus($orderId, $status, $message = null) {
        $updateData = [
            'status' => $status,
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        if ($message !== null) {
            $updateData['msg'] = $message;
        }
        
        return $this->db->update(
            'payment_transactions',
            $updateData,
            'order_id = ?',
            [$orderId]
        );
    }

    public function verifyPurchase() {
        return $this->apiEndpoint('verify_purchase', function($validator) {
            // Validate các tham số bắt buộc
            $sdk_order_id = $validator->input('sdk_order_id');
            $status = $validator->input('status');
            $product_id = $validator->input('product_id');
            $purchase_source = $validator->input('purchase_source');
            $verification_data = $validator->input('verification_data');
            $transaction_date = $validator->input('transaction_date');

            // Kiểm tra đơn hàng đã tồn tại chưa
            $existingOrder = $this->checkOrderExists($sdk_order_id);
            if (!$existingOrder) {
                throw new \Exception('Đơn hàng không tồn tại, hoặc đã được xử lý');
            }

            if ($status !== 'purchased') {
                $this->updateOrderStatus(
                    $existingOrder['order_id'],
                    'error',
                    $msg ?? 'Giao dịch thất bại'
                );
                throw new \Exception('Giao dịch thất bại');
            }

            // Kiểm tra sản phẩm có tồn tại trong config không
            $productId = $product_id;
            if (!isset($this->orderConfig['packages'][$productId])) {
                throw new \Exception('Sản phẩm không tồn tại');
            }

            // Xử lý theo nguồn thanh toán
            $purchaseSource = $purchase_source;
            $verificationData = $verification_data;
            $verificationResult = false;

            if ($purchaseSource === 'google_play') {
                $verificationResult = $this->verifyGooglePlayPurchase(
                    $verificationData, 
                    $productId,
                    $transaction_date
                );
            } else if ($purchaseSource === 'app_store') {
                $verificationResult = $this->verifyAppStorePurchase($verificationData, $productId);
            }

            // Sau khi xác thực thành công, xử lý đơn hàng
            if ($verificationResult) {
                // Cập nhật trạng thái đơn hàng thành công
                $this->updateOrderStatus(
                    $existingOrder['order_id'],
                    'success',
                    json_encode($verification_data)
                );

                $this->successResponse([
                    'order_id' => $existingOrder['order_id'],
                    'client_order_id' => $existingOrder['client_order_id'],
                    'product_info' => $this->orderConfig['packages'][$productId]
                ], 'Xác thực giao dịch thành công');
            } else {
                // Cập nhật trạng thái đơn hàng thành error
                $this->updateOrderStatus(
                    $existingOrder['order_id'],
                    'error',
                    'Xác thực giao dịch thất bại'
                );
                throw new \Exception('Xác thực giao dịch thất bại');
            }
        }, ['auto_log' => true]);
    }

    /**
     * Xác minh giao dịch từ Google Play
     */
    private function verifyGooglePlayPurchase($purchaseToken, $productId, $transactionDate) {
        // Xác thực token với Firebase
        $verifiedData = $this->getFirebaseService()->verifyPlayPurchase($purchaseToken, $productId);
        
        // Kiểm tra trạng thái thanh toán
        if ($verifiedData['purchaseState'] !== 0) { // 0 = Purchased
            throw new \Exception('Trạng thái thanh toán không hợp lệ');
        }

        // Kiểm tra thời gian giao dịch
        $purchaseTime = (int)$verifiedData['purchaseTimeMillis']; // Chuyển về seconds
        $transactionTime = (int)$transactionDate;
        $timeDiff = abs($purchaseTime - $transactionTime);
        
        // Nếu chênh lệch quá 30 phút (1800 giây)
        if ($timeDiff > 1800) {
            throw new \Exception('Thời gian giao dịch không hợp lệ');
        }

        return true;
    }

    /**
     * Xác minh giao dịch từ App Store
     */
    private function verifyAppStorePurchase($receiptData, $productId) {
        try {
            // Implement App Store verification
            $sandboxMode = getenv('APP_DEBUG') === 'true';
            $verifyUrl = $sandboxMode 
                ? 'https://sandbox.itunes.apple.com/verifyReceipt'
                : 'https://buy.itunes.apple.com/verifyReceipt';
            
            $response = file_get_contents($verifyUrl, false, stream_context_create([
                'http' => [
                    'method' => 'POST',
                    'header' => 'Content-Type: application/json',
                    'content' => json_encode(['receipt-data' => $receiptData])
                ]
            ]));

            $result = json_decode($response, true);
            
            if (!$result || $result['status'] !== 0) {
                throw new \Exception('Xác thực App Store thất bại');
            }

            // Kiểm tra thông tin sản phẩm
            $receipt = $result['receipt'];
            $inApp = $receipt['in_app'][0] ?? null;
            
            if (!$inApp || $inApp['product_id'] !== $productId) {
                throw new \Exception('Thông tin sản phẩm không khớp');
            }

            return true;
        } catch (\Exception $e) {
            error_log("App Store verification error: " . $e->getMessage());
            throw $e;
        }
    }
} 