<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h1 class="text-2xl font-bold"><PERSON><PERSON><PERSON>n lý cấu hình hệ thống</h1>
        <a href="/public/?route=config&action=create" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded w-full sm:w-auto text-center">
            <i class="fas fa-plus mr-2"></i>Thêm cấu hình
        </a>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
        <?php echo $_SESSION['success']; unset($_SESSION['success']); ?>
    </div>
    <?php endif; ?>
    <?php if (isset($_SESSION['error'])): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <?php echo $_SESSION['error']; unset($_SESSION['error']); ?>
    </div>
    <?php endif; ?>

    <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Key</th>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loại</th>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mô tả</th>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cập nhật</th>
                        <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php if (empty($configs)): ?>
                    <tr>
                        <td colspan="7" class="px-3 sm:px-6 py-4 text-center text-gray-500">Không có cấu hình nào</td>
                    </tr>
                    <?php else: ?>
                    <?php foreach ($configs as $config): ?>
                    <tr>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $config['id']; ?></td>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($config['config_key']); ?></td>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?php
                            if ($config['type'] === 'boolean') {
                                echo $config['config_value'] == '1' ? 'True' : 'False';
                            } elseif ($config['type'] === 'json') {
                                // Hiển thị json đẹp
                                $json = json_decode($config['config_value'], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    echo '<pre class="whitespace-pre-wrap max-w-xs overflow-x-auto">' . htmlspecialchars(json_encode($json, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
                                } else {
                                    echo htmlspecialchars($config['config_value']);
                                }
                            } else {
                                echo htmlspecialchars($config['config_value']);
                            }
                            ?>
                        </td>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo $typeOptions[$config['type']] ?? $config['type']; ?></td>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo htmlspecialchars($config['description']); ?></td>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900"><?php echo date('d/m/Y H:i', strtotime($config['updated_at'])); ?></td>
                        <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="/public/?route=config&action=edit&id=<?php echo $config['id']; ?>" class="text-blue-600 hover:text-blue-900" title="Sửa"><i class="fas fa-edit"></i></a>
                                <?php if ($config['config_key'] === 'server_status'): ?>
                                    <button onclick="sendServerStatus()" class="text-green-600 hover:text-green-900" title="Gửi tình trạng cho toàn bộ server">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                <?php endif; ?>
                                <a href="/public/?route=config&action=delete&id=<?php echo $config['id']; ?>" class="text-red-600 hover:text-red-900" onclick="return confirm('Bạn có chắc muốn xóa cấu hình này?');" title="Xóa"><i class="fas fa-trash"></i></a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function sendServerStatus() {
    Swal.fire({
        title: 'Gửi tình trạng server',
        html: `
            <div class="text-left">
                <label class="block text-sm font-medium text-gray-700 mb-2">Nội dung thông báo <span class="text-red-600">*</span></label>
                <textarea id="content" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" rows="3" required>Máy chủ bảo trì</textarea>
            </div>
        `,
        showCancelButton: true,
        confirmButtonText: 'Gửi',
        cancelButtonText: 'Hủy',
        focusConfirm: false,
        preConfirm: () => {
            const content = document.getElementById('content').value.trim();
            if (!content) {
                Swal.showValidationMessage('Vui lòng nhập nội dung thông báo');
                return false;
            }
            
            // Kiểm tra độ dài base64
            const base64Content = btoa(unescape(encodeURIComponent(content)));
            if (base64Content.length > 256) {
                Swal.showValidationMessage('Nội dung sau khi mã hóa vượt quá 256 ký tự');
                return false;
            }
            
            return content;
        }
    }).then((result) => {
        if (result.isConfirmed) {
            const content = result.value;
            
            Swal.fire({
                title: 'Đang gửi tình trạng...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch('?route=config&action=sendServerStatus', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `content=${encodeURIComponent(content)}&csrf_token=<?php echo $_SESSION['csrf_token']; ?>`
            })
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    let html = '<div class="text-left">';
                    html += `<p class="mb-4">${data.message}</p>`;
                    html += '<div class="space-y-2">';
                    
                    data.results.forEach(result => {
                        if (result.status) {
                            html += `<div class="flex items-center text-green-600">
                                <i class="fas fa-check-circle mr-2"></i>
                                <span>Server ${result.server_id}: ${result.message}</span>
                            </div>`;
                        } else {
                            html += `<div class="flex items-center text-red-600">
                                <i class="fas fa-times-circle mr-2"></i>
                                <span>Server ${result.server_id}: ${result.message}</span>
                            </div>`;
                            if (result.error) {
                                html += `<div class="ml-6 text-sm text-red-500">${result.error}</div>`;
                            }
                        }
                    });
                    
                    html += '</div></div>';

                    Swal.fire({
                        icon: 'success',
                        title: 'Kết quả gửi tình trạng',
                        html: html,
                        width: '600px'
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Lỗi',
                        text: data.message
                    });
                }
            })
            .catch(error => {
                console.error('Error:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Lỗi',
                    text: 'Có lỗi xảy ra khi gửi tình trạng'
                });
            });
        }
    });
}
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 