<?php
namespace App\Controllers;

use App\Core\Controller;

class MailController extends Controller {
    public function __construct($db, $config) {
        parent::__construct($db, $config);
        $this->initItems();
    }

    public function index() {
        $this->requireLogin();
        
        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('mail.send')) {
            $this->redirectToFirstAccessiblePage();
        }

        // Lấy danh sách server
        $servers = $this->db->getServers(true);
        
        require_once __DIR__ . '/../../resources/views/mail/index.php';
    }

    public function send() {
        $this->requireLogin();
        
        // Kiểm tra quyền truy cập
        if (!$this->hasPermission('mail.send')) {
            $this->jsonResponse(['status' => false, 'message' => 'Bạn không có quyền gửi thư']);
        }

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->jsonResponse(['status' => false, 'message' => '<PERSON><PERSON>ơng thức không hợp lệ']);
        }

        // Lấy dữ liệu từ POST
        $serverIds = $_POST['server_ids'] ?? [];
        $type = $_POST['type'] ?? ''; // 1: người chơi cụ thể, 2: online, 3: toàn server
        $kind = $_POST['kind'] ?? 0; // Loại mail: 0-5
        $roleIds = $_POST['role_ids'] ?? '';
        $items = $_POST['items'] ?? [];
        $quickItems = $_POST['quick_items'] ?? '';
        $subject = $_POST['subject'] ?? '';
        $content = $_POST['content'] ?? '';
        $reason = $_POST['reason'] ?? '';

        // Kiểm tra dữ liệu
        if (empty($serverIds)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng chọn ít nhất một máy chủ']);
        }

        // Kiểm tra nếu type=1 thì chỉ được chọn 1 server
        if ($type == 1 && count($serverIds) > 1) {
            $this->jsonResponse(['status' => false, 'message' => 'Khi gửi cho người chơi cụ thể chỉ được chọn 1 máy chủ']);
        }

        if (empty($subject)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập tiêu đề thư']);
        }

        if (empty($content)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập nội dung thư']);
        }

        if ($type == 1 && empty($roleIds)) {
            $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập ID người chơi']);
        }

        // Kiểm tra loại mail hợp lệ
        if (!in_array($kind, [0, 1, 2, 3, 4, 5])) {
            $this->jsonResponse(['status' => false, 'message' => 'Loại mail không hợp lệ']);
        }

        // Xử lý items từ quick_items nếu có
        if (!empty($quickItems)) {
            $items = [];
            $itemPairs = explode(',', $quickItems);
            foreach ($itemPairs as $index => $pair) {
                $parts = explode(':', $pair);
                if (count($parts) == 2) {
                    $items[$index] = [
                        'id' => trim($parts[0]),
                        'num' => trim($parts[1]),
                        'is_bind' => 0,
                        'invalid_time' => 0,
                        'param' => ''
                    ];
                }
            }
        }

        // Kiểm tra item ID
        if (!empty($items)) {
            // Kiểm tra số lượng item không vượt quá 5
            if (count($items) > 5) {
                $this->jsonResponse(['status' => false, 'message' => 'Số lượng vật phẩm không được vượt quá 5']);
            }

            foreach ($items as $item) {
                if (!empty($item['id'])) {
                    if (!isset($this->allItems[$item['id']])) {
                        $this->jsonResponse(['status' => false, 'message' => 'Vật phẩm ID ' . $item['id'] . ' không tồn tại']);
                    }
                }
            }
        }

        try {
            // Kết nối tới game database
            $connectResults = $this->gameDatabaseManager->connectServers($serverIds);
            $this->checkGameDbConnections($connectResults);
            
            // Chuẩn bị dữ liệu thư
            $mailData = [
                'kind' => $kind, // Sử dụng loại mail từ form
                'is_read' => 0,
                'is_lock' => 0,
                'subject' => $subject,
                'recv_time' => time(),
                'content' => $content
            ];

            // Thêm thông tin vật phẩm
            foreach ($items as $index => $item) {
                if (!empty($item['id']) && !empty($item['num'])) {
                    $itemIndex = $index + 1; // Bắt đầu từ 1
                    $mailData["item_id{$itemIndex}"] = $item['id'];
                    $mailData["item_num{$itemIndex}"] = $item['num'];
                    $mailData["item_is_bind{$itemIndex}"] = $item['is_bind'] ?? 0;
                    $mailData["item_invalid_time{$itemIndex}"] = !empty($item['invalid_time']) ? time() + ($item['invalid_time'] * 3600) : 0;
                    $mailData["item_param{$itemIndex}"] = $item['param'] ?? '';
                }
            }

            $results = [];
            $successCount = 0;
            $errorCount = 0;

            // Gửi thư cho từng server
            foreach ($serverIds as $serverId) {
                $serverResult = [
                    'server_id' => $serverId,
                    'status' => true,
                    'message' => 'Gửi thư thành công',
                    'details' => []
                ];

                // Gửi thư theo loại
                if ($type == 1) { // Gửi cho người chơi cụ thể
                    $roleIds = explode(',', $roleIds);
                    $roleIds = array_map('trim', $roleIds);
                    $roleIds = array_filter($roleIds); // Loại bỏ các giá trị rỗng
                    
                    if (empty($roleIds)) {
                        $this->jsonResponse(['status' => false, 'message' => 'Vui lòng nhập ID người chơi']);
                    }

                    // Kiểm tra sự tồn tại của role IDs
                    $invalidRoleIds = [];
                    $selectRole = $this->gameSqlBuilder->buildSelect('role', 'role_id IN (' . str_repeat('?,', count($roleIds) - 1) . '?)', $roleIds);
                    $result = $this->gameDatabaseManager->executeOnServer($serverId, $selectRole['sql'], $selectRole['params']);
                    
                    if (!$result['success']) {
                        $this->jsonResponse(['status' => false, 'message' => "Server $serverId lỗi: " . $result['error']]);
                    }
                    
                    // Lấy danh sách role IDs tồn tại
                    $existingRoleIds = array_column($result['data'], 'role_id');
                    
                    // Kiểm tra role IDs không tồn tại
                    foreach ($roleIds as $roleId) {
                        if (!in_array($roleId, $existingRoleIds)) {
                            $invalidRoleIds[] = $roleId;
                        }
                    }
                    
                    // Nếu có role ID không tồn tại
                    if (!empty($invalidRoleIds)) {
                        $this->jsonResponse([
                            'status' => false, 
                            'message' => 'Các ID người chơi sau không tồn tại: ' . implode(', ', $invalidRoleIds)
                        ]);
                    }

                    foreach ($roleIds as $roleId) {
                        $mailData['uid'] = $roleId;
                        $insertMail = $this->gameSqlBuilder->buildInsert('systemmail', $mailData);
                        $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertMail['sql'], $insertMail['params']);
                        
                        if (!$result['success']) {
                            $serverResult['status'] = false;
                            $serverResult['message'] = 'Gửi thư thất bại';
                            $serverResult['details'][] = [
                                'role_id' => $roleId,
                                'error' => $result['error']
                            ];
                            $errorCount++;
                        } else {
                            $successCount++;
                        }
                    }
                } else if ($type == 2) { // Gửi cho người chơi online
                    $selectOnlinePlayers = $this->gameSqlBuilder->buildSelect('role', 'is_online = ?', [1]);
                    $onlinePlayers = $this->gameDatabaseManager->executeOnServer($serverId, $selectOnlinePlayers['sql'], $selectOnlinePlayers['params']);
                    if ($onlinePlayers['success']) {
                        foreach ($onlinePlayers['data'] as $player) {
                            $mailData['uid'] = $player['role_id'];
                            $insertMail = $this->gameSqlBuilder->buildInsert('systemmail', $mailData);
                            $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertMail['sql'], $insertMail['params']);
                            
                            if (!$result['success']) {
                                $serverResult['status'] = false;
                                $serverResult['message'] = 'Gửi thư thất bại';
                                $serverResult['details'][] = [
                                    'role_id' => $player['role_id'],
                                    'error' => $result['error']
                                ];
                                $errorCount++;
                            } else {
                                $successCount++;
                            }
                        }
                    } else {
                        $serverResult['status'] = false;
                        $serverResult['message'] = 'Không thể lấy danh sách người chơi online';
                        $serverResult['details'][] = [
                            'error' => $onlinePlayers['error']
                        ];
                        $errorCount++;
                    }
                } else if ($type == 3) { // Gửi cho toàn bộ server
                    $minLevel = $_POST['min_level'] ?? 50;
                    $selectAllPlayers = $this->gameSqlBuilder->buildSelect('role', 'level >= ?', [$minLevel]);
                    $allPlayers = $this->gameDatabaseManager->executeOnServer($serverId, $selectAllPlayers['sql'], $selectAllPlayers['params']);
                    
                    if ($allPlayers['success']) {
                        foreach ($allPlayers['data'] as $player) {
                            $mailData['uid'] = $player['role_id'];
                            $insertMail = $this->gameSqlBuilder->buildInsert('systemmail', $mailData);
                            $result = $this->gameDatabaseManager->executeOnServer($serverId, $insertMail['sql'], $insertMail['params']);
                            
                            if (!$result['success']) {
                                $serverResult['status'] = false;
                                $serverResult['message'] = 'Gửi thư thất bại';
                                $serverResult['details'][] = [
                                    'role_id' => $player['role_id'],
                                    'error' => $result['error']
                                ];
                                $errorCount++;
                            } else {
                                $successCount++;
                            }
                        }
                    } else {
                        $serverResult['status'] = false;
                        $serverResult['message'] = 'Không thể lấy danh sách người chơi';
                        $serverResult['details'][] = [
                            'error' => $allPlayers['error']
                        ];
                        $errorCount++;
                    }
                }

                $results[] = $serverResult;
            }

            // Ghi log
            $this->db->log('mail_send', 'success', [
                'server_ids' => $serverIds,
                'type' => $type,
                'role_ids' => $roleIds,
                'items' => $items,
                'results' => $results
            ]);

            $this->jsonResponse([
                'status' => true,
                'message' => 'Gửi thư thành công',
                'data' => [
                    'success_count' => $successCount,
                    'error_count' => $errorCount,
                    'results' => $results
                ]
            ]);
        } catch (\Exception $e) {
            $this->db->log('mail_send', 'error', [
                'error' => $e->getMessage(),
                'server_ids' => $serverIds,
                'type' => $type,
                'role_ids' => $roleIds
            ]);
            $this->jsonResponse(['status' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
        } finally {
            // Đóng kết nối
            $this->gameDatabaseManager->closeAll();
        }
    }
} 