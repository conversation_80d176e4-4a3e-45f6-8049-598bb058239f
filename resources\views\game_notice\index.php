<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="min-h-screen bg-gray-100">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8 py-4 sm:py-8">
        <!-- Flash Messages -->
        <?php if (isset($_SESSION['success'])): ?>
            <div class="mb-3 sm:mb-4 p-3 sm:p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
                <?php 
                echo $_SESSION['success'];
                unset($_SESSION['success']);
                ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
            <div class="mb-3 sm:mb-4 p-3 sm:p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                <?php 
                echo $_SESSION['error'];
                unset($_SESSION['error']);
                ?>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-[300px,1fr] gap-3 sm:gap-4">
            <!-- Server List -->
            <div class="bg-gradient-to-b from-blue-900 to-indigo-900 text-white p-3 sm:p-4 rounded-lg h-fit">
                <h2 class="text-lg sm:text-xl font-bold mb-3 sm:mb-4 flex items-center">
                    <i class="fas fa-server mr-2"></i>
                    Danh sách máy chủ
                </h2>
                <div class="space-y-2" id="serverList">
                    <?php foreach ($groupedServers as $group): ?>
                        <?php 
                        $hasSelectedServer = false;
                        if (isset($_GET['server_id'])) {
                            foreach ($group['servers'] as $server) {
                                if ($server['id'] == $_GET['server_id']) {
                                    $hasSelectedServer = true;
                                    break;
                                }
                            }
                        }
                        ?>
                        <div class="server-group">
                            <div class="flex items-center justify-between p-2 bg-blue-800/30 hover:bg-blue-800/50 rounded-lg cursor-pointer transition-all duration-300"
                                 onclick="toggleGroup(this)">
                                <div class="flex items-center">
                                    <i class="fas fa-chevron-right mr-2 transition-transform duration-200"></i>
                                    <span class="font-medium">Khu <?php echo $group['start_id']; ?></span>
                                </div>
                                <span class="text-xs text-blue-300">[S<?php echo $group['start_id']; ?>-S<?php echo $group['end_id']; ?>]</span>
                            </div>
                            <div class="pl-4 space-y-1 mt-2 <?php echo $hasSelectedServer ? '' : 'hidden'; ?>">
                                <?php foreach ($group['servers'] as $server): ?>
                                    <?php if (!$server['merged_into']): ?>
                                        <a href="?route=gamenotice&server_id=<?php echo $server['id']; ?>" 
                                           class="block p-2 rounded-lg transition-all duration-300 <?php 
                                               echo isset($_GET['server_id']) && $_GET['server_id'] == $server['id'] 
                                                   ? 'bg-blue-700 text-white' 
                                                   : 'text-blue-200 hover:bg-blue-800/50 hover:text-white';
                                           ?>">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <span class="font-medium">S<?php echo $server['id']; ?></span>
                                                    <span class="ml-2"><?php echo htmlspecialchars($server['name']); ?></span>
                                                </div>
                                                <?php 
                                                    $statusClass = '';
                                                    $statusText = '';
                                                    switch ($server['status']) {
                                                        case 1:
                                                            $statusClass = 'bg-green-500';
                                                            $statusText = 'Mượt';
                                                            break;
                                                        case 2:
                                                            $statusClass = 'bg-yellow-500';
                                                            $statusText = 'Đông';
                                                            break;
                                                        case 5:
                                                            $statusClass = 'bg-red-500';
                                                            $statusText = 'Bảo Trì';
                                                            break;
                                                    }
                                                ?>
                                                <span class="text-xs <?php echo $statusClass; ?> text-white px-2 py-1 rounded-full">
                                                    <?php echo $statusText; ?>
                                                </span>
                                            </div>
                                        </a>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Game Notice Management -->
            <div class="bg-white shadow-xl rounded-lg overflow-hidden">
                <!-- Header -->
                <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                    <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                        <h2 class="text-xl sm:text-2xl font-bold text-gray-900">
                            Quản lý thông báo game
                        </h2>
                        <?php if (isset($_GET['server_id'])): ?>
                            <a href="?route=gamenotice&action=create" class="bg-blue-600 text-white px-3 sm:px-4 py-1.5 sm:py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm sm:text-base">
                                <i class="fas fa-plus mr-1 sm:mr-2"></i>Thêm thông báo
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Main Content Area -->
                <div class="p-3 sm:p-6">
                    <?php if (isset($_GET['server_id'])): ?>
                        <!-- Notices Table -->
                        <div class="overflow-x-auto -mx-3 sm:mx-0">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                        <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nội dung</th>
                                        <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian kết thúc</th>
                                        <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trạng thái</th>
                                        <th class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php if (!empty($notices)): ?>
                                        <?php 
                                        // Sắp xếp thông báo: ưu tiên thông báo đang mở và khẩn cấp
                                        usort($notices, function($a, $b) {
                                            // Ưu tiên thông báo đang mở (is_forbid = 0)
                                            if ($a['is_forbid'] != $b['is_forbid']) {
                                                return $a['is_forbid'] - $b['is_forbid'];
                                            }
                                            // Sau đó ưu tiên thông báo khẩn cấp (is_urgent = 1)
                                            if ($a['is_urgent'] != $b['is_urgent']) {
                                                return $b['is_urgent'] - $a['is_urgent'];
                                            }
                                            // Cuối cùng sắp xếp theo thời gian bắt đầu
                                            return strtotime($b['begin_send_time']) - strtotime($a['begin_send_time']);
                                        });
                                        ?>
                                        <?php foreach ($notices as $notice): ?>
                                            <tr class="hover:bg-gray-50">
                                                <td class="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo $notice['idpublicnotice']; ?>
                                                </td>
                                                <td class="px-3 sm:px-6 py-3 sm:py-4 text-sm text-gray-900">
                                                    <div class="max-w-lg break-words">
                                                        <div class="truncate" title="<?php echo htmlspecialchars($notice['content']); ?>">
                                                            <?php 
                                                            $content = htmlspecialchars($notice['content']);
                                                            echo strlen($content) > 15 ? substr($content, 0, 15) . '...' : $content;
                                                            ?>
                                                        </div>
                                                        <?php if (!empty($notice['link'])): ?>
                                                            <a href="<?php echo $notice['link']; ?>" target="_blank" class="text-blue-600 hover:text-blue-800 ml-2">
                                                                <i class="fas fa-external-link-alt"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                                <td class="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <?php echo date('Y-m-d H:i:s', $notice['end_send_time']); ?>
                                                </td>
                                                <td class="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap">
                                                    <?php if (!$notice['is_forbid']): ?>
                                                        <div class="flex items-center space-x-2">
                                                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800">
                                                                Đang hoạt động
                                                            </span>
                                                        </div>
                                                    <?php else: ?>
                                                        <span class="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800">
                                                            Đã tắt
                                                        </span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-sm text-gray-900">
                                                    <div class="flex space-x-2">
                                                        <button onclick="deleteNotice(<?php echo $notice['idpublicnotice']; ?>)" 
                                                                class="text-red-600 hover:text-red-900" title="Xóa">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                        <button onclick="toggleStatus(<?php echo $notice['idpublicnotice']; ?>, 'is_forbid', <?php echo $notice['is_forbid'] ? '0' : '1'; ?>)" 
                                                                class="text-yellow-600 hover:text-yellow-900" title="<?php echo $notice['is_forbid'] ? 'Bật' : 'Tắt'; ?>">
                                                            <i class="fas fa-power-off"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="5" class="px-3 sm:px-6 py-3 sm:py-4 text-center text-gray-500">Không có dữ liệu</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="px-3 sm:px-6 py-3 sm:py-4 bg-gray-50 border-t border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <?php if ($page > 1): ?>
                                            <a href="?route=gamenotice&server_id=<?php echo $_GET['server_id']; ?>&page=1" 
                                               class="px-2 sm:px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                <i class="fas fa-angle-double-left"></i>
                                            </a>
                                            <a href="?route=gamenotice&server_id=<?php echo $_GET['server_id']; ?>&page=<?php echo $page - 1; ?>" 
                                               class="px-2 sm:px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                <i class="fas fa-angle-left"></i>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                            <a href="?route=gamenotice&server_id=<?php echo $_GET['server_id']; ?>&page=<?php echo $i; ?>" 
                                               class="px-2 sm:px-3 py-1 rounded-md <?php echo $i == $page ? 'bg-blue-600 text-white' : 'border border-gray-300 text-gray-700 hover:bg-gray-50'; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        <?php endfor; ?>
                                        
                                        <?php if ($page < $totalPages): ?>
                                            <a href="?route=gamenotice&server_id=<?php echo $_GET['server_id']; ?>&page=<?php echo $page + 1; ?>" 
                                               class="px-2 sm:px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                <i class="fas fa-angle-right"></i>
                                            </a>
                                            <a href="?route=gamenotice&server_id=<?php echo $_GET['server_id']; ?>&page=<?php echo $totalPages; ?>" 
                                               class="px-2 sm:px-3 py-1 rounded-md border border-gray-300 text-sm font-medium text-gray-700 hover:bg-gray-50">
                                                <i class="fas fa-angle-double-right"></i>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="bg-white rounded-lg shadow-lg p-4 sm:p-8 text-center">
                            <i class="fas fa-bullhorn text-gray-400 text-4xl sm:text-5xl mb-3 sm:mb-4"></i>
                            <h3 class="text-lg sm:text-xl font-semibold text-gray-700 mb-2">Chọn server để xem danh sách thông báo</h3>
                            <p class="text-sm sm:text-base text-gray-500">Vui lòng chọn một server từ danh sách bên trái để xem thông báo game.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Server search functionality
    const serverSearch = document.getElementById('server-search');
    const serverItems = document.querySelectorAll('.server-item');
    
    if (serverSearch) {
        serverSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            serverItems.forEach(item => {
                const label = item.querySelector('label').textContent.toLowerCase();
                const id = item.querySelector('span').textContent.toLowerCase();
                if (label.includes(searchTerm) || id.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Select/Deselect all servers
    const selectAllBtn = document.getElementById('select-all');
    const deselectAllBtn = document.getElementById('deselect-all');
    
    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', function() {
            serverItems.forEach(item => {
                if (item.style.display !== 'none') {
                    item.querySelector('input[type="checkbox"]').checked = true;
                }
            });
        });
    }

    if (deselectAllBtn) {
        deselectAllBtn.addEventListener('click', function() {
            serverItems.forEach(item => {
                if (item.style.display !== 'none') {
                    item.querySelector('input[type="checkbox"]').checked = false;
                }
            });
        });
    }
});

function deleteNotice(id) {
    if (!confirm('Bạn có chắc chắn muốn xóa thông báo này?')) {
        return;
    }

    fetch('?route=gamenotice&action=delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&server_id=<?php echo $_GET['server_id']; ?>&csrf_token=<?php echo $_SESSION['csrf_token']; ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            location.reload();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: data.message || 'Có lỗi xảy ra',
                width: '90vw',
                maxWidth: '400px'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Lỗi',
            text: 'Có lỗi xảy ra',
            width: '90vw',
            maxWidth: '400px'
        });
    });
}

function toggleStatus(id, field, value) {
    fetch('?route=gamenotice&action=toggleStatus', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${id}&field=${field}&value=${value}&server_id=<?php echo $_GET['server_id']; ?>&csrf_token=<?php echo $_SESSION['csrf_token']; ?>`
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            location.reload();
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Lỗi',
                text: data.message || 'Có lỗi xảy ra',
                width: '90vw',
                maxWidth: '400px'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Lỗi',
            text: 'Có lỗi xảy ra',
            width: '90vw',
            maxWidth: '400px'
        });
    });
}

function toggleGroup(header) {
    const content = header.nextElementSibling;
    const icon = header.querySelector('.fa-chevron-right');
    
    if (content.classList.contains('hidden')) {
        content.classList.remove('hidden');
        icon.style.transform = 'rotate(90deg)';
    } else {
        content.classList.add('hidden');
        icon.style.transform = 'rotate(0deg)';
    }
}
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 