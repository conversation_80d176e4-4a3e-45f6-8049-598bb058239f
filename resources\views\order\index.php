<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h3 class="text-lg sm:text-xl font-medium text-gray-900">
                <PERSON>h sách đơn hàng
            </h3>
            <form method="GET" action="/public/?route=order" class="w-full sm:w-auto">
                <input type="hidden" name="route" value="order">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" 
                           name="search" 
                           value="<?= htmlspecialchars($search ?? '') ?>" 
                           placeholder="Tìm kiếm theo Order ID, User ID, Game UID" 
                           class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 text-sm sm:text-base"
                           minlength="<?= $orderConfig['search']['min_length'] ?>">
                </div>
            </form>
        </div>
        <?php if (isset($_SESSION['success'])): ?>
        <div class="mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <?php 
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
        </div>
        <?php endif; ?>

        <?php if (isset($_SESSION['error'])): ?>
        <div class="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
        </div>
        <?php endif; ?>
    </div>

    <div class="border-t border-gray-200">
        <div class="p-4">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Order ID</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">User ID</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Game UID</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Source</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Created At</th>
                            <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Thao tác</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($orders)): ?>
                        <tr>
                            <td colspan="8" class="px-3 sm:px-6 py-4 text-center text-sm text-gray-500">
                                Không có dữ liệu
                            </td>
                        </tr>
                        <?php else: ?>
                            <?php foreach ($orders as $order): ?>
                            <tr>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= htmlspecialchars($order['order_id']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= htmlspecialchars($order['user_id']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= htmlspecialchars($order['game_uid']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= number_format($order['amount'], 0) ?> VNĐ
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <?= htmlspecialchars($order['source']) ?>
                                    </span>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-<?= $orderConfig['display']['status_colors'][$order['status']] ?>-100 text-<?= $orderConfig['display']['status_colors'][$order['status']] ?>-800">
                                        <?= htmlspecialchars(ucfirst($order['status'])) ?>
                                    </span>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= htmlspecialchars($order['created_at']) ?>
                                </td>
                                <td class="px-3 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <div class="flex flex-col sm:flex-row gap-2">
                                        <a href="?route=order&action=view_detail&id=<?= htmlspecialchars($order['order_id']) ?>" 
                                           class="text-indigo-600 hover:text-indigo-900">
                                            <i class="fas fa-eye"></i>
                                            <span class="ml-1">Chi tiết</span>
                                        </a>
                                        <?php if ($order['status'] === 'pending'): ?>
                                        <a href="?route=order&action=approve&id=<?= htmlspecialchars($order['order_id']) ?>" 
                                           class="text-green-600 hover:text-green-900">
                                            <i class="fas fa-check"></i>
                                            <span class="ml-1">Duyệt</span>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <?php if ($pagination['total'] > 1): ?>
            <div class="mt-4 flex flex-col sm:flex-row items-center justify-between gap-4">
                <div class="text-sm text-gray-700">
                    Hiển thị <span class="font-medium"><?= ($offset + 1) ?></span> đến 
                    <span class="font-medium"><?= min($offset + $perPage, $total) ?></span> của 
                    <span class="font-medium"><?= $total ?></span> kết quả
                </div>
                <div class="flex flex-wrap gap-2">
                    <?php if ($pagination['current'] > 1): ?>
                        <a href="?route=order&page=1<?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                        <a href="?route=order&page=<?= ($pagination['current'] - 1) ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    <?php endif; ?>

                    <?php
                    $start = max(1, $pagination['current'] - floor($pagination['max_links'] / 2));
                    $end = min($pagination['total'], $start + $pagination['max_links'] - 1);
                    $start = max(1, $end - $pagination['max_links'] + 1);

                    for ($i = $start; $i <= $end; $i++):
                    ?>
                        <a href="?route=order&page=<?= $i ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md <?= $i == $pagination['current'] ? 'bg-indigo-600 text-white' : 'text-gray-700 bg-white hover:bg-gray-50' ?>">
                            <?= $i ?>
                        </a>
                    <?php endfor; ?>

                    <?php if ($pagination['current'] < $pagination['total']): ?>
                        <a href="?route=order&page=<?= ($pagination['current'] + 1) ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-right"></i>
                        </a>
                        <a href="?route=order&page=<?= $pagination['total'] ?><?= $search ? '&search=' . urlencode($search) : '' ?>" 
                           class="inline-flex items-center px-3 py-1 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 