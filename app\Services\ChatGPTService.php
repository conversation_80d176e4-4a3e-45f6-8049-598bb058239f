<?php
namespace App\Services;

class ChatGPTService {
    private $apiKey;
    private $apiUrl = 'https://api.openai.com/v1/chat/completions';
    
    public function __construct() {
        $this->apiKey = getenv('CHATGPT_API_KEY');
        if (!$this->apiKey) {
            throw new \Exception('ChatGPT API key không được cấu hình');
        }
    }
    
    public function processQuery($query) {
        $prompt = "Bạn là một trợ lý AI thông minh. Hãy phân tích câu hỏi sau và quyết định xem nó thuộc loại nào:\n\n";
        $prompt .= "Câu hỏi: " . $query . "\n\n";
        $prompt .= "Cấu trúc bảng payment_transactions:\n";
        $prompt .= "- id: INT (PRIMARY KEY)\n";
        $prompt .= "- order_id: VARCHAR(200) (UNIQUE)\n";
        $prompt .= "- client_order_id: VARCHAR(200)\n";
        $prompt .= "- user_id: VARCHAR(50)\n";
        $prompt .= "- game_uid: VARCHAR(50)\n";
        $prompt .= "- package_id: VARCHAR(50)\n";
        $prompt .= "- package_count: INT\n";
        $prompt .= "- server_id: VARCHAR(50)\n";
        $prompt .= "- amount: INT (số tiền của đơn hàng)\n";
        $prompt .= "- source: ENUM('ios', 'android', 'web', 'facebook', 'google', 'guest')\n";
        $prompt .= "- status: ENUM('pending', 'success', 'error')\n";
        $prompt .= "- msg: TEXT\n";
        $prompt .= "- created_at: TIMESTAMP\n";
        $prompt .= "- updated_at: TIMESTAMP\n\n";
        $prompt .= "Các loại câu hỏi có thể:\n";
        $prompt .= "1. chat: Câu hỏi thông thường, không liên quan đến dữ liệu\n";
        $prompt .= "2. sql: Câu hỏi cần truy vấn dữ liệu từ bảng payment_transactions\n\n";
        $prompt .= "Yêu cầu:\n";
        $prompt .= "1. Phân tích câu hỏi và xác định loại phù hợp\n";
        $prompt .= "2. Nếu là loại 'chat', trả lời câu hỏi một cách tự nhiên\n";
        $prompt .= "3. Nếu là loại 'sql', tạo câu lệnh SQL để truy vấn dữ liệu\n";
        $prompt .= "4. Trả về kết quả theo định dạng JSON sau:\n";
        $prompt .= "{\n";
        $prompt .= "  \"type\": \"chat\" hoặc \"sql\",\n";
        $prompt .= "  \"content\": \"Nội dung trả lời hoặc câu SQL\"\n";
        $prompt .= "}\n\n";
        $prompt .= "Lưu ý khi tạo SQL:\n";
        $prompt .= "- CHỈ sử dụng câu lệnh SELECT\n";
        $prompt .= "- Không sử dụng các câu lệnh khác như INSERT, UPDATE, DELETE\n";
        $prompt .= "- Chỉ lọc theo các trường chính: created_at, status, source\n";
        $prompt .= "- Không giới hạn số lượng kết quả trả về (không dùng LIMIT)\n";
        $prompt .= "- Không lọc quá chi tiết (ví dụ: không lọc theo user_id, game_uid, server_id)\n";
        $prompt .= "- Nếu cần thống kê, sử dụng GROUP BY với các trường chính\n";
        $prompt .= "- Khi truy vấn theo thời gian, sử dụng created_at\n";
        $prompt .= "- Khi tính tổng tiền, sử dụng SUM(amount)\n";
        $prompt .= "- Khi đếm số lượng, sử dụng COUNT(*)\n";
        $prompt .= "- Khi tính trung bình, sử dụng AVG(amount)\n";
        $prompt .= "- Chỉ sử dụng các giá trị ENUM đã được định nghĩa cho source và status\n";
        $prompt .= "- Đảm bảo SQL hợp lệ và an toàn\n";
        $prompt .= "- Phần lớn các yêu cầu đều là yêu cầu truy vấn liên quan đến dữ liệu\n";
        $prompt .= "- Khi có câu hỏi về doanh thu, thống kê, số lượng đơn hàng, v.v. thì luôn ưu tiên tạo SQL để truy vấn dữ liệu\n";
        $prompt .= "- Đối với câu hỏi về doanh thu:\n";
        $prompt .= "  + Nếu hỏi doanh thu hôm nay: SELECT SUM(amount) as total_revenue FROM payment_transactions WHERE DATE(created_at) = CURDATE() AND status = 'success'\n";
        $prompt .= "  + Nếu hỏi doanh thu theo ngày: SELECT DATE(created_at) as date, SUM(amount) as total_revenue FROM payment_transactions WHERE status = 'success' GROUP BY DATE(created_at)\n";
        $prompt .= "  + Nếu hỏi doanh thu theo nguồn: SELECT source, SUM(amount) as total_revenue FROM payment_transactions WHERE status = 'success' GROUP BY source\n";
        
        $response = $this->callAPI($prompt);
        
        try {
            $result = json_decode($response, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Không thể phân tích câu trả lời từ ChatGPT');
            }
            
            if (!isset($result['type']) || !isset($result['content'])) {
                throw new \Exception('Định dạng câu trả lời không hợp lệ');
            }
            
            return $result;
        } catch (\Exception $e) {
            // Nếu không thể parse JSON, coi như là câu trả lời chat
            return [
                'type' => 'chat',
                'content' => $response
            ];
        }
    }
    
    public function analyzeResults($results, $query) {
        $prompt = "Bạn là một chuyên gia phân tích dữ liệu. Hãy phân tích kết quả sau và trả lời câu hỏi:\n\n";
        $prompt .= "Câu hỏi: " . $query . "\n\n";
        $prompt .= "Dữ liệu: " . json_encode($results) . "\n\n";
        $prompt .= "Yêu cầu:\n";
        $prompt .= "1. Phân tích theo cấu trúc rõ ràng, sử dụng markdown để định dạng\n";
        $prompt .= "2. Sử dụng các heading (##) để chia thành các phần\n";
        $prompt .= "3. Sử dụng bullet points (-) cho các điểm chính\n";
        $prompt .= "4. Sử dụng code blocks (```) cho các số liệu quan trọng\n";
        $prompt .= "5. Đưa ra các insights hữu ích và đề xuất nếu có\n";
        
        return $this->callAPI($prompt);
    }
    
    private function callAPI($prompt) {
        $ch = curl_init();
        
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $data = [
            'model' => 'gpt-3.5-turbo',
            'messages' => [
                ['role' => 'system', 'content' => 'Bạn là một trợ lý AI chuyên về phân tích dữ liệu và SQL.'],
                ['role' => 'user', 'content' => $prompt]
            ],
            'temperature' => 0.7,
            'max_tokens' => 4000
        ];
        
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        
        $response = curl_exec($ch);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new \Exception('Lỗi khi gọi API ChatGPT: ' . $error);
        }
        
        $result = json_decode($response, true);
        if (isset($result['error'])) {
            throw new \Exception('Lỗi từ API ChatGPT: ' . $result['error']['message']);
        }
        
        return $result['choices'][0]['message']['content'];
    }
} 