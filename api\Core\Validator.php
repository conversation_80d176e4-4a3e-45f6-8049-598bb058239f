<?php
namespace Api\Core;

use Api\Exceptions\ValidationException;

class Validator 
{
    private static $instance = null;
    private $input = [];
    private $inputLoaded = false;
    private $config = null; // Store config để access endpoint config
    
    /**
     * Get singleton instance
     */
    public static function getInstance($config = null): self
    {
        if (self::$instance === null) {
            self::$instance = new self();
            // Auto load input data khi khởi tạo
            self::$instance->config = $config;
            self::$instance->loadInputData();
        }
        return self::$instance;
    }
    /**
     * Lấy dữ liệu input an toàn (lazy loading)
     */
    private function loadInputData(): void 
    {
        if ($this->inputLoaded) {
            return; // Đã load rồi, không load lại
        }
        
        // Kiểm tra nếu là GET request
        if ($this->config && isset($this->config['method']) && strtoupper($this->config['method']) === 'GET') {
            $this->input = $_GET ?: [];
            $this->inputLoaded = true;
            return;
        }
        
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        
        if (strpos($contentType, 'application/json') !== false) {
            $json = file_get_contents('php://input');
            $data = json_decode($json, true);
            $this->input = $data ?: [];
        } else {
            $this->input = $_POST ?: [];
        }
        
        $this->inputLoaded = true;
    }
    
    /**
     * Validate dữ liệu với rules
     */
    public function validate(array $rules): array 
    {
        $errors = [];
        $validData = [];
        
        foreach ($rules as $field => $rule) {
            $value = $this->input[$field] ?? null;
            $fieldRules = explode('|', $rule);
            
            foreach ($fieldRules as $singleRule) {
                $result = $this->validateField($field, $value, $singleRule);
                if ($result !== true) {
                    $errors[$field][] = $result;
                    break; // Dừng validate field này nếu có lỗi
                }
            }
            
            if (!isset($errors[$field])) {
                $validData[$field] = $this->sanitizeValue($value, $fieldRules);
            }
        }
        
        if (!empty($errors)) {
            throw new ValidationException($errors);
        }
        
        return $validData;
    }
    
    /**
     * Validate với custom messages
     */
    public function validateWithMessages(array $rules, array $messages): array 
    {
        $errors = [];
        $validData = [];
        
        foreach ($rules as $field => $rule) {
            $value = $this->input[$field] ?? null;
            $fieldRules = explode('|', $rule);
            
            foreach ($fieldRules as $singleRule) {
                $result = $this->validateField($field, $value, $singleRule);
                if ($result !== true) {
                    $messageKey = $field . '.' . $singleRule;
                    $errors[$field][] = $messages[$messageKey] ?? $result;
                    break;
                }
            }
            
            if (!isset($errors[$field])) {
                $validData[$field] = $this->sanitizeValue($value, $fieldRules);
            }
        }
        
        if (!empty($errors)) {
            throw new ValidationException($errors);
        }
        
        return $validData;
    }
    
    /**
     * Validate một field với rule
     */
    private function validateField(string $field, $value, string $rule)
    {
        if (strpos($rule, ':') !== false) {
            [$ruleName, $parameter] = explode(':', $rule, 2);
        } else {
            $ruleName = $rule;
            $parameter = null;
        }
        
        switch ($ruleName) {
            case 'required':
                if (empty($value) && $value !== '0') {
                    return "Trường {$field} là bắt buộc";
                }
                break;
                
            case 'min':
                if (strlen($value) < intval($parameter)) {
                    return "Trường {$field} phải có ít nhất {$parameter} ký tự";
                }
                break;
                
            case 'max':
                if (strlen($value) > intval($parameter)) {
                    return "Trường {$field} không được vượt quá {$parameter} ký tự";
                }
                break;
                
            case 'email':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    return "Trường {$field} phải là email hợp lệ";
                }
                break;
                
            case 'phone':
                if (!empty($value) && !preg_match('/^[\+]?[0-9]{10,15}$/', $value)) {
                    return "Trường {$field} phải là số điện thoại hợp lệ";
                }
                break;
                
            case 'username':
                if (!empty($value) && !preg_match('/^[a-zA-Z0-9_]{3,20}$/', $value)) {
                    return "Trường {$field} chỉ được chứa chữ cái, số và dấu gạch dưới, từ 3-20 ký tự";
                }
                break;
                
            case 'password':
                if (!empty($value) && (strlen($value) < 6 || strlen($value) > 30)) {
                    return "Trường {$field} phải có độ dài từ 6-30 ký tự";
                }
                break;
                
            case 'numeric':
                if (!empty($value) && !is_numeric($value)) {
                    return "Trường {$field} phải là số";
                }
                break;
                
            case 'integer':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_INT)) {
                    return "Trường {$field} phải là số nguyên";
                }
                break;
                
            case 'in':
                if (!empty($value)) {
                    $validValues = explode(',', $parameter);
                    if (!in_array($value, $validValues)) {
                        return "Trường {$field} phải là một trong: " . implode(', ', $validValues);
                    }
                }
                break;
                
            case 'url':
                if (!empty($value) && !filter_var($value, FILTER_VALIDATE_URL)) {
                    return "Trường {$field} phải là URL hợp lệ";
                }
                break;
                
            case 'json':
                if (!empty($value)) {
                    // Nếu đã là array/object thì OK (đã được decode)
                    if (is_array($value) || is_object($value)) {
                        return true;
                    }
                    
                    // Nếu là string thì validate JSON
                    if (is_string($value)) {
                        json_decode($value);
                        if (json_last_error() !== JSON_ERROR_NONE) {
                            return "Trường {$field} phải là JSON hợp lệ";
                        }
                    } else {
                        return "Trường {$field} phải là JSON hợp lệ";
                    }
                }
                break;
                
            case 'boolean':
                if (!empty($value) && !in_array($value, [true, false, 'true', 'false', '1', '0', 1, 0], true)) {
                    return "Trường {$field} phải là giá trị boolean (true/false/1/0)";
                }
                break;
        }
        
        return true;
    }
    
    /**
     * Làm sạch giá trị
     */
    private function sanitizeValue($value, array $rules) 
    {
        if (is_string($value)) {
            // Trim whitespace
            $value = trim($value);
            
            // Basic XSS protection
            $value = htmlspecialchars($value, ENT_QUOTES, 'UTF-8');
            
            // Nếu là email, chuyển về lowercase
            if (in_array('email', $rules)) {
                $value = strtolower($value);
            }
        }
        
        return $value;
    }
    
    /**
     * Lấy giá trị input
     */
    public function input(string $key, $default = null) 
    {
        return $this->input[$key] ?? $default;
    }
    
    /**
     * Lấy tất cả input có trong config endpoint params
     */
    public function all(string $endpointName = null): array 
    {
        // Nếu không có config, trả về empty array
        if (!$this->config) {
            return [];
        }
        
        // Config hiện tại là endpointConfig, không cần tìm endpoint nữa
        if (empty($this->config['params'])) {
            return []; // Không có params trong config
        }
        
        // Chỉ lấy các field có trong config params
        $result = [];
        foreach ($this->config['params'] as $paramName => $paramConfig) {
            if (isset($this->input[$paramName])) {
                $result[$paramName] = $this->input[$paramName];
            }
        }
        
        return $result;
    }
    
    /**
     * Lấy tất cả input (bao gồm cả fields không có trong config)
     */
    public function getAllInput(): array 
    {
        return $this->input;
    }
    
    /**
     * Lấy IP address (cached)
     */
    private static $cachedIp = null;
    
    public function getIp(): string 
    {
        if (self::$cachedIp !== null) {
            return self::$cachedIp;
        }
        
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    self::$cachedIp = $ip;
                    return $ip;
                }
            }
        }
        
        self::$cachedIp = $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
        return self::$cachedIp;
    }
    
    /**
     * Lấy User Agent (cached)
     */
    private static $cachedUserAgent = null;
    
    public function getUserAgent(): string 
    {
        if (self::$cachedUserAgent === null) {
            self::$cachedUserAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        }
        return self::$cachedUserAgent;
    }
    
    /**
     * Validate parameters dựa trên config của endpoint
     */
    public function validateEndpointParams(string $endpointName): void
    {
        // Check if config has been set
        if (!$this->config) {
            return; // Không có config, skip validation
        }
        
        // Config hiện tại là endpointConfig, không cần tìm endpoint nữa
        if (empty($this->config['params'])) {
            return; // Không có params để validate
        }

        $rules = [];
        
        // Convert config params thành validation rules
        foreach ($this->config['params'] as $paramName => $paramConfig) {
            $rule = [];
            
            // Required check
            if ($paramConfig['required'] ?? false) {
                $rule[] = 'required';
            }
            
            // Type validation
            switch ($paramConfig['type'] ?? 'string') {
                case 'integer':
                    $rule[] = 'integer';
                    break;
                case 'numeric':
                    $rule[] = 'numeric';
                    break;
                case 'boolean':
                    $rule[] = 'boolean';
                    break;
                case 'email':
                    $rule[] = 'email';
                    break;
                case 'phone':
                    $rule[] = 'phone';
                    break;
                case 'username':
                    $rule[] = 'username';
                    break;
                case 'password':
                    $rule[] = 'password';
                    break;
                case 'enum':
                    if (!empty($paramConfig['values'])) {
                        $rule[] = 'in:' . implode(',', $paramConfig['values']);
                    }
                    break;
                case 'json':
                    // Custom validation cho JSON
                    $rule[] = 'json';
                    break;
                case 'string':
                default:
                    // String validation có thể có min/max length
                    if (isset($paramConfig['min'])) {
                        $rule[] = 'min:' . $paramConfig['min'];
                    }
                    if (isset($paramConfig['max'])) {
                        $rule[] = 'max:' . $paramConfig['max'];
                    }
                    break;
            }
            
            if (!empty($rule)) {
                $rules[$paramName] = implode('|', $rule);
            }
        }
        
        // Validate với rules đã tạo - chỉ validate, không return data
        if (!empty($rules)) {
            $this->validate($rules); // Sẽ throw ValidationException nếu fail
        }
    }
} 