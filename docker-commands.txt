# Hướng dẫn sử dụng các lệnh Docker

# 1. Xem trạng thái các container
docker ps                    # Xem các container đang chạy
docker ps -a                 # Xem tất cả container (bao gồm đã dừng)
docker-compose ps           # Xem trạng thái các service trong docker-compose

# 2. Xem logs
docker-compose logs          # Xem logs của tất cả service
docker-compose logs nginx    # Xem logs của service nginx
docker-compose logs db       # Xem logs của service MySQL
docker-compose logs app      # Xem logs của service PHP

# 3. Dừng và khởi động lại service
docker-compose restart nginx # Khởi động lại service nginx
docker-compose restart db    # Khởi động lại service MySQL
docker-compose restart app   # Khởi động lại service PHP

# 4. Reset hoàn toàn một service
# Reset nginx:
docker-compose stop nginx    # Dừng service nginx
docker-compose rm nginx      # Xóa container nginx
docker-compose up -d nginx   # Tạo và chạy lại container nginx

# Reset MySQL:
docker-compose stop db       # Dừng service MySQL
docker-compose rm db         # Xóa container MySQL
docker volume rm gmtool_dbdata  # Xóa volume data của MySQL (cẩn thận, sẽ mất dữ liệu)
docker-compose up -d db      # Tạo và chạy lại container MySQL

# 5. Reset toàn bộ hệ thống
docker-compose down          # Dừng và xóa tất cả container
docker-compose down -v       # Dừng, xóa container và xóa volumes
docker-compose build         # Build lại images
docker-compose up -d         # Khởi động lại tất cả service

# 6. Truy cập vào container
docker-compose exec nginx sh     # Truy cập vào container nginx
docker-compose exec db bash      # Truy cập vào container MySQL
docker-compose exec app bash     # Truy cập vào container PHP

# 7. Kiểm tra kết nối mạng
docker network ls              # Xem danh sách network
docker network inspect gmtool-network  # Xem chi tiết network

# 8. Xóa tất cả container và images
docker-compose down -v --rmi all  # Xóa container, volumes và images

# 9. Cập nhật cấu hình
# Khi sửa file cấu hình (my.cnf, nginx/conf.d):
docker-compose restart nginx    # Khởi động lại Nginx sau khi sửa nginx/conf.d
docker-compose restart db       # Khởi động lại MySQL sau khi sửa my.cnf

# Kiểm tra logs sau khi cập nhật cấu hình
docker-compose logs -f nginx    # Xem logs Nginx
docker-compose logs -f db       # Xem logs MySQL

# Lưu ý:
# - Khi reset MySQL, dữ liệu sẽ bị mất nếu không backup
# - Nên backup dữ liệu trước khi reset service
# - Có thể sử dụng docker-compose logs -f để theo dõi logs realtime
# - Để xem chi tiết cấu hình của container: docker inspect <container_id> 