<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            Chỉnh sửa Cài đặt App
        </h3>
    </div>
    <div class="border-t border-gray-200">
        <div class="p-4">
            <?php if (!empty($errors)): ?>
                <div class="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">
                                Đã xảy ra lỗi:
                            </h3>
                            <div class="mt-2 text-sm text-red-700">
                                <ul class="list-disc pl-5 space-y-1">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <form action="/public/?route=appsettings&action=edit&id=<?php echo $appSettings['id']; ?>" method="POST">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="app_id" class="block text-sm font-medium text-gray-700">App ID</label>
                        <input type="number" name="app_id" id="app_id" value="<?php echo $appSettings['app_id']; ?>"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                        <label for="app_secret" class="block text-sm font-medium text-gray-700">App Secret</label>
                        <input type="text" name="app_secret" id="app_secret" value="<?php echo $appSettings['app_secret']; ?>"
                               class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>
                </div>

                <div class="mt-4">
                    <span class="text-sm font-medium text-gray-700">Cài đặt đăng nhập</span>
                    <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="google_login" id="google_login"
                                   <?php echo $appSettings['google_login'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="google_login" class="ml-2 block text-sm text-gray-900">
                                Cho phép đăng nhập Google
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="facebook_login" id="facebook_login"
                                   <?php echo $appSettings['facebook_login'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="facebook_login" class="ml-2 block text-sm text-gray-900">
                                Cho phép đăng nhập Facebook
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="apple_login" id="apple_login"
                                   <?php echo $appSettings['apple_login'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="apple_login" class="ml-2 block text-sm text-gray-900">
                                Cho phép đăng nhập Apple
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="guest_login" id="guest_login"
                                   <?php echo $appSettings['guest_login'] ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="guest_login" class="ml-2 block text-sm text-gray-900">
                                Cho phép đăng nhập khách
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <span class="text-sm font-medium text-gray-700">Cài đặt hệ thống</span>
                    <div class="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="is_active"
                                   <?php echo ($appSettings['is_active'] ?? 1) ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="is_active" class="ml-2 block text-sm text-gray-900">
                                Kích hoạt ứng dụng
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="is_debug" id="is_debug"
                                   <?php echo ($appSettings['is_debug'] ?? 0) ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="is_debug" class="ml-2 block text-sm text-gray-900">
                                Chế độ debug
                            </label>
                        </div>

                        <div class="flex items-center">
                            <input type="checkbox" name="phone_sync" id="phone_sync"
                                   <?php echo ($appSettings['phone_sync'] ?? 0) ? 'checked' : ''; ?>
                                   class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="phone_sync" class="ml-2 block text-sm text-gray-900">
                                Đồng bộ điện thoại
                            </label>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex items-center justify-end">
                    <a href="/public/?route=appsettings&action=view" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 mr-3">
                        Hủy
                    </a>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?>
