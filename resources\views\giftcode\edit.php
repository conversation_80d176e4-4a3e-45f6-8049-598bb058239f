<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
            <i class="fas fa-edit mr-2"></i>Chỉnh sửa nhóm Gift Code
        </h3>
    </div>
    <div class="border-t border-gray-200">
        <div class="p-4">
            <?php if (isset($_SESSION['success'])): ?>
                <div class="mb-4 p-4 rounded-md bg-green-50">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-green-800">
                                <?= htmlspecialchars($_SESSION['success']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                <div class="mb-4 p-4 rounded-md bg-red-50">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-red-800">
                                <?= htmlspecialchars($_SESSION['error']) ?>
                            </p>
                        </div>
                    </div>
                </div>
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <form method="POST" action="?route=giftcode&action=update">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="id" value="<?= $group['id'] ?>">
                
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="group_name" class="block text-sm font-medium text-gray-700">Tên nhóm</label>
                        <input type="text" name="group_name" id="group_name" value="<?= htmlspecialchars($group['group_name']) ?>" required
                               class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>

                    <?php if ($group['type'] === 'single'): ?>
                    <div>
                        <label for="code_prefix" class="block text-sm font-medium text-gray-700">Tiền tố code</label>
                        <input type="text" name="code_prefix" id="code_prefix" value="<?= htmlspecialchars($group['code_prefix']) ?>" required
                               class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <?php else: ?>
                    <div>
                        <label for="code_value" class="block text-sm font-medium text-gray-700">Code chung</label>
                        <input type="text" name="code_value" id="code_value" value="<?= htmlspecialchars($group['code_value']) ?>" required
                               class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="max_use" class="block text-sm font-medium text-gray-700">Số lần sử dụng tối đa</label>
                        <input type="number" name="max_use" id="max_use" value="<?= $group['max_use'] ?>" min="1" required
                               class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    </div>
                    <?php endif; ?>

                    <div>
                        <label for="items" class="block text-sm font-medium text-gray-700">Danh sách item</label>
                        <div class="mt-1 flex rounded-md shadow-sm">
                            <input type="text" name="items" id="items" value="<?= htmlspecialchars($group['items']) ?>" required
                                   class="flex-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            <button type="button" id="open-item-modal" 
                                    class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                <i class="fas fa-search mr-2"></i>Kiểm tra
                            </button>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Định dạng: id:số_lượng,id:số_lượng (ví dụ: 10000:100000000,10001:1000)</p>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Thời hạn sử dụng</label>
                        <div class="mt-2 space-y-4">
                            <div class="flex items-center">
                                <input type="radio" name="expired_type" id="expired_type_unlimited" value="unlimited" 
                                       <?= empty($group['expired_at']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                <label for="expired_type_unlimited" class="ml-3 block text-sm font-medium text-gray-700">
                                    Không giới hạn
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="expired_type" id="expired_type_limited" value="limited"
                                       <?= !empty($group['expired_at']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                <label for="expired_type_limited" class="ml-3 block text-sm font-medium text-gray-700">
                                    Có thời hạn
                                </label>
                            </div>
                            <div id="expired_at_container" class="hidden">
                                <input type="datetime-local" name="expired_at" id="expired_at" 
                                       value="<?= !empty($group['expired_at']) ? date('Y-m-d\TH:i', strtotime($group['expired_at'])) : '' ?>"
                                       class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            </div>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Trạng thái</label>
                        <div class="mt-2 space-y-4">
                            <div class="flex items-center">
                                <input type="radio" name="is_disabled" id="is_disabled_0" value="0" 
                                       <?= empty($group['is_disabled']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                <label for="is_disabled_0" class="ml-3 block text-sm font-medium text-gray-700">
                                    Hoạt động
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input type="radio" name="is_disabled" id="is_disabled_1" value="1"
                                       <?= !empty($group['is_disabled']) ? 'checked' : '' ?>
                                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300">
                                <label for="is_disabled_1" class="ml-3 block text-sm font-medium text-gray-700">
                                    Vô hiệu hóa
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 flex justify-end">
                    <a href="?route=giftcode" class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Hủy
                    </a>
                    <button type="submit" class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Xử lý hiển thị/ẩn trường thời hạn
    document.querySelectorAll('input[name="expired_type"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const container = document.getElementById('expired_at_container');
            if (this.value === 'limited') {
                container.classList.remove('hidden');
            } else {
                container.classList.add('hidden');
            }
        });
    });

    // Hiển thị/ẩn trường thời hạn khi trang load
    document.addEventListener('DOMContentLoaded', function() {
        const container = document.getElementById('expired_at_container');
        if (document.querySelector('input[name="expired_type"]:checked').value === 'limited') {
            container.classList.remove('hidden');
        }
    });
    
    document.getElementById('open-item-modal').onclick = function() {
        let value = this.previousElementSibling.value;
        requestAndShowItemInfo(value);
    };
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 