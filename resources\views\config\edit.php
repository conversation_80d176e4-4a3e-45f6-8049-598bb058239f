<?php require_once __DIR__ . '/../layouts/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">Sửa c<PERSON>u hình</h1>
            <a href="/public/?route=config" class="text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left mr-2"></i>Quay lại
            </a>
        </div>

        <?php if (!empty($errors)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <ul class="list-disc list-inside">
                <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php endif; ?>

        <div class="bg-white shadow-md rounded-lg p-6">
            <form method="POST" action="/public/?route=config&action=edit&id=<?php echo $config['id']; ?>">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <div class="mb-4">
                    <label for="config_key" class="block text-gray-700 text-sm font-bold mb-2">Key</label>
                    <input type="text" id="config_key" name="config_key" value="<?php echo isset($_POST['config_key']) ? htmlspecialchars($_POST['config_key']) : htmlspecialchars($config['config_key']); ?>" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                </div>
                <div class="mb-4">
                    <label for="config_value" class="block text-gray-700 text-sm font-bold mb-2">Value</label>
                    <div id="value-input-wrapper">
                        <input type="text" id="config_value" name="config_value" value="<?php echo isset($_POST['config_value']) ? htmlspecialchars($_POST['config_value']) : htmlspecialchars($config['config_value']); ?>" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                    </div>
                </div>
                <div class="mb-4">
                    <label for="description" class="block text-gray-700 text-sm font-bold mb-2">Mô tả</label>
                    <textarea id="description" name="description" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : htmlspecialchars($config['description']); ?></textarea>
                </div>
                <div class="flex items-center justify-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                        <i class="fas fa-save mr-2"></i>Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    function renderValueInput(type) {
        var wrapper = document.getElementById('value-input-wrapper');
        if (type === 'boolean') {
            wrapper.innerHTML = `<select id=\"config_value\" name=\"config_value\" class=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\" required>
                <option value=\"1\" <?php if((isset($_POST['config_value']) ? $_POST['config_value'] : $config['config_value'])==='1') echo 'selected'; ?>>True</option>
                <option value=\"0\" <?php if((isset($_POST['config_value']) ? $_POST['config_value'] : $config['config_value'])==='0') echo 'selected'; ?>>False</option>
            </select>`;
        } else {
            wrapper.innerHTML = `<input type=\"text\" id=\"config_value\" name=\"config_value\" value=\"<?php echo isset($_POST['config_value']) ? htmlspecialchars($_POST['config_value']) : htmlspecialchars($config['config_value']); ?>\" class=\"shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline\" required>`;
        }
    }
    // Lấy giá trị type hiện tại
    var typeSelect = document.getElementById('type');
    if (!typeSelect) {
        // Nếu chưa có select type thì thêm vào
        var typeOptions = <?php echo json_encode($typeOptions); ?>;
        var currentType = '<?php echo isset($_POST['type']) ? $_POST['type'] : $config['type']; ?>';
        var selectHtml = '<label for="type" class="block text-gray-700 font-bold mb-2">Loại <span class="text-red-500">*</span></label>';
        selectHtml += '<select name="type" id="type" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>';
        for (var key in typeOptions) {
            selectHtml += '<option value="'+key+'"'+(key===currentType?' selected':'')+'>'+typeOptions[key]+'</option>';
        }
        selectHtml += '</select>';
        var div = document.createElement('div');
        div.className = 'mb-4';
        div.innerHTML = selectHtml;
        document.querySelector('form').insertBefore(div, document.querySelector('form').children[3]);
    }
    document.getElementById('type').addEventListener('change', function() {
        renderValueInput(this.value);
    });
    window.addEventListener('DOMContentLoaded', function() {
        renderValueInput(document.getElementById('type').value);
    });
</script>

<?php require_once __DIR__ . '/../layouts/footer.php'; ?> 